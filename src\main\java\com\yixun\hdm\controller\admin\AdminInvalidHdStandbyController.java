package com.yixun.hdm.controller.admin;



import cn.hutool.crypto.SecureUtil;
import com.aliyun.oss.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.InvalidHdStandby;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.InvalidHdStandbyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * (InvalidHdStandby)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-26 16:06:28
 */
@RestController
@RequestMapping("/admin/invalidHdStandby")
public class AdminInvalidHdStandbyController {
    /**
     * 服务对象
     */
    @Resource
    private InvalidHdStandbyService invalidHdStandbyService;

    /**
     * 新增数据
     *
     * @param invalidHdStandby 实体对象
     * @return 新增结果
     */
    @PostMapping("insert")
    public CommonResult<Boolean> insert(@RequestBody InvalidHdStandby invalidHdStandby) {
        String content = invalidHdStandby.getContent();
        if(StringUtils.isNullOrEmpty(content)){
            throw new DataErrorException("内容不能为空");
        }
        //计算content MD5
        String md5 = SecureUtil.md5(content);
        invalidHdStandby.setContentMd5(md5);
        Long count = invalidHdStandbyService.lambdaQuery().eq(InvalidHdStandby::getContentMd5, md5).count();
        if(count == 0){
            invalidHdStandbyService.save(invalidHdStandby);
        }else {
            throw new DataErrorException("已设置为失效");
        }
        return CommonResult.successData(true);
    }
}

