package com.yixun.hdm.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.yixun.api.CorpCenterApi;
import com.yixun.bean.CommonResult;
import com.yixun.bean.in.CorporationApiNamesIn;
import com.yixun.bean.out.CorporationOut;
import com.yixun.hdm.bean.excel.SelfCheckCorpExcelData;
import com.yixun.hdm.bean.out.SelfCheckCorpImportProgressOut;

import com.yixun.hdm.entity.SelfCheckCorp;
import com.yixun.hdm.service.SelfCheckCorpImportService;
import com.yixun.hdm.service.SelfCheckCorpService;
import com.yixun.hdm.service.UserCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 自查企业导入服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Slf4j
@Service
public class SelfCheckCorpImportServiceImpl implements SelfCheckCorpImportService {

    @Autowired
    private SelfCheckCorpService selfCheckCorpService;

    @Autowired
    private UserCenterService userCenterService;

	@Autowired
	private CorpCenterApi corpCenterApi;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    private static final String IMPORT_PROGRESS_KEY_PREFIX = "import_progress:";
    private static final int REDIS_EXPIRE_HOURS = 1;

    @Override
    public String startImport(MultipartFile file, Long projectId) {
        String taskId = IdUtil.simpleUUID();

        // 初始化进度数据
        SelfCheckCorpImportProgressOut progress = new SelfCheckCorpImportProgressOut();
        progress.setTaskId(taskId);
        progress.setStatus("进行中");
        progress.setTotalRows(0);
        progress.setSuccessCount(0);
        progress.setFailCount(0);
        progress.setFailedRowNumbers(new ArrayList<>());
        progress.setFailureReasons(new HashMap<>());
        progress.setProgress(0);

        // 保存初始进度到Redis
        saveProgressToRedis(taskId, progress);

        // 异步执行导入
        SpringUtil.getBean(SelfCheckCorpImportService.class).processImportAsync(file, projectId, taskId);

        return taskId;
    }

    @Override
    public SelfCheckCorpImportProgressOut getImportProgress(String taskId) {
        String key = IMPORT_PROGRESS_KEY_PREFIX + taskId;
        String progressJson = redisTemplate.opsForValue().get(key);

        if (StrUtil.isBlank(progressJson)) {
            return null;
        }

        return JSON.parseObject(progressJson, SelfCheckCorpImportProgressOut.class);
    }

	@Async
	@Override
	public void processImportAsync(MultipartFile file, Long projectId, String taskId) {
        SelfCheckCorpImportProgressOut progress = getImportProgress(taskId);

        try {
            // 解析Excel文件
            List<SelfCheckCorpExcelData> excelDataList = EasyExcel.read(file.getInputStream())
                    .head(SelfCheckCorpExcelData.class)
                    .headRowNumber(3) // 前3行为标题，从第4行开始读取数据
                    .sheet()
                    .doReadSync();

            progress.setTotalRows(excelDataList.size());
            saveProgressToRedis(taskId, progress);

            if (excelDataList.isEmpty()) {
                progress.setStatus("已完成");
                progress.setProgress(100);
                saveProgressToRedis(taskId, progress);
                return;
            }

            // 提取企业名称并去除空格
            List<String> corpNames = excelDataList.stream()
                    .map(data -> StrUtil.trim(data.getCorpName()))
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询企业信息 - 使用 CorporationProjectApi 的 getCorpByProjectId 方法
	        CorporationApiNamesIn corporationApiNamesIn = new CorporationApiNamesIn();
	        corporationApiNamesIn.setNames(corpNames);
	        CommonResult<List<CorporationOut>> byNames = corpCenterApi.getByNames(corporationApiNamesIn);

	        List<CorporationOut> existingCorps = new ArrayList<>();

	        if (byNames != null && byNames.getData() != null) {
		        existingCorps = byNames.getData();
	        }

            Map<String, CorporationOut> corpMap = existingCorps.stream()
                    .collect(Collectors.toMap(CorporationOut::getCorpName, corp -> corp, (k1, k2) -> k1));

            // 处理每一行数据
            for (int i = 0; i < excelDataList.size(); i++) {
                SelfCheckCorpExcelData excelData = excelDataList.get(i);
                String corpName = StrUtil.trim(excelData.getCorpName());
                int rowNumber = i + 4; // Excel行号（从第4行开始）

                try {
                    if (StrUtil.isBlank(corpName)) {
                        // 企业名称为空，标记为失败
                        recordFailure(progress, rowNumber, "企业名称为空");
                        continue;
                    }

                    // 检查是否已存在于自查企业列表中
                    boolean existsInSelfCheck = selfCheckCorpService.lambdaQuery()
                            .eq(SelfCheckCorp::getProjectId, projectId)
                            .eq(SelfCheckCorp::getCorpName, corpName)
                            .exists();

                    if (existsInSelfCheck) {
                        // 已存在，跳过
                        continue;
                    }

                    // 检查企业是否存在于机构管理中
                    CorporationOut corp = corpMap.get(corpName);
                    if (corp == null) {
                        // 企业不存在，标记为失败
                        recordFailure(progress, rowNumber, "企业在机构管理中不存在");
                    } else {
                        // 获取项目信息
                        String projectName = userCenterService.getProject(projectId).getProjectName();

                        // 创建自查企业记录
                        SelfCheckCorp selfCheckCorp = new SelfCheckCorp();
                        selfCheckCorp.setProjectId(projectId);
                        selfCheckCorp.setProjectName(projectName);
                        selfCheckCorp.setCorpId(corp.getId());
                        selfCheckCorp.setCorpName(corpName);
	                    selfCheckCorp.setRegion(corp.getRegion());
	                    selfCheckCorp.setEconomicIndustry(corp.getEconomicIndustry());
                        selfCheckCorp.setPushStatus(0); // 未推送

                        selfCheckCorpService.insert(selfCheckCorp);
                        progress.setSuccessCount(progress.getSuccessCount() + 1);
                    }

                } catch (Exception e) {
                    log.error("处理第{}行数据失败: {}", rowNumber, e.getMessage(), e);
                    recordFailure(progress, rowNumber, "处理异常: " + e.getMessage());
                }

                // 更新进度
                int currentProgress = (int) ((double) (i + 1) / excelDataList.size() * 100);
                progress.setProgress(currentProgress);
                saveProgressToRedis(taskId, progress);
            }

            // 导入完成
            progress.setStatus("已完成");
            progress.setProgress(100);
            saveProgressToRedis(taskId, progress);

        } catch (IOException e) {
            log.error("解析Excel文件失败: {}", e.getMessage(), e);
            progress.setStatus("失败");
            saveProgressToRedis(taskId, progress);
        } catch (Exception e) {
            log.error("导入过程发生异常: {}", e.getMessage(), e);
            progress.setStatus("失败");
            saveProgressToRedis(taskId, progress);
        }
    }

    private void saveProgressToRedis(String taskId, SelfCheckCorpImportProgressOut progress) {
        String key = IMPORT_PROGRESS_KEY_PREFIX + taskId;
        String progressJson = JSON.toJSONString(progress);
        redisTemplate.opsForValue().set(key, progressJson, REDIS_EXPIRE_HOURS, TimeUnit.HOURS);
    }

    /**
     * 记录失败原因
     */
    private void recordFailure(SelfCheckCorpImportProgressOut progress, int rowNumber, String reason) {
        progress.setFailCount(progress.getFailCount() + 1);
        progress.getFailedRowNumbers().add(rowNumber);
        progress.getFailureReasons().put(rowNumber, reason);
    }
}
