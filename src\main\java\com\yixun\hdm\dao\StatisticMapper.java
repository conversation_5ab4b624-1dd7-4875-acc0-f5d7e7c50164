package com.yixun.hdm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.out.HdCorpInfoOut;
import com.yixun.hdm.bean.out.ProjectCorpHdStatisticOut;
import com.yixun.hdm.bean.out.statistic.HdCorporationOut;
import com.yixun.hdm.bean.out.statistic.HdCountOut;
import com.yixun.hdm.bean.out.statistic.HdStatusOut;
import com.yixun.hdm.bean.out.statistic.HdTypeOut;

import java.util.Date;
import java.util.List;

public interface StatisticMapper extends BaseMapper<Void> {

    List<HdCountOut> getMonthlyTotalCount(Long projectId, Date firstMonth);

    List<HdCountOut> getMonthlyDoneCount(Long projectId, Date firstMonth);

    List<HdTypeOut> getHdTypeByProject(Long projectId, Date startDate, Date endDate);

    List<HdStatusOut> getHdStatusByProject(Long projectId, Date startDate, Date endDate);

    int getHdWrongByProject(Long projectId, Date startDate, Date endDate);

    int getHdCountByProject(Long projectId, Date startDate, Date endDate);

    List<HdTypeOut> getHdTierOneTypeByProject(Long projectId, Date startDate, Date endDate);

    List<HdTypeOut> getHdRiskFactorByProject(Long projectId);

    int getHdLevelCountByProject(Long projectId, String level);

    List<HdCorporationOut> getHdCorporationByProject(Long projectId);

    Page<HdCorpInfoOut> getHdCorpInfoPage(Page page, Long projectId, List<Long> corpIds);

    List<HdCorpInfoOut> getHdCorpInfoList(Long projectId, List<Long> corpIds);

    Page<ProjectCorpHdStatisticOut> getProjectCorpHdStatistic(Page page, Long projectId, List<Long> corpIds);

    List<ProjectCorpHdStatisticOut> getProjectCorpHdCount(Long projectId, List<Long> corpIds);
}
