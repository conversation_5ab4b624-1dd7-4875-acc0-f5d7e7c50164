package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 隐患详情表
 * @TableName hdm_hidden_danger_temp
 */
@TableName(value ="hdm_hidden_danger_temp")
@Data
public class HiddenDangerTemp implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 操作人员id
     */
    private Long operatorId;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 公司id
     */
    private Long corporationId;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 隐患状态
     */
    private String status;

    /**
     * 隐患照片（可多图）
     */
    private Object pictures;

    /**
     * gif动图
     */
    private String gifFile;

    /**
     * 隐患描述
     */
    private String description;

    /**
     * 隐患类型
     */
    private String type;

    /**
     * 隐患级别
     */
    private String level;

    /**
     * 一级业务
     */
    private String tierOneBusiness;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件（可多个）
     */
    private Object files;

    /**
     * 依据标准
     */
    private Object standard;

    /**
     * 可能导致后果
     */
    private Object possibleConsequence;

    /**
     * 危害因素分类
     */
    private String riskFactorType;

    /**
     * 风险描述
     */
    private String riskDescription;

    /**
     * 管理追溯
     */
    private String manageSource;

    /**
     * 管理追溯内容
     */
    private String manageSourceContent;

    /**
     * 建议整改措施
     */
    private String rectificationSuggestion;

    /**
     * 整改期限（天）
     */
    private Integer rectifyDeadline;

    /**
     * 持表检查项目id
     */
    private Long checkOptionId;

    /**
     * 是否回访时发现
     */
    private Boolean isFindByCallback;

    /**
     * 是否为错误上报
     */
    private Boolean isWrongSubmit;

    /**
     * 计划安排的审查员
     */
    private String arrangedAuditor;

    /**
     * 计划安排的审查员id
     */
    private Long arrangedAuditorId;

    /**
     * 审查员
     */
    private String auditor;

    /**
     * 审查员id
     */
    private Long auditorId;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 是否需老师修改
     */
    private Boolean isNeedEdit;

    /**
     * 审查意见
     */
    private String auditComment;

    /**
     * 审查时间
     */
    private Date auditTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}