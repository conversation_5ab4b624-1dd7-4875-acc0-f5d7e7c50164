package com.yixun.hdm.controller.gov;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.in.*;
import com.yixun.hdm.bean.out.*;
import com.yixun.hdm.entity.*;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.*;
import com.yixun.hdm.utils.*;
import com.yixun.teacher.api.TaskApi;
import com.yixun.teacher.bean.enums.TaskTypeEnum;
import com.yixun.teacher.bean.in.ApiTaskGetIn;
import com.yixun.teacher.bean.out.TaskOut;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "admin隐患基本情况")
@RestController
@RequestMapping(value = "/gov/hiddenDanger")
public class GovHiddenDangerController {

    @Autowired
    private TaskApi taskApi;

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource
    private RectificationService rectificationService;

    @Resource
    private VerificationService verificationService;

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private HdAuditLogService hdAuditLogService;

    @Autowired
    private HiddenDangerSuggestionService hiddenDangerSuggestionService;

    @ApiOperation(value = "获取企业隐患质量")
    @GetMapping(value = "/getCorpHdQuality")
    public CommonResult<CorpHdQualityOut> getTaskList(Long corpId){
        LambdaQueryWrapper<HiddenDanger> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HiddenDanger::getCorporationId, corpId);
        List<HiddenDanger> list = hiddenDangerService.list(wrapper);
        CorpHdQualityOut out = new CorpHdQualityOut();
        Integer corpScore = HdQualityScoreUtils.calcEvaluateHdScore(list);
        Integer effectiveHdNum = HdQualityScoreUtils.calcEffectiveHdNum(list);
        out.setCorpHdQualityScore(corpScore);
        out.setEffectiveHdNum(effectiveHdNum);
        return CommonResult.successData(out);
    }

    @ApiOperation(value = "获取隐患任务列表")
    @GetMapping(value = "/getTaskList")
    public com.yixun.bean.CommonResult<List<TaskOut>> getTaskList(TaskGetIn taskGetIn, CommonPage commonPage){

        ApiTaskGetIn apiTaskGetIn = new ApiTaskGetIn();
        if (taskGetIn.getProjectId()!=null){
            apiTaskGetIn.setProjectId(taskGetIn.getProjectId());
        }
        if (taskGetIn.getCorporationId()!=null){
            apiTaskGetIn.setCorporationId(taskGetIn.getCorporationId());
        }
        if (taskGetIn.getTurn()!=null){
            apiTaskGetIn.setTurn(taskGetIn.getTurn());
        }
        apiTaskGetIn.setTypes(Arrays.asList(TaskTypeEnum.Survey, TaskTypeEnum.Callback));
        apiTaskGetIn.setPageNum(commonPage.getPageNum());
        apiTaskGetIn.setPageSize(commonPage.getPageSize());

        return taskApi.getTaskPageList(apiTaskGetIn);
    }

//    @RequiredPermission("get:getHiddenDangerList:hiddenDanger")
    @ApiOperation(value = "获取隐患列表")
    @PostMapping(value = "/getHiddenDangerList")
    public CommonResult<List<HiddenDangerOut>> getHiddenDangerList(@RequestBody HiddenDangerListGetIn hiddenDangerListGetIn){

        PageInfo<HiddenDanger> hiddenDangerList = hiddenDangerService.getHiddenDangerPage(hiddenDangerListGetIn);
        PageInfo<HiddenDangerOut> outPage = BeanUtils.copyToOutListPage(hiddenDangerList, HiddenDangerOut.class);
        return CommonResult.successPageData(outPage);
    }

//    @RequiredPermission("get:getHiddenDangerAllList:hiddenDanger")
    @ApiOperation(value = "获取所有隐患列表")
    @PostMapping(value = "/getHiddenDangerAllList")
    public CommonResult<List<HiddenDangerOut>> getHiddenDangerAllList(@RequestBody HiddenDangerListGetIn hiddenDangerListGetIn){

        List<HiddenDanger> hiddenDangerList = hiddenDangerService.getHiddenDangerAllList(hiddenDangerListGetIn);
        List<HiddenDangerOut> outList = BeanUtils.copyToOutList(hiddenDangerList, HiddenDangerOut.class);

        return CommonResult.successData(outList);
    }

//    @RequiredPermission("get:getHiddenDangerDetail:hiddenDanger")
    @ApiOperation(value = "获取隐患详情")
    @GetMapping(value = "/getHiddenDangerDetail/{hiddenDangerId}")
    public CommonResult<HiddenDangerDetailOut> getHiddenDangerDetail(@PathVariable("hiddenDangerId") Long hiddenDangerId){

        HiddenDanger hiddenDanger = hiddenDangerService.getById(hiddenDangerId);
        if (hiddenDanger==null){
            throw new DataErrorException("未找到该隐患");
        }

        HiddenDangerDetailOut out = new HiddenDangerDetailOut();
        BeanUtils.copyProperties(hiddenDanger, out);

        //获取审查记录（最近2次）
        List<HdAuditLog> hdAuditLogs = hdAuditLogService.getByHdId(hiddenDangerId);
        if(hdAuditLogs != null && !hdAuditLogs.isEmpty()){
            out.setHdAuditLog(hdAuditLogs.get(0));
        }

        //整改和验证记录
        List<Rectification> rectificationList = rectificationService.getByHiddenDanger(hiddenDangerId);
        List<RectificationOut> rectificationOutList = BeanUtils.copyToOutList(rectificationList, RectificationOut.class);
        rectificationOutList.forEach(r->r.setType("rectification"));
        List<Verification> verificationList = verificationService.getByHiddenDanger(hiddenDangerId);
        List<VerificationOut> verificationOutsList = BeanUtils.copyToOutList(verificationList, VerificationOut.class);
        verificationOutsList.forEach(r->r.setType("verification"));

        List<HdManageItemsOut> hdManageItems = new ArrayList<>();
        HdSubmitOut hdSubmitOut = new HdSubmitOut();
        BeanUtils.copyProperties(hiddenDanger, hdSubmitOut);
        hdSubmitOut.setType("submit");
        hdSubmitOut.setPictures(hiddenDanger.getPictures());
        hdManageItems.add(hdSubmitOut);
        hdManageItems.addAll(rectificationOutList);
        hdManageItems.addAll(verificationOutsList);
        hdManageItems = hdManageItems.stream()
                .sorted(Comparator.comparing(HdManageItemsOut::getCreateTime)).collect(Collectors.toList());
        out.setHdManageItems(hdManageItems);
        //建议措施
        LambdaQueryWrapper<HiddenDangerSuggestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HiddenDangerSuggestion::getHdId, hiddenDangerId);
        List<HiddenDangerSuggestion> suggestionList = hiddenDangerSuggestionService.list(queryWrapper);
        out.getHiddenDanger().setSuggestionList(suggestionList);

        return CommonResult.successData(out);
    }

}
