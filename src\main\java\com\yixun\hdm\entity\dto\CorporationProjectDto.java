package com.yixun.hdm.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.entity.SurveyInfo;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Map;


@Data
public class CorporationProjectDto {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    private String accessCode;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;
    private String projectName;
    private String corpName;
    private SurveyInfo surveyInfo;
}
