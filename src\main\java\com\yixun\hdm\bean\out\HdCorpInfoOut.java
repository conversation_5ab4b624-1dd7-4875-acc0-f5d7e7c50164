package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HdCorpInfoOut {

    private String ids;
    private String projectName;
    private String corpName;
    private String surveyDate;
    private String callbackDate;
    @ApiModelProperty("调研人员")
    private String surveyExecutor;
    @ApiModelProperty("回访人员")
    private String callbackExecutor;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;
    private int hdCount;
    private int doneCount;
    private float doneRate;
    private int diaoyanCount;
    private int diaoyanDoneCount;
    private float diaoyanDoneRate;
    private int huifangCount;
    private int huifangDoneCount;
    private float huifangDoneRate;
    private int normalCount;
    private int majorCount;
    private int xianchangCount;
    private int guanliCount;
    private int anquanshengchanCount;
    private int zhiyejiankangCount;
    private int qitaCount;

}
