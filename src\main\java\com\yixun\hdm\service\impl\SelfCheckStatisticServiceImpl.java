package com.yixun.hdm.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yixun.hdm.dao.SelfCheckDetailMapper;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.vo.*;
import com.yixun.hdm.service.SelfCheckCorpService;
import com.yixun.hdm.service.SelfCheckDetailService;
import com.yixun.hdm.service.SelfCheckStatisticService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Service
@AllArgsConstructor
public class SelfCheckStatisticServiceImpl implements SelfCheckStatisticService {

	private final SelfCheckCorpService selfCheckCorpService;

	private final SelfCheckDetailMapper selfCheckDetailMapper;

	private final SelfCheckDetailService selfCheckDetailService;

	@Override
	public SelfCheckCorpStatisticVO selfCheckCorpStatistic(String projectId) {
		SelfCheckCorpStatisticVO selfCheckCorpStatisticVO = selfCheckDetailMapper.selectSelfCheckCorpStatistic(projectId);
		selfCheckCorpStatisticVO.setNeedSelfCheckCorp(selfCheckCorpService.countByProjectId(projectId));

		Long needSelfCheckCorp = selfCheckCorpStatisticVO.getNeedSelfCheckCorp();
		Long alreadySelfCheckCorp = selfCheckCorpStatisticVO.getAlreadySelfCheckCorp();
		if (needSelfCheckCorp == 0 || alreadySelfCheckCorp == 0) {
			selfCheckCorpStatisticVO.setSelfCheckCorpRate(new BigDecimal("0.00"));
			return selfCheckCorpStatisticVO;
		}
		BigDecimal rate = new BigDecimal(alreadySelfCheckCorp)
			.multiply(new BigDecimal("100"))
			.divide(new BigDecimal(needSelfCheckCorp), 2, RoundingMode.HALF_UP);
		selfCheckCorpStatisticVO.setSelfCheckCorpRate(rate);
		return selfCheckCorpStatisticVO;
	}

	@SuppressWarnings("DuplicateExpressions")
	@Override
	public SelfCheckSituationStatisticVO selfCheckSituationStatistic(String projectId) {
		SelfCheckSituationStatisticVO selfCheckSituationStatisticVO = selfCheckDetailMapper.totalRecommendSelfCheck(projectId);
		selfCheckSituationStatisticVO.setTotalHiddenDanger(selfCheckDetailMapper.totalHiddenDanger(projectId).getTotalHiddenDanger());

		Long totalRecommendSelfCheck = selfCheckSituationStatisticVO.getTotalRecommendSelfCheck();
		Long totalHiddenDanger = selfCheckSituationStatisticVO.getTotalHiddenDanger();

		if (totalRecommendSelfCheck == 0 || totalHiddenDanger == 0) {
			selfCheckSituationStatisticVO.setHiddenDangerDetectionRate(new BigDecimal("0.00"));
		} else {
			BigDecimal rate = new BigDecimal(totalHiddenDanger)
				.multiply(new BigDecimal("100"))
				.divide(new BigDecimal(totalRecommendSelfCheck), 2, RoundingMode.HALF_UP);
			selfCheckSituationStatisticVO.setHiddenDangerDetectionRate(rate);
		}

		SelfCheckCorpStatisticVO selfCheckCorpStatisticVO = selfCheckDetailMapper.selectSelfCheckCorpStatistic(projectId);
		Long alreadySelfCheckCorp = selfCheckCorpStatisticVO.getAlreadySelfCheckCorp();
//		Long needSelfCheckCorp = selfCheckCorpService.countByProjectId(projectId);
		if (alreadySelfCheckCorp == 0 || totalHiddenDanger == 0) {
			selfCheckSituationStatisticVO.setAverageHiddenDangersNumber(new BigDecimal("0.00"));
		} else {
			BigDecimal average = new BigDecimal(totalHiddenDanger)
//				.multiply(new BigDecimal("100"))
				.divide(new BigDecimal(alreadySelfCheckCorp), 2, RoundingMode.HALF_UP);
			selfCheckSituationStatisticVO.setAverageHiddenDangersNumber(average);
		}
		return selfCheckSituationStatisticVO;
	}

	@Override
	public HiddenDangerRectificationVO hiddenDangerRectification(String projectId) {
		HiddenDangerRectificationVO hiddenDangerRectificationVO = selfCheckDetailMapper.hiddenDangersRectifiedNum(projectId);
		Long hiddenDangersRectifiedNum = hiddenDangerRectificationVO.getHiddenDangersRectifiedNum();
		Long totalHiddenDanger = selfCheckDetailMapper.totalHiddenDanger(projectId).getTotalHiddenDanger();
		if (hiddenDangersRectifiedNum == 0 || totalHiddenDanger == 0) {
			hiddenDangerRectificationVO.setHiddenDangersRectifiedRate(new BigDecimal("0.00"));
		} else {
			BigDecimal rate = new BigDecimal(hiddenDangersRectifiedNum)
				.multiply(new BigDecimal("100"))
				.divide(new BigDecimal(totalHiddenDanger), 2, RoundingMode.HALF_UP);
			hiddenDangerRectificationVO.setHiddenDangersRectifiedRate(rate);
		}
		return hiddenDangerRectificationVO;
	}

	@Override
	public PageInfo<SelfCheckResult> corpSelfCheckStatistic(SelfCheckQueryParam selfCheckQueryParam) {
		com.github.pagehelper.Page<SelfCheckResult> page = PageHelper.startPage(selfCheckQueryParam.getPageNum(), selfCheckQueryParam.getPageSize());
		selfCheckDetailMapper.corpSelfCheckStatistic(selfCheckQueryParam);
		return page.toPageInfo();
	}
}
