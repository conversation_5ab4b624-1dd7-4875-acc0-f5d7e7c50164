<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yixun.hdm.dao.SelfCheckCorpMapper">

    <resultMap type="com.yixun.hdm.entity.SelfCheckCorp" id="SelfCheckCorpMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="corpId" column="corp_id" jdbcType="INTEGER"/>
        <result property="corpName" column="corp_name" jdbcType="VARCHAR"/>
        <result property="pushStatus" column="push_status" jdbcType="INTEGER"/>
        <result property="lastPushTime" column="last_push_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hidden_danger.self_check_corp(project_idproject_namecorp_idcorp_namepush_statuslast_push_timecreate_timeupdate_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.projectId}#{entity.projectName}#{entity.corpId}#{entity.corpName}#{entity.pushStatus}#{entity.lastPushTime}#{entity.createTime}#{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hidden_danger.self_check_corp(project_idproject_namecorp_idcorp_namepush_statuslast_push_timecreate_timeupdate_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.projectId}#{entity.projectName}#{entity.corpId}#{entity.corpName}#{entity.pushStatus}#{entity.lastPushTime}#{entity.createTime}#{entity.updateTime})
        </foreach>
        on duplicate key update
project_id = values(project_id) project_name = values(project_name) corp_id = values(corp_id) corp_name = values(corp_name) push_status = values(push_status) last_push_time = values(last_push_time) create_time = values(create_time) update_time = values(update_time)     </insert>

</mapper>

