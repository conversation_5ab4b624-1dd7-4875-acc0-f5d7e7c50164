package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.hdm.entity.UserCollect;
import com.yixun.hdm.entity.em.CollectContentType;

import java.util.List;
import java.util.Set;

/**
 * 用户收藏表(UserCollect)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-19 16:10:45
 */
public interface UserCollectService extends IService<UserCollect> {

    Set<Long> getUserCollect(Long userId, CollectContentType type);

    Set<Long> idSet(Long userId, Integer contentType, Integer range);
}

