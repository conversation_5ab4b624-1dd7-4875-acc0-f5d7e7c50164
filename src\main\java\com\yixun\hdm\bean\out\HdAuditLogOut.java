package com.yixun.hdm.bean.out;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class HdAuditLogOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    /**
     * 创建时间
     */
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;

    /**
     * 隐患id
     */
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long hiddenDangerId;

    /**
     * 项目id
     */
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 公司id
     */
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 岗位/工种批注
     */
    @ApiModelProperty("岗位/工种批注")
    private String workCategoryComment;

    /**
     * 岗位/工种
     */
    @ApiModelProperty("岗位/工种")
    private String workCategory;

    /**
     * 作业活动批注
     */
    @ApiModelProperty("作业活动批注")
    private String jobActivityComment;

    /**
     * 作业活动
     */
    @ApiModelProperty("作业活动")
    private String jobActivity;

    /**
     * 设备设施/物料批注
     */
    @ApiModelProperty("设备设施/物料批注")
    private String equipmentComment;

    /**
     * 设备设施/物料
     */
    @ApiModelProperty("设备设施/物料")
    private String equipment;

    /**
     * 隐患区域批注
     */
    @ApiModelProperty("隐患区域批注")
    private String hdRegionComment;

    /**
     * 隐患区域
     */
    @ApiModelProperty("隐患区域")
    private String hdRegion;

    /**
     * 隐患照片（可多图）
     */
    private List pictures;

    /**
     * 隐患照片审查批注
     */
    private String picturesComment;

    /**
     * 专项类别
     */
    private String specialCategory;

    /**
     * 专项类别明细
     */
    private List specialCategoryDetail;

    /**
     * 专项类别审查批注
     */
    private String specialCategoryComment;

    /**
     * 隐患描述
     */
    private String description;

    /**
     * 隐患描述审查批注
     */
    private String descriptionComment;

    /**
     * 隐患类型
     */
    private String type;

    /**
     * 隐患类型审查批注
     */
    private String typeComment;

    /**
     * 隐患级别
     */
    private String level;

    /**
     * 隐患级别审查批注
     */
    private String levelComment;

    /**
     * 备注
     */
    private String remark;

    /**
     * 依据标准
     */
    private List standard;

    /**
     * 依据标准审查批注
     */
    private String standardComment;

    /**
     * 可能导致后果
     */
    private List possibleConsequence;

    /**
     * 可能导致后果审查批注
     */
    private String possibleConsequenceComment;

    /**
     * 危害因素分类
     */
    private String riskFactorType;

    /**
     * 危害因素分类明细
     */
    private List riskFactorTypeDetail;

    /**
     * 危害因素分类审查批注
     */
    private String riskFactorTypeComment;

    /**
     * 所属风险点
     */
    private String riskPointName;

    /**
     * 所属风险点审查批注
     */
    private String riskPointComment;

    /**
     * 建议整改措施
     */
    private String rectificationSuggestion;

    /**
     * 建议整改措施审查批注
     */
    private String rectificationSuggestionComment;

    /**
     * 整改期限（天）
     */
    private Integer rectifyDeadline;

    /**
     * 整改期限审查批注
     */
    private String rectifyDeadlineComment;

    /**
     * 是否回访时发现
     */
    private Boolean isFindByCallback;

    /**
     * 审查提交人
     */
    private String auditSubmitter;

    /**
     * 审查提交人id
     */
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long auditSubmitterId;

    /**
     * 审查员
     */
    private String auditor;

    /**
     * 审查员id
     */
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long auditorId;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 审查意见
     */
    private String auditComment;

    public void setPictures(String pictures) {
        this.pictures = JSON.parseObject(pictures, List.class);
    }

    public void setStandard(String standard) {
        this.standard = JSON.parseObject(standard, List.class);
    }

    public void setPossibleConsequence(String possibleConsequence) {
        this.possibleConsequence = JSON.parseObject(possibleConsequence, List.class);
    }

    public void setSpecialCategoryDetail(String specialCategoryDetail) {
        this.specialCategoryDetail = JSON.parseObject(specialCategoryDetail, List.class);
    }

    public void setRiskFactorTypeDetail(String riskFactorTypeDetail) {
        this.riskFactorTypeDetail = JSON.parseObject(riskFactorTypeDetail, List.class);
    }
}
