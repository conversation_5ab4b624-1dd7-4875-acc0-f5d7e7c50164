package com.yixun.hdm.entity;



import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 隐患建议改善措施(HdmHiddenDangerSuggestion)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-27 10:48:44
 */
@SuppressWarnings("serial")
@Data
@ApiModel("隐患建议改善措施")
@TableName(value ="hdm_hidden_danger_suggestion")
public class HiddenDangerSuggestion extends Model<HiddenDangerSuggestion> {

    /**
    * 主键ID
    */
    @ApiModelProperty("主键ID")
    @NotNull(message="[主键ID]不能为空")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
    * 隐患ID
    */
    @ApiModelProperty("隐患ID")
    private Long hdId;

    /**
    * 类型
    */
    @ApiModelProperty("类型")
    private String type;

    /**
    * 改善措施
    */
    @ApiModelProperty("改善措施")
    private String improvementMeasures;

    /**
    * 跟踪状态，1:已改善，2:未改善
    */
    @ApiModelProperty("跟踪状态，1:已改善，2:未改善")
    private Integer status;

    /**
    * 跟踪时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty("跟踪时间")
    private Date trackingTime;

    /**
    * 企业回复
    */
    @ApiModelProperty("企业回复")
    private String corpResp;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createTime;

    private Date updateTime;
}

