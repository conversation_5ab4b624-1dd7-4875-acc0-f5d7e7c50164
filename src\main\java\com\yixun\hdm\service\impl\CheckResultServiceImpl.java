package com.yixun.hdm.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.bean.common.CodeNameInfo;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.PreciseCheckListIn;
import com.yixun.hdm.bean.out.CheckResultStatisticsOut;
import com.yixun.hdm.dao.CheckResultMapper;
import com.yixun.hdm.entity.CheckResult;
import com.yixun.hdm.entity.dto.CorporationDto;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.CheckResultService;
import com.yixun.hdm.service.UserCenterService;
import com.yixun.hdm.service.biz.AiService;
import com.yixun.hdm.utils.SnGeneratorUtil;
import com.yixun.teacher.api.TaskApi;
import com.yixun.teacher.bean.dto.Accompany;
import com.yixun.teacher.bean.out.TaskOut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class CheckResultServiceImpl extends ServiceImpl<CheckResultMapper, CheckResult>
    implements CheckResultService {

    @Autowired
    private UserCenterService userCenterService;

    @Autowired
    private AiService aiService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private TaskApi taskApi;

    @Override
    public Page<CheckResultStatisticsOut> getStatisticsPage(Long projectId, Long corporationId, Boolean hasHd, CommonPage commonPage) {

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        Page<CheckResultStatisticsOut> statisticsPage = getBaseMapper().getStatisticsPage(page, projectId, corporationId, hasHd);
//        List<CheckResultStatisticsOut> records = statisticsPage.getRecords();
//        for (CheckResultStatisticsOut record : records) {
//            TaskOut taskOut = taskApi.getById(record.getTaskId());
//            if(taskOut != null){
//                record.setExecutor(taskOut.getExecutor());
//                List<Accompany> accompany = taskOut.getAccompany();
//                if(accompany != null && !accompany.isEmpty()){
//                    record.setAccompany(accompany.stream().map(Accompany::getName).collect(Collectors.joining(",")));
//                }
//            }
//        }
        return statisticsPage;
    }

    @Override
    public List<CheckResultStatisticsOut> getStatisticsList(Long projectId, Long corporationId, Boolean hasHd) {

        return getBaseMapper().getStatisticsList(projectId, corporationId, hasHd);
    }

    @Override
    public Page<CheckResult> getCheckedList(Long taskId, Integer result, CommonPage commonPage) {
        QueryWrapper<CheckResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        if (result!=null){
            if(result == -1){
                queryWrapper.isNull("result");
            }else{
                queryWrapper.eq("result", result);
            }
        }

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<CheckResult>) page(page, queryWrapper);
    }


    @Override
    public void syncPreciseCheckList(PreciseCheckListIn in) {
        String key = "syncPreciseCheckList:" + in.getTaskId();
        try {
            TaskOut taskOut = taskApi.getById(in.getTaskId());
            if(taskOut.getId() == null){
                throw new DataErrorException("任务不存在");
            }
            CorporationDto corporation = userCenterService.getCorporation(taskOut.getCorporationId());
            if(corporation == null){
                throw new DataErrorException("公司不存在");
            }
            List<CodeNameInfo> economicIndustry = corporation.getEconomicIndustry();
            if(economicIndustry != null && !economicIndustry.isEmpty()){
                String industry = economicIndustry.stream().map(CodeNameInfo::getName).collect(Collectors.joining("/"));
                in.setIndustry(industry);
            }
            in.setProjectId(taskOut.getProjectId());
            in.setProjectName(taskOut.getProjectName());
            in.setCorpId(taskOut.getCorporationId());
            in.setCorpName(taskOut.getCorpName());
            Boolean res = stringRedisTemplate.opsForValue().setIfAbsent(key, "1", 10, TimeUnit.MINUTES);
            if (Boolean.TRUE.equals(res)) {
                // 同步数据
                LambdaQueryWrapper<CheckResult> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(CheckResult::getProjectId, in.getProjectId());
                wrapper.eq(CheckResult::getCorporationId, in.getCorpId());
                wrapper.eq(CheckResult::getTaskId, in.getTaskId());
                wrapper.isNotNull(CheckResult::getRecommendHdId);
                List<CheckResult> list = list(wrapper);
                if (!list.isEmpty() && !in.getRefresh()) {
                    return;
                }
                List<CheckResult> recommendHd = aiService.getRecommendHd(in);
                recommendHd.forEach(checkResult -> {
                    checkResult.setId(SnGeneratorUtil.getId());
                    checkResult.setTaskId(in.getTaskId());
                    checkResult.setProjectId(in.getProjectId());
                    checkResult.setProjectName(in.getProjectName());
                    checkResult.setCorporationId(in.getCorpId());
                    checkResult.setCorpName(in.getCorpName());
                    checkResult.setExecutor(taskOut.getExecutor());
                    List<Accompany> accompany = taskOut.getAccompany();
                    if(accompany != null && !accompany.isEmpty()){
                        checkResult.setAccompany(accompany.stream().map(Accompany::getName).collect(Collectors.joining(",")));
                    }
                    checkResult.setCreateTime(new Date());
                    checkResult.setCreateTime(new Date());
                });
                SpringUtil.getBean(CheckResultServiceImpl.class).syncRecommendHd(list, recommendHd);
            } else {
                throw new DataErrorException("正在同步精准隐患列表，请稍后再试");
            }
        } catch (Exception e) {
            throw new DataErrorException(e.getMessage());
        } finally {
            stringRedisTemplate.delete(key);
        }

    }

    @Transactional
    public void syncRecommendHd(List<CheckResult> originalList, List<CheckResult> newList){
        List<Long> collect = originalList.stream().filter(item -> item.getResult() == null).map(CheckResult::getId).collect(Collectors.toList());
        if(!collect.isEmpty()){
            removeByIds(collect);
        }
        List<String> haveResult = originalList.stream().filter(item -> item.getResult() != null).map(CheckResult::getRecommendHdId).collect(Collectors.toList());
        List<CheckResult> newSaveList = newList.stream().filter(item -> !haveResult.contains(item.getRecommendHdId())).collect(Collectors.toList());
        if(!newSaveList.isEmpty()){
            saveBatch(newSaveList);
        }
    }
}




