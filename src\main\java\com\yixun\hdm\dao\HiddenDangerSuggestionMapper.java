package com.yixun.hdm.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.yixun.hdm.entity.HiddenDangerSuggestion;

/**
 * 隐患建议改善措施(HiddenDangerSuggestion)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-27 14:06:46
 */
public interface HiddenDangerSuggestionMapper extends BaseMapper<HiddenDangerSuggestion> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<HiddenDangerSuggestion> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<HiddenDangerSuggestion> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<HiddenDangerSuggestion> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<HiddenDangerSuggestion> entities);

}

