package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class UserProjectOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;
    private String realName;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    private String projectName;
}
