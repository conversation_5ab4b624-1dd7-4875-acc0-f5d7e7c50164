package com.yixun.hdm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.constants.RedisKeyConstant;
import com.yixun.hdm.dao.SelfCheckRecordMapper;
import com.yixun.hdm.entity.SelfCheckDetail;
import com.yixun.hdm.entity.SelfCheckRecord;
import com.yixun.hdm.entity.dto.ProjectDto;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.SelfCheckDetailService;
import com.yixun.hdm.service.SelfCheckRecordService;
import com.yixun.hdm.service.UserCenterService;
import com.yixun.hdm.utils.ExcelOutUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 自查记录(SelfCheckRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-26 17:09:51
 */
@Service("selfCheckRecordService")
public class SelfCheckRecordServiceImpl extends ServiceImpl<SelfCheckRecordMapper, SelfCheckRecord> implements SelfCheckRecordService {

    @Autowired
    UserCenterService userCenterService;

    @Autowired
    private SelfCheckDetailService selfCheckDetailService;

    @Autowired
    private HttpServletResponse response;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;


    @Override
    public SelfCheckRecord getRecord(Long projectId, Long corpId, Date date) {
        List<SelfCheckRecord> list = lambdaQuery().eq(SelfCheckRecord::getProjectId, projectId)
                .eq(SelfCheckRecord::getCorpId, corpId)
                .apply("date(create_time)=date({0})", date)
                .orderByDesc(SelfCheckRecord::getCreateTime)
                .list();
        if(list.isEmpty()){
            return null;
        }else{
            return list.get(0);
        }
    }

    @Override
    @Transactional
    public boolean createRecord(Long projectId, String projectName, Long corpId, String corpName, Date date, boolean isForce) {
        SelfCheckRecord record = getRecord(projectId, corpId, date);
        String key = RedisKeyConstant.SELF_CHECK_RECORD_SEQ_KEY + corpId  + ":" + DateUtil.format(date, "yyyyMMdd");
        Long increment = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, 24, TimeUnit.HOURS);
        String seq = String.format("%04d", increment);
        String name = "精准工伤预防推荐" + DateUtil.format(date, "yyyyMMdd") + seq;
        if (record == null) {
            record = new SelfCheckRecord();
            record.setName(name);
            record.setProjectId(projectId);
            record.setProjectName(projectName);
            record.setCorpId(corpId);
            record.setCorpName(corpName);
            // 设置检查期限为创建时间+30天
            record.setCheckDeadline(DateUtil.offsetDay(new Date(), 30));
//            ProjectDto project = userCenterService.getProject(projectId);
//            record.setStartTime(project.getStartDate());
//            record.setEndTime(project.getEndDate());
            save(record);
            //创建检查明细
            selfCheckDetailService.createDetail(record);
        } else {
            if (record.getLastCheckTime() != null) {
                throw new DataErrorException("企业正在检查，无法覆盖推送");
            }
            if (isForce) {
                SelfCheckRecord selfCheckRecord = BeanUtil.copyProperties(record, SelfCheckRecord.class);
                selfCheckRecord.setName(name);
                // 设置检查期限为创建时间+30天
                selfCheckRecord.setCheckDeadline(DateUtil.offsetDay(new Date(), 30));
                removeById(record.getId());
                selfCheckDetailService.lambdaUpdate().eq(SelfCheckDetail::getCheckRecordId,record.getId()).remove();
                selfCheckRecord.setId(null);
                save(selfCheckRecord);
                selfCheckDetailService.createDetail(selfCheckRecord);
            }
        }
        return false;
    }

    @Override
    public Page<SelfCheckRecord> pageList(Page<SelfCheckRecord> page, SelfCheckRecord selfCheckRecord) {
        String name = selfCheckRecord.getName();
        if(StrUtil.isNotBlank(name)){
            selfCheckRecord.setName(null);
        }
        LambdaQueryWrapper<SelfCheckRecord> wrapper = new QueryWrapper<>(selfCheckRecord).lambda();
        wrapper.like(StrUtil.isNotBlank(name), SelfCheckRecord::getName, name);
        wrapper.orderByDesc(SelfCheckRecord::getCreateTime);
        page(page, wrapper);
        for (SelfCheckRecord record : page.getRecords()) {
            List<SelfCheckDetail> list = selfCheckDetailService.lambdaQuery().eq(SelfCheckDetail::getCheckRecordId, record.getId()).list();
            record.setCheckCount(list.size());
            record.setPassCount((int) list.stream().filter(detail -> detail.getResult() == 0).count());
            record.setHdCount((int) list.stream().filter(detail -> detail.getResult() == 1).count());
            record.setUninvolvedCount((int) list.stream().filter(detail -> detail.getResult() == 2).count());
            record.setRepeatCount((int) list.stream().filter(detail -> detail.getResult() == 3).count());
            record.setUnCheckCount((int) list.stream().filter(detail -> detail.getResult() == -1).count());
            if(record.getCheckCount() != null && record.getCheckCount() != 0 && record.getUnCheckCount() != null){
                record.setCheckPercent(String.format("%.1f", (record.getCheckCount() - record.getUnCheckCount()) * 100.0 / record.getCheckCount()) + "%");
            }else{
                record.setCheckPercent("0");
            }
        }
        return page;
    }

    @Override
    @Transactional
    public Boolean delete(Long id) {
        removeById(id);
        selfCheckDetailService.lambdaUpdate().eq(SelfCheckDetail::getCheckRecordId,id)
                .remove();
        return true;
    }

    @Override
    public void export(SelfCheckRecord selfCheckRecord) {
        Page<SelfCheckRecord> selfCheckRecordPage = pageList(new Page<>(1L, 1000L), selfCheckRecord);
        List<SelfCheckRecord> list = selfCheckRecordPage.getRecords();
        ExcelOutUtil.exportByTemp(response, "/file/企业自查记录导出模板.xlsx", "企业自查记录" + DatePattern.PURE_DATETIME_FORMAT.format(new Date()) + ".xlsx", null, list);
    }
}

