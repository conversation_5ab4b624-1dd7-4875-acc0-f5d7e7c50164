package com.yixun.hdm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.api.ProjectConfigApi;
import com.yixun.bean.out.YqycProjectConfig;
import com.yixun.hd.api.HiddenDangerApi;
import com.yixun.hd.bean.CropHdNumIn;
import com.yixun.hd.bean.CropHdNumOut;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.em.AuditStatus;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.utils.HdQualityScoreUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest(classes = HdmAdminServiceApplication.class,properties = "spring.profiles.active=dev")
public class HdTest {

    @Autowired
    private HiddenDangerService hiddenDangerService;

    @Test
    public void fillRiskFactorTypeDetailAlias() {
        for (int i = 1; i < 1000; i++) {
            LambdaQueryWrapper<HiddenDanger> wrapper = new LambdaQueryWrapper<>();
//            wrapper.eq(HiddenDanger::getAuditStatus, AuditStatus.UnFinished.name());
            wrapper.isNull(HiddenDanger::getRiskFactorTypeDetailAlias);
            wrapper.eq(HiddenDanger::getProjectId,15127041517883648L);
            wrapper.like(HiddenDanger::getRiskFactorTypeDetail, "[%");
            Page<HiddenDanger> objectPage = new Page<>();
            objectPage.setSize(500);
            objectPage.setCurrent(i);
            IPage<HiddenDanger> page = hiddenDangerService.page(objectPage, wrapper);
            List<HiddenDanger> list = page.getRecords();
            if(list.isEmpty()){
                break;
            }
            JSONObject object = hiddenDangerService.convertRiskFactorTypeDetail(list);
            for (HiddenDanger hiddenDanger : list) {
                hiddenDanger.setRiskFactorTypeDetailAlias(object.getString(hiddenDanger.getId() + ""));
            }
            hiddenDangerService.updateBatchById(list);
        }
    }

    @Test
    public void calculateScore() {
        HiddenDanger hiddenDanger = hiddenDangerService.getById(16084797390004736L);
        Integer score = HdQualityScoreUtils.calculateScore(hiddenDanger);
        System.out.println("隐患id" + hiddenDanger.getId() + "分数：" + score);
    }

    @Test
    public void calcEffectiveHdNum() {
        Collection<HiddenDanger> hiddenDangers = hiddenDangerService.listByIds(
                Arrays.asList(16079501828825600L, 16079493283324416L, 16079485713129984L, 16079438652645888L, 16079196050498048L
                        , 16079185600127488L, 16079177592939008L, 16079167055143424L, 16079157541937664L, 16079147478163968L));
        List<HiddenDanger> list = new ArrayList<>(hiddenDangers);
        Integer num = HdQualityScoreUtils.calcEffectiveHdNum(list);
        System.out.println("有效隐患数：" + num);
    }

    @Autowired
    HiddenDangerApi hiddenDangerApi;

    @Autowired
    ProjectConfigApi projectConfigApi;

    @Test
    public void test() {
        YqycProjectConfig info = projectConfigApi.info(11820140291432960L);
        System.out.println(info);
    }

    @Test
    public void test2() {
        CropHdNumIn cropHdNumIn = new CropHdNumIn();
        cropHdNumIn.setProjectId(11820140291432960L);
        List<CropHdNumOut> latestTurnCropHdNum = hiddenDangerApi.getLatestTurnCropHdNum(cropHdNumIn);
        System.out.println(latestTurnCropHdNum);
    }


}
