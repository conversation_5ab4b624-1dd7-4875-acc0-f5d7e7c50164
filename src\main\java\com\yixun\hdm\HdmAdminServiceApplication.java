package com.yixun.hdm;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.baomidou.dynamic.datasource.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.TimeZone;

@Slf4j
@SpringBootApplication
@ServletComponentScan
@ComponentScan(basePackages={"com.yixun"})
@EnableFeignClients(basePackages={"com.yixun"})
@MapperScan("com.yixun.hdm.dao")
@EnableSpringUtil
public class HdmAdminServiceApplication {

	public static void main(String[] args) {
		SpringApplication app = new SpringApplication(HdmAdminServiceApplication.class);
		Environment env = app.run(args).getEnvironment();
		app.setBannerMode(Banner.Mode.CONSOLE);
		logApplicationStartup(env);
	}

	@PostConstruct
	void setDefaultTimezone(){
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
	}

	private static void logApplicationStartup(Environment env) {
		String protocol = "http";
		if (env.getProperty("server.ssl.key-store") != null) {
			protocol = "https";
		}
		String serverPort = env.getProperty("server.port");
		String contextPath = env.getProperty("server.servlet.context-path");
		if (StringUtils.isBlank(contextPath)) {
			contextPath = "/swagger-ui.html";
		} else {
			contextPath = contextPath + "/swagger-ui.html";
		}
		String hostAddress = "localhost";
		try {
			hostAddress = InetAddress.getLocalHost().getHostAddress();
		} catch (UnknownHostException e) {
			log.warn("The host name could not be determined, using `localhost` as fallback");
		}
		log.info("\n----------------------------------------------------------\n\t" +
						"Application '{}' is running! Access URLs:\n\t" +
						"Local: \t\t{}://localhost:{}{}\n\t" +
						"External: \t{}://{}:{}{}\n\t" +
						"Profile(s): \t{}\n----------------------------------------------------------",
				env.getProperty("spring.application.name"),
				protocol,
				serverPort,
				contextPath,
				protocol,
				hostAddress,
				serverPort,
				contextPath,
				env.getActiveProfiles());
	}
}
