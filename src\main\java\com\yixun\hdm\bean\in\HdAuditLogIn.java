package com.yixun.hdm.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class HdAuditLogIn {

    @NotNull(message="[${column.comment}]不能为空")
    private Long id;


    /**
     * 隐患id
     */
    @ApiModelProperty("隐患id")
    @NotNull(message="[隐患id]不能为空")
    private Long hiddenDangerId;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @NotNull(message="[项目id]不能为空")
    private Long projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 公司id
     */
    @ApiModelProperty("公司id")
    @NotNull(message="[公司id]不能为空")
    private Long corporationId;

    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String corpName;

    /**
     * 岗位/工种批注
     */
    @ApiModelProperty("岗位/工种批注")
    private String workCategoryComment;


    /**
     * 作业活动批注
     */
    @ApiModelProperty("作业活动批注")
    private String jobActivityComment;


    /**
     * 设备设施/物料批注
     */
    @ApiModelProperty("设备设施/物料批注")
    private String equipmentComment;


    /**
     * 隐患区域批注
     */
    @ApiModelProperty("隐患区域批注")
    private String hdRegionComment;



    /**
     * 隐患照片审查批注
     */
    @ApiModelProperty("隐患照片审查批注")
    private String picturesComment;


    /**
     * 隐患描述审查批注
     */
    @ApiModelProperty("隐患描述审查批注")
    private String descriptionComment;



    /**
     * 专项类别审查批注
     */
    @ApiModelProperty("专项类别审查批注")
    private String specialCategoryComment;


    /**
     * 隐患类型审查批注
     */
    @ApiModelProperty("隐患类型审查批注")
    private String typeComment;


    /**
     * 隐患级别审查批注
     */
    @ApiModelProperty("隐患级别审查批注")
    private String levelComment;


    /**
     * 一级业务审查批注
     */
    @ApiModelProperty("一级业务审查批注")
    private String tierOneBusinessComment;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;


    /**
     * 依据标准审查批注
     */
    @ApiModelProperty("依据标准审查批注")
    private String standardComment;


    /**
     * 可能导致后果审查批注
     */
    @ApiModelProperty("可能导致后果审查批注")
    private String possibleConsequenceComment;

    /**
     * 危害因素分类审查批注
     */
    @ApiModelProperty("危害因素分类审查批注")
    private String riskFactorTypeComment;


    /**
     * 风险点描述审查批注
     */
    @ApiModelProperty("风险点描述审查批注")
    private String riskPointComment;


    /**
     * 管理追溯审查批注
     */
    @ApiModelProperty("管理追溯审查批注")
    private String manageSourceComment;


    /**
     * 建议整改措施审查批注
     */
    @ApiModelProperty("建议整改措施审查批注")
    private String rectificationSuggestionComment;


    /**
     * 整改期限审查批注
     */
    @ApiModelProperty("整改期限审查批注")
    private String rectifyDeadlineComment;


    /**
     * 审查意见
     */
    @ApiModelProperty("审查意见")
    private String auditComment;

}
