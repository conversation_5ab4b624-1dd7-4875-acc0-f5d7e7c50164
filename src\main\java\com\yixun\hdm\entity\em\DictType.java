package com.yixun.hdm.entity.em;

public enum DictType {

    mainProduct("产品或服务"),
    surveyContent("调研内容"),
    comInsurance("企业商业保险"),
    mainFacility("设备设施"),
    accident("事故类型"),
    industry("行业分类"),
    occuDangerFactor("职业危害因素"),
    involveCraft("工艺"),
    superCraft("重点监管工艺"),
    superChemical("重点监管化学品"),
    majorSafetyRisk("主要安全风险"),
    majorHealthRisk("主要职业健康风险"),
    yhoutcome("隐患管理可能导致的后果"),
    yhonebiz("隐患管理一级业务"),
    obsoleteEquip("淘汰工艺设备"),
    specialWork("特殊工种"),
    nationalEconomyIndustry("国民经济行业"),
    yhEndangerFactors("隐患危害因素"),
    specialCategory("专项类别"),
    keyIndustry("重点行业"),
    mainDiagnose("主要诊断"),
    commercialInsuranceType("商业保险类型"),
    riskPointType("风险类型"),
    projectStage("项目阶段"),
    performanceEvaluator("履职评估人员类型程锦"),
    standardType("标准类型");

    private String mark;

    DictType(String mark ) {
        this.mark = mark;
    }

    public String getMark() {
        return mark;
    }
}
