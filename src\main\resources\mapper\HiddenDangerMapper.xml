<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yixun.hdm.dao.HiddenDangerMapper">


    <select id="frequentlyUseStandard" resultType="java.lang.String">
        SELECT
            sub1.id
        FROM
            `hdm_hidden_danger` t,
            JSON_TABLE ( t.standard, "$[*]" COLUMNS ( id VARCHAR ( 255 ) PATH "$.id", `from` VARCHAR ( 255 ) PATH "$.from" ) ) AS sub1
        WHERE
            sub1.`from` = #{from}
          and t.operator_id = #{userId}
        GROUP BY
            sub1.id
        ORDER BY
            count( sub1.id ) DESC
            LIMIT 30
    </select>
    <select id="getHiddenDangerPage" resultType="com.yixun.hdm.entity.HiddenDanger" resultMap="mybatis-plus_HiddenDanger">
        select
            *
        from
            hdm_hidden_danger t
        <if test="quality != null and quality !=''">
            left join hdm_hd_audit_log t1 on t.id = t1.hidden_danger_id and t1.audit_status = 'Audited'
        </if>
        where
            1=1
        <if test="ids != null">
            and t.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="projectId != null and projectId != ''">
            and t.project_id = #{projectId}
        </if>
        <if test="corporationId != null and corporationId != ''">
            and t.corporation_id = #{corporationId}
        </if>
        <if test="turn != null">
            and t.turn = #{turn}
        </if>
        <if test="taskType != null">
            and t.task_type = #{taskType}
        </if>
        <if test="taskId != null">
            and t.task_id = #{taskId}
        </if>
<!--        <if test="taskWorkDate != null">-->
<!--            and t.task_work_date = #{taskWorkDate}-->
<!--        </if>-->
        <if test="startDate != null and endDate != null">
            and t.create_time between #{startDate} and #{endDate}
        </if>
        <if test="operatorId != null">
            and t.operator_id = #{operatorId}
        </if>
        <if test="level != null and level != ''">
            and t.level = #{level}
        </if>
        <if test="type != null">
            and t.type = #{type}
        </if>
        <if test="specialCategory != null and specialCategory != ''">
            and t.special_category = #{specialCategory}
        </if>
        <if test="possibleConsequence != null and possibleConsequence != ''">
            and t.possible_consequence = #{specialCategory}
        </if>
        <if test="riskFactorTypeDetail != null and riskFactorTypeDetail != ''">
            and t.risk_factor_type_detail like concat('%',#{riskFactorTypeDetail},'%')
        </if>
        <if test="riskFactorTypeDetailAlias != null and riskFactorTypeDetailAlias != ''">
            and t.risk_factor_type_detail_alias like concat('%',#{riskFactorTypeDetailAlias},'%')
        </if>
        <if test="hiddenDangerStatus != null">
            and t.status = #{hiddenDangerStatus}
        </if>
        <if test="auditStatus != null and auditStatus != ''">
            and t.audit_status = #{auditStatus}
        </if>
        <if test="isFindByCallback != null">
            and t.is_find_by_callback = #{isFindByCallback}
        </if>
        <if test="isWrongSubmit != null">
            and t.is_wrong_submit = #{isWrongSubmit}
        </if>
        <if test="isWrongSubmit == null">
            and t.is_wrong_submit != 1
        </if>
        <if test="isNeedEdit != null">
            and t.is_need_edit = #{isNeedEdit}
        </if>
        <choose>
            <when test="qualityScore == 0">and t.hd_quality_score = 0</when>
            <when test="qualityScore == 1">and t.hd_quality_score > 0</when>
            <when test="qualityScore == 2">and t.hd_quality_score &lt; 0</when>
        </choose>
        <if test="quality != null and quality !=''">
          <choose>
              <when test="quality == '无问题'">
                  and CONCAT_WS(',',
                  t1.hd_region_comment,
                  t1.equipment_comment,
                  t1.job_activity_comment,
                  t1.work_category_comment,
                  t1.type_comment,
                  t1.level_comment,
                  t1.standard_comment,
                  t1.tier_one_business_comment,
                  t1.pictures_comment,
                  t1.description_comment,
                  t1.manage_source_comment,
                  t1.possible_consequence_comment,
                  t1.rectification_suggestion_comment,
                  t1.rectify_deadline_comment,
                  t1.risk_point_comment,
                  t1.risk_factor_type_comment,
                  t1.special_category_comment
                  ) = ''
              </when>
              <when test="quality == '任一问题'">
                  and CONCAT_WS(',',
                  t1.hd_region_comment,
                  t1.equipment_comment,
                  t1.job_activity_comment,
                  t1.work_category_comment,
                  t1.type_comment,
                  t1.level_comment,
                  t1.standard_comment,
                  t1.tier_one_business_comment,
                  t1.pictures_comment,
                  t1.description_comment,
                  t1.manage_source_comment,
                  t1.possible_consequence_comment,
                  t1.rectification_suggestion_comment,
                  t1.rectify_deadline_comment,
                  t1.risk_point_comment,
                  t1.risk_factor_type_comment,
                  t1.special_category_comment
                  ) != ''
              </when>
              <otherwise>
                  and CONCAT_WS(',',
                  t1.hd_region_comment,
                  t1.equipment_comment,
                  t1.job_activity_comment,
                  t1.work_category_comment,
                  t1.type_comment,
                  t1.level_comment,
                  t1.standard_comment,
                  t1.tier_one_business_comment,
                  t1.pictures_comment,
                  t1.description_comment,
                  t1.manage_source_comment,
                  t1.possible_consequence_comment,
                  t1.rectification_suggestion_comment,
                  t1.rectify_deadline_comment,
                  t1.risk_point_comment,
                  t1.risk_factor_type_comment,
                  t1.special_category_comment
                  ) like CONCAT('%',#{quality},'%')
              </otherwise>
          </choose>
        </if>
        order by t.create_time desc
    </select>
    <select id="recommendSpecialCategory" resultType="com.yixun.hdm.bean.out.RecommendSpecialCategoryOut">
        SELECT
            ttt.id as checkContentId,
            ttt.special_category_id as specialCategoryId,
            ttt.total,
            ttt.rn
        FROM
            (
                SELECT
                    tt.id,
                    tt.special_category_id,
                    tt.total,
                    ROW_NUMBER() OVER ( PARTITION BY tt.id ORDER BY tt.total DESC ) AS rn
                FROM
                    (
                        SELECT
                            sub1.id,
                            sub2.special_category_id,
                            sum( 1 ) AS total
                        FROM
                            `hdm_hidden_danger` t,
                            JSON_TABLE ( t.standard, "$[*]" COLUMNS ( id VARCHAR ( 255 ) PATH "$.id", `from` VARCHAR ( 255 ) PATH "$.from" ) ) AS sub1,
                            JSON_TABLE (
                                    t.special_category_detail,
                                    "$[1]" COLUMNS ( special_category_id VARCHAR ( 255 ) PATH "$.value" )) AS sub2
                        WHERE
                            sub1.`from` = 'checkContent'
                          AND sub1.id in
                        <foreach collection="checkContentIdSet" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                        GROUP BY
                            sub1.id,
                            sub2.special_category_id
                    ) AS tt
            ) AS ttt
        WHERE
            ttt.rn = 1
    </select>
    <select id="recommendRiskFactorType" resultType="com.yixun.hdm.bean.out.RecommendSpecialCategoryOut">
        SELECT
            ttt.id as checkContentId,
            ttt.risk_factor_type_id as specialCategoryId,
            ttt.total,
            ttt.rn
        FROM
            (
                SELECT
                    tt.id,
                    tt.risk_factor_type_id,
                    tt.total,
                    ROW_NUMBER() OVER ( PARTITION BY tt.id ORDER BY tt.total DESC ) AS rn
                FROM
                    (
                        SELECT
                            sub1.id,
                            sub2.risk_factor_type_id,
                            sum( 1 ) AS total
                        FROM
                            `hdm_hidden_danger` t,
                            JSON_TABLE ( t.standard, "$[*]" COLUMNS ( id VARCHAR ( 255 ) PATH "$.id", `from` VARCHAR ( 255 ) PATH "$.from" ) ) AS sub1,
                            JSON_TABLE (
                                    t.risk_factor_type_detail,
                                    "$[1]" COLUMNS ( risk_factor_type_id VARCHAR ( 255 ) PATH "$.value" )) AS sub2
                        WHERE
                            sub1.`from` = 'checkContent'
                          AND sub1.id in
                        <foreach collection="checkContentIdSet" item="id" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                        GROUP BY
                            sub1.id,
                            sub2.risk_factor_type_id
                    ) AS tt
            ) AS ttt
        WHERE
            ttt.rn = 1
    </select>
    <select id="top5PossibleConsequenceName" resultType="com.yixun.hdm.bean.out.RecommendPossibleConsequenceName">
        SELECT
        ttt.id as checkContentId,
        ttt.possible_consequence_name as possibleConsequenceName,
        ttt.total,
        ttt.rn
        FROM
        (
        SELECT
        tt.id,
        tt.possible_consequence_name,
        tt.total,
        ROW_NUMBER() OVER ( PARTITION BY tt.id ORDER BY tt.total DESC ) AS rn
        FROM
        (
        SELECT
        sub1.id,
        sub2.possible_consequence_name,
        sum( 1 ) AS total
        FROM
        `hdm_hidden_danger` t,
        JSON_TABLE ( t.standard, "$[*]" COLUMNS ( id VARCHAR ( 255 ) PATH "$.id", `from` VARCHAR ( 255 ) PATH "$.from" ) ) AS sub1,
        JSON_TABLE (
        t.possible_consequence,
        "$[*]" COLUMNS ( possible_consequence_name VARCHAR ( 255 ) PATH "$" )) AS sub2
        WHERE
        sub1.`from` = 'checkContent'
        AND sub1.id in
        <foreach collection="checkContentIdSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        sub1.id,
        sub2.possible_consequence_name
        ) AS tt
        ) AS ttt
        WHERE
        ttt.rn &lt;= 5
    </select>
    <select id="getCropHdNum" resultType="com.yixun.hd.bean.CropHdNumOut">
        SELECT
        t.corporation_id as corpId,
        t.turn,
        sum( 1 ) AS hdNum
        FROM
        hdm_hidden_danger t
        WHERE
        t.project_id = #{projectId}
        AND t.corporation_id in
        <foreach collection="corpIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        t.corporation_id,
        t.turn
    </select>
    <select id="getLatestTurnCropHdNum" resultType="com.yixun.hd.bean.CropHdNumOut">
        SELECT
            t.corporation_id AS corpId,
            t.turn,
            SUM( 1 ) as hdNum
        FROM
            hdm_hidden_danger t
                INNER JOIN ( SELECT project_id, corporation_id, MAX( turn ) AS maxTurn FROM hdm_hidden_danger GROUP BY project_id, corporation_id ) t1 ON t.project_id = t1.project_id
                AND t.corporation_id = t1.corporation_id
                AND t.turn = t1.maxTurn
        WHERE
            is_wrong_submit != 1
	    AND audit_status = 'Released'
        AND t.project_id = #{projectId}
        <if test="corpIdList != null and corpIdList.size() > 0">
            AND t.corporation_id in
            <foreach collection="corpIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="level != null and level !=''">
            AND t.level = #{level}
        </if>
        GROUP BY
            t.project_id,
            t.corporation_id
    </select>
    <select id="getCropSafeStatistic" resultType="com.yixun.hdm.bean.out.CorpSafeListOut">
        SELECT
            project_id,
            corporation_id as corpId,
            sum(1) as surveyHdNum,
            sum(IF(`status` in ("待验证","已改善"),1,0)) as improvedHdNum,
            sum(IF(`status` in ("待验证","已改善"),1,0)) / sum(1) as improveRate,
            SUM(IF(`status` in ("待改善","改善中") and DATEDIFF(NOW(),create_time) > rectify_deadline,1,0)) as overdueHdNum,
            SUM(IF(`status` in ("待改善","改善中") and DATEDIFF(NOW(),create_time) > rectify_deadline + 5,1,0)) as overdue5DaysHdNum,
            SUM(IF(`status` in ("待改善","改善中") and DATEDIFF(NOW(),create_time) > rectify_deadline + 10,1,0)) as overdue10DaysHdNum
        FROM
            `hdm_hidden_danger`
        WHERE
            audit_status = 'Released'
          AND task_type IN (1,2)
        <if test="projectId != null">
            AND project_id = #{projectId}
        </if>
        <if test="corpName != null and corp_name != ''">
            AND corp_name LIKE CONCAT('%',#{corpName},'%')
        </if>
        <if test="corpIds != null and corpIds.size() > 0">
            AND corporation_id in
            <foreach collection="corpIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY project_id,corporation_id
        <if test="hdOverdue != null">
            <if test="hdOverdue == 1">
                HAVING overdueHdNum = surveyHdNum
            </if>
            <if test="hdOverdue == 2">
                HAVING overdue5DaysHdNum > 0
            </if>
            <if test="hdOverdue == 3">
                HAVING overdue10DaysHdNum > 0
            </if>
        </if>
    </select>
</mapper>

