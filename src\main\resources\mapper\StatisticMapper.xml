<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yixun.hdm.dao.StatisticMapper">
    <select id="getMonthlyTotalCount" resultType="com.yixun.hdm.bean.out.statistic.HdCountOut">
        SELECT create_time, DATE_FORMAT(create_time, '%Y-%m') AS `month`, COUNT(*) AS `totalCount`
        FROM hdm_hidden_danger
        where is_wrong_submit=false and create_time > #{firstMonth}
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        GROUP BY `month`
    </select>
    <select id="getMonthlyDoneCount" resultType="com.yixun.hdm.bean.out.statistic.HdCountOut">
        SELECT create_time, DATE_FORMAT(create_time, '%Y-%m') AS `month`, COUNT(*) AS `doneCount`
        FROM hdm_hidden_danger
        where is_wrong_submit=false and create_time > #{firstMonth} and status='已改善'
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        GROUP BY `month`
    </select>
    <select id="getHdTypeByProject" resultType="com.yixun.hdm.bean.out.statistic.HdTypeOut">
        SELECT `type`, COUNT(*) AS `count`
        FROM hdm_hidden_danger
        where is_wrong_submit=false and `type`!=""
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        <if test="startDate!=null">
            <if test="endDate!=null" >
                and create_time between #{startDate} and #{endDate}
            </if>
        </if>
        GROUP BY `type`
    </select>
    <select id="getHdStatusByProject" resultType="com.yixun.hdm.bean.out.statistic.HdStatusOut">
        SELECT status, COUNT(*) AS `count`
        FROM hdm_hidden_danger
        where is_wrong_submit=false
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        <if test="startDate!=null">
            <if test="endDate!=null" >
                and create_time between #{startDate} and #{endDate}
            </if>
        </if>
        GROUP BY status
    </select>
    <select id="getHdWrongByProject" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM hdm_hidden_danger
        where is_wrong_submit=true
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        <if test="startDate!=null">
            <if test="endDate!=null" >
                and create_time between #{startDate} and #{endDate}
            </if>
        </if>
    </select>
    <select id="getHdCountByProject" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM hdm_hidden_danger
        where is_wrong_submit=false
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        <if test="startDate!=null">
            <if test="endDate!=null" >
                and create_time between #{startDate} and #{endDate}
            </if>
        </if>
    </select>
    <select id="getHdTierOneTypeByProject" resultType="com.yixun.hdm.bean.out.statistic.HdTypeOut">
        SELECT tier_one_business as `type`, COUNT(*) AS `count`
        FROM hdm_hidden_danger
        where is_wrong_submit=false
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        <if test="startDate!=null">
            <if test="endDate!=null" >
                and create_time between #{startDate} and #{endDate}
            </if>
        </if>
        GROUP BY tier_one_business
    </select>
    <select id="getHdRiskFactorByProject" resultType="com.yixun.hdm.bean.out.statistic.HdTypeOut">
        SELECT risk_factor_type as `type`, COUNT(*) AS `count`
        FROM hdm_hidden_danger
        where is_wrong_submit=false
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        GROUP BY risk_factor_type
    </select>
    <select id="getHdLevelCountByProject" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM hdm_hidden_danger
        where is_wrong_submit=false and `level` = #{level}
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
    </select>
    <select id="getHdCorporationByProject" resultType="com.yixun.hdm.bean.out.statistic.HdCorporationOut">
        SELECT corporation_id, corp_name, COUNT(*) AS `count`
        FROM hdm_hidden_danger
        where is_wrong_submit=false
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        <if test="corporationId!=null">
            and corporation_id = #{corporationId}
        </if>
        GROUP BY corporation_id
    </select>
    <select id="getHdCorpInfoPage" resultType="com.yixun.hdm.bean.out.HdCorpInfoOut">
        SELECT 	project_name, corp_name, project_id, corporation_id, group_concat(id) as ids,
                COUNT(id) as hdCount,
                SUM(CASE WHEN is_find_by_callback = false THEN 1 ELSE 0 END) AS diaoyanCount,
                SUM(CASE WHEN is_find_by_callback = false and `status` = '已改善' THEN 1 ELSE 0 END) AS diaoyanDoneCount,
                SUM(CASE WHEN is_find_by_callback = true THEN 1 ELSE 0 END) AS huifangCount,
                SUM(CASE WHEN is_find_by_callback = true and `status` = '已改善' THEN 1 ELSE 0 END) AS huifangDoneCount,
				SUM(CASE WHEN `level` = '重大隐患' THEN 1 ELSE 0 END) AS majorCount,
				SUM(CASE WHEN `level` = '一般隐患' THEN 1 ELSE 0 END) AS normalCount,
				SUM(CASE WHEN `status` = '已改善' THEN 1 ELSE 0 END) AS doneCount,
				SUM(CASE WHEN `type` = '现场类' THEN 1 ELSE 0 END) AS xianchangCount,
				SUM(CASE WHEN `type` = '管理类' THEN 1 ELSE 0 END) AS guanliCount,
				SUM(CASE WHEN special_category = '生产安全' THEN 1 ELSE 0 END) AS anquanshengchanCount,
				SUM(CASE WHEN special_category = '职业健康' THEN 1 ELSE 0 END) AS zhiyejiankangCount,
				SUM(CASE WHEN special_category = '其他' THEN 1 ELSE 0 END) AS qitaCount
        FROM hdm_hidden_danger
        where is_wrong_submit=false
        and project_id = #{projectId}
        and corporation_id in
        <foreach collection="corpIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY corporation_id, project_id
    </select>
    <select id="getHdCorpInfoList" resultType="com.yixun.hdm.bean.out.HdCorpInfoOut">
        SELECT 	project_name, corp_name, project_id, corporation_id, group_concat(id) as ids,
        COUNT(id) as hdCount,
        SUM(CASE WHEN is_find_by_callback = false THEN 1 ELSE 0 END) AS diaoyanCount,
        SUM(CASE WHEN is_find_by_callback = false and `status` = '已改善' THEN 1 ELSE 0 END) AS diaoyanDoneCount,
        SUM(CASE WHEN is_find_by_callback = true THEN 1 ELSE 0 END) AS huifangCount,
        SUM(CASE WHEN is_find_by_callback = true and `status` = '已改善' THEN 1 ELSE 0 END) AS huifangDoneCount,
        SUM(CASE WHEN `level` = '重大隐患' THEN 1 ELSE 0 END) AS majorCount,
        SUM(CASE WHEN `level` = '一般隐患' THEN 1 ELSE 0 END) AS normalCount,
        SUM(CASE WHEN `status` = '已改善' THEN 1 ELSE 0 END) AS doneCount,
        SUM(CASE WHEN `type` = '现场类' THEN 1 ELSE 0 END) AS xianchangCount,
        SUM(CASE WHEN `type` = '管理类' THEN 1 ELSE 0 END) AS guanliCount,
        SUM(CASE WHEN special_category = '生产安全' THEN 1 ELSE 0 END) AS anquanshengchanCount,
        SUM(CASE WHEN special_category = '职业健康' THEN 1 ELSE 0 END) AS zhiyejiankangCount,
        SUM(CASE WHEN special_category = '其他' THEN 1 ELSE 0 END) AS qitaCount
        FROM hdm_hidden_danger
        where is_wrong_submit=false
        and project_id = #{projectId}
        and corporation_id in
        <foreach collection="corpIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY corporation_id, project_id
    </select>
    <select id="getProjectCorpHdStatistic" resultType="com.yixun.hdm.bean.out.ProjectCorpHdStatisticOut">
        SELECT corporation_id, COUNT(*) AS `count`
        FROM hdm_hidden_danger
        where is_wrong_submit=false and project_id = #{projectId}
        and corporation_id in
        <foreach collection="corpIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY corporation_id
    </select>
    <select id="getProjectCorpHdCount" resultType="com.yixun.hdm.bean.out.ProjectCorpHdStatisticOut">
        SELECT corporation_id, COUNT(*) AS `count`
        FROM hdm_hidden_danger
        where is_wrong_submit=false and project_id = #{projectId}
        and corporation_id in
        <foreach collection="corpIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY corporation_id
    </select>
</mapper>