package com.yixun.hdm.bean.out;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 自查企业导入进度输出
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Data
public class SelfCheckCorpImportProgressOut {

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("状态：进行中/已完成/失败")
    private String status;

    @ApiModelProperty("解析的总行数")
    private Integer totalRows;

    @ApiModelProperty("成功导入行数")
    private Integer successCount;

    @ApiModelProperty("导入失败行数")
    private Integer failCount;

    @ApiModelProperty("失败的Excel行号数组")
    private List<Integer> failedRowNumbers;

    @ApiModelProperty("失败原因详情，key为行号，value为失败原因")
    private Map<Integer, String> failureReasons;

    @ApiModelProperty("进度百分比(0-100)")
    private Integer progress;
}
