package com.yixun.hdm.controller.admin;

import com.alibaba.fastjson.JSON;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.in.RectificationIn;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.OperationLogs;
import com.yixun.hdm.entity.Rectification;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import com.yixun.hdm.entity.em.OperationType;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.service.RectificationService;
import com.yixun.hdm.service.UserCenterService;
import com.yixun.hdm.utils.AdminUserHelper;
import com.yixun.hdm.utils.BeanUtils;
import com.yixun.hdm.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

@Api(tags = "admin隐患改善")
@RestController
@RequestMapping(value = "/admin/rectification")
public class AdminRectificationController {

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource
    private RectificationService rectificationService;

    @Resource
    private UserCenterService userCenterService;

    @PostMapping("/submit")
    @ApiOperation("隐患改善：初次保存或提交")
    public CommonResult<Void> submit(@RequestBody RectificationIn hiddenDangerIn) {

        HiddenDanger hiddenDanger = hiddenDangerService.getById(hiddenDangerIn.getHiddenDangerId());
        if (hiddenDanger==null){
            throw new DataErrorException("未找到该隐患");
        }
        if (!hiddenDanger.getStatus().equals(HiddenDangerStatus.待改善.name()) && !hiddenDanger.getStatus().equals(HiddenDangerStatus.改善中.name())){
            throw new DataErrorException("隐患状态错误");
        }

        Rectification rectification = new Rectification();
        Long rectificationId = SnGeneratorUtil.getId();
        rectification.setId(rectificationId);
        rectification.setCreateTime(new Date());
        BeanUtils.copyProperties(hiddenDangerIn, rectification);
        rectification.setPictures(JSON.toJSONString(hiddenDangerIn.getPictures()));

        OperationLogs operationLogs = new OperationLogs();
        operationLogs.setId(SnGeneratorUtil.getId());
        operationLogs.setCreateTime(new Date());
        operationLogs.setCorporationId(hiddenDanger.getCorporationId());
        operationLogs.setHiddenDangerId(hiddenDangerIn.getHiddenDangerId());
        operationLogs.setOperationType(OperationType.rectification.name());
        operationLogs.setInChargeName(hiddenDangerIn.getInChargeName());
        StaffUserDto adminUser = userCenterService.getAdminUser(AdminUserHelper.getCurrentUserId());
        operationLogs.setOperatorId(adminUser.getId());
        operationLogs.setOperator(adminUser.getRealName());

        hiddenDanger.setStatus(HiddenDangerStatus.待验证.name());
        hiddenDanger.setUpdateTime(new Date());

        rectificationService.submitRectification(hiddenDanger, rectification, operationLogs);

        return CommonResult.successResult("提交成功");
    }

}
