package com.yixun.hdm.controller.admin;



import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.HiddenDangerSuggestion;
import com.yixun.hdm.service.HiddenDangerSuggestionService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 隐患建议改善措施(HiddenDangerSuggestion)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-27 14:06:46
 */
@Api(tags = "admin 隐患建议改善措施")
@RestController
@RequestMapping("/admin/hiddenDanger/suggestion")
public class HiddenDangerSuggestionController {

    @Autowired
    private HiddenDangerSuggestionService hiddenDangerSuggestionService;

    @GetMapping("list")
    public CommonResult<List<HiddenDangerSuggestion>> list(@RequestParam Long hdId) {
        List<HiddenDangerSuggestion> list = hiddenDangerSuggestionService.list(hdId);
        return CommonResult.successData(list);
    }

    @PostMapping("update")
    public void update(@RequestBody List<HiddenDangerSuggestion> list){
        hiddenDangerSuggestionService.updateBatchById(list);
    }

}

