package com.yixun.hdm.controller.api;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.UserCollect;
import com.yixun.hdm.service.UserCollectService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Slf4j
@Api(tags = "admin 用户收藏")
@RestController
@RequestMapping("/api/user/collect")
public class ApiUserCollectController {
    /**
     * 服务对象
     */
    @Autowired
    private UserCollectService userCollectService;

    /**
     * 分页查询所有数据
     *
     * @return 所有数据
     */
    @GetMapping("idList")
    public CommonResult<Set<Long>> idSet(@RequestParam("userId") Long userId,
                                         @RequestParam("contentType") Integer contentType,
                                         @RequestParam("range") Integer range) {
        return CommonResult.successData(userCollectService.idSet(userId,contentType,range));
    }

}
