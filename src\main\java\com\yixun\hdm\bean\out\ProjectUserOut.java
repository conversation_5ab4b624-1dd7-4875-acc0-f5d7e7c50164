package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

@Data
public class ProjectUserOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    private String projectName;
    private String phone;
    private String realName;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long organizationId;
    private String organization;
    private String jobTitle;
    private String position;

}
