package com.yixun.hdm.controller.api;

import com.alibaba.fastjson.JSON;
import com.yixun.hd.api.HiddenDangerApi;
import com.yixun.hd.bean.CropHdNumIn;
import com.yixun.hd.bean.CropHdNumOut;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.common.ErrorMessage;
import com.yixun.hdm.bean.in.CorpNameApiIn;
import com.yixun.hdm.bean.in.HiddenDangerListApiGetIn;
import com.yixun.hdm.bean.in.HiddenDangerListApiIn;
import com.yixun.hdm.bean.in.ArrangedAuditorApiIn;
import com.yixun.hdm.bean.out.*;
import com.yixun.hdm.bean.out.statistic.HdTypeOut;
import com.yixun.hdm.dao.HiddenDangerMapper;
import com.yixun.hdm.entity.*;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import com.yixun.hdm.entity.em.OperationType;
import com.yixun.hdm.exception.ParameterErrorException;
import com.yixun.hdm.service.*;
import com.yixun.hdm.utils.BeanFieldCheckingUtils;
import com.yixun.hdm.utils.BeanUtils;
import com.yixun.hdm.utils.SnGeneratorUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/hiddenDanger")
public class ApiHiddenDangerController{

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource
    private HiddenDangerTempService hiddenDangerTempService;

    @Resource
    private RectificationService rectificationService;

    @Resource
    private RectificationTempService rectificationTempService;

    @Resource
    private VerificationService verificationService;

    @Resource
    private VerificationTempService verificationTempService;

    @Resource
    private StatisticService statisticService;

    @Autowired
    private HiddenDangerMapper hiddenDangerMapper;

//    @ApiOperation(value = "获取隐患详情列表")
//    @GetMapping(value = "/getHiddenDangerList")
//    public CommonResult<List<HiddenDangerApiOut>> getHiddenDangerList(HiddenDangerListApiGetIn hiddenDangerListGetIn){
//
//        List<HiddenDanger> hiddenDangerList = hiddenDangerService.getHiddenDangerList(hiddenDangerListGetIn);
//        List<HiddenDangerApiOut> outList = BeanUtils.copyToOutList(hiddenDangerList, HiddenDangerApiOut.class);
//        outList.forEach(hd->{
//            if (hd.getStatus().equals(HiddenDangerStatus.已改善.name())){
//                Rectification rectification = rectificationService.getLastRectification(hd.getId());
//                if (rectification!=null){
//                    hd.setRectificationTime(rectification.getCreateTime());
//                    hd.setRectificationDescription(rectification.getDescription());
//                    hd.setRectificationPictures(rectification.getPictures());
//                    hd.setRectificationOperator(rectification.getInChargeName());
//                }
//                Verification verification = verificationService.getLastVerification(hd.getId());
//                if (verification!=null){
//                    hd.setVerifyTime(verification.getCreateTime());
//                    hd.setVerifyDescription(verification.getReason());
//                    hd.setVerifyPictures(verification.getPictures());
//                    hd.setVerifyOperator(verification.getInChargeName());
//                }
//            }
//        });
//
//        return CommonResult.successData(outList);
//    }

    @ApiOperation(value = "按项目获取隐患统计")
    @GetMapping(value = "/getHiddenDangerCount")
    public CommonResult<List<HiddenDangerProjectApiOut>> getHiddenDangerCount(HiddenDangerListApiGetIn hiddenDangerListGetIn){

        List<HiddenDanger> hiddenDangerList = hiddenDangerService.getHiddenDangerList(hiddenDangerListGetIn);
        List<HiddenDangerProjectApiOut> outList = new ArrayList<>();
        Map<Long, List<HiddenDanger>> collect = hiddenDangerList.stream().collect(Collectors.groupingBy(HiddenDanger::getCorporationId));
        collect.forEach((cid, list)->{
            HiddenDangerProjectApiOut out = new HiddenDangerProjectApiOut();
            out.setCorporationId(cid);
            out.setCorpName(list.get(0).getCorpName());
            out.setProjectId(hiddenDangerListGetIn.getProjectId());
            out.setHdTotalCount(list.size());
            out.setHdDoneCount(list.stream().filter(l->l.getStatus().equals(HiddenDangerStatus.已改善.name())).count());
            List<HiddenDanger> majorHdList = list.stream().filter(l -> l.getLevel().equals("重大隐患")).collect(Collectors.toList());
            out.setMajorHdCount(majorHdList.size());
            out.setMajorHdDescList(majorHdList.stream().map(HiddenDanger::getDescription).collect(Collectors.toList()));
            outList.add(out);
        });

        return CommonResult.successData(outList);
    }

    @ApiOperation(value = "按项目获取隐患分析")
    @GetMapping(value = "/getHdTypeCount")
    public CommonResult<List<HdTypeOut>> getHdTypeCount(Long projectId){

        List<HdTypeOut> outList = statisticService.getHdTierOneTypeByProject(projectId, null, null);
        outList = outList.stream().sorted(Comparator.comparing(HdTypeOut::getCount).reversed()).collect(Collectors.toList());

        return CommonResult.successData(outList);
    }

    @ApiOperation(value = "批量导入隐患")
    @PostMapping(value = "/doSaveList")
    public CommonResult doSaveList(@RequestBody HiddenDangerListApiIn hiddenDangerListApiIn){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(hiddenDangerListApiIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        StaffUserDto adminUser = new StaffUserDto();
        adminUser.setId(10000L);
        adminUser.setRealName("历史数据导入");

        hiddenDangerListApiIn.getHiddenDangerList().forEach(hiddenDangerListApi -> {
            HiddenDanger hiddenDanger = new HiddenDanger();
            Long hiddenDangerId = SnGeneratorUtil.getId();
            hiddenDanger.setId(hiddenDangerId);
            hiddenDanger.setCreateTime(new Date());
            hiddenDanger.setOperator(adminUser.getRealName());
            hiddenDanger.setOperatorId(adminUser.getId());
            BeanUtils.copyProperties(hiddenDangerListApi, hiddenDanger);
            hiddenDanger.setPictures(hiddenDangerListApi.getPictures());
            hiddenDanger.setFiles(hiddenDangerListApi.getFiles());
            hiddenDanger.setStandard(JSON.toJSONString(hiddenDangerListApi.getStandard()));
            hiddenDanger.setPossibleConsequence(hiddenDangerListApi.getPossibleConsequence());
            hiddenDanger.setStatus(hiddenDangerListApi.getStatus().name());
            hiddenDanger.setIsWrongSubmit(false);
            hiddenDanger.setIsFindByCallback(false);

            OperationLogs operationLogs = new OperationLogs();
            operationLogs.setId(SnGeneratorUtil.getId());
            operationLogs.setCreateTime(new Date());
            operationLogs.setCorporationId(hiddenDangerListApi.getCorporationId());
            operationLogs.setHiddenDangerId(hiddenDangerId);
            operationLogs.setOperationType(OperationType.submit.name());
            operationLogs.setInChargeName(adminUser.getRealName());
            operationLogs.setOperatorId(adminUser.getId());
            operationLogs.setOperator(adminUser.getRealName());

            hiddenDangerService.submitHiddenDanger(hiddenDanger, operationLogs);

            Rectification rectification = new Rectification();
            rectification.setId(SnGeneratorUtil.getId());
            rectification.setCreateTime(new Date());
            rectification.setHiddenDangerId(hiddenDangerId);
            rectification.setInChargeName(adminUser.getRealName());
            rectification.setPictures(JSON.toJSONString(hiddenDangerListApi.getRectPictures()));
            rectification.setDescription(hiddenDangerListApi.getRectDescription());
            rectification.setIsSubmit(true);

            OperationLogs rectificationLogs = new OperationLogs();
            rectificationLogs.setId(SnGeneratorUtil.getId());
            rectificationLogs.setCreateTime(new Date());
            rectificationLogs.setCorporationId(hiddenDanger.getCorporationId());
            rectificationLogs.setHiddenDangerId(hiddenDangerId);
            rectificationLogs.setOperationType(OperationType.rectification.name());
            rectificationLogs.setInChargeName(adminUser.getRealName());
            rectificationLogs.setOperatorId(adminUser.getId());
            rectificationLogs.setOperator(adminUser.getRealName());

            rectificationService.submitRectification(null, rectification, rectificationLogs);

            Verification verification = new Verification();
            verification.setId(SnGeneratorUtil.getId());
            verification.setCreateTime(new Date());
            verification.setHiddenDangerId(hiddenDangerId);
            verification.setInChargeName(adminUser.getRealName());
            verification.setIsPass(hiddenDangerListApi.getIsPass());
            verification.setReason(hiddenDangerListApi.getReason());
            verification.setPictures(JSON.toJSONString(hiddenDangerListApi.getRectPictures()));

            OperationLogs verificationLogs = new OperationLogs();
            verificationLogs.setId(SnGeneratorUtil.getId());
            verificationLogs.setCreateTime(new Date());
            verificationLogs.setCorporationId(hiddenDanger.getCorporationId());
            verificationLogs.setHiddenDangerId(hiddenDangerId);
            verificationLogs.setOperationType(OperationType.verification.name());
            verificationLogs.setInChargeName(adminUser.getRealName());
            verificationLogs.setOperatorId(adminUser.getId());
            verificationLogs.setOperator(adminUser.getRealName());

            verificationService.submitVerification(null, verification, verificationLogs);
        });

        return CommonResult.successResult("保存成功");
    }

    @ApiOperation(value = "批量导入隐患-测试")
    @PostMapping(value = "/doSaveListTemp")
    public CommonResult doSaveListTemp(@RequestBody HiddenDangerListApiIn hiddenDangerListApiIn){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(hiddenDangerListApiIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        StaffUserDto adminUser = new StaffUserDto();
        adminUser.setId(10000L);
        adminUser.setRealName("历史数据导入");

        hiddenDangerListApiIn.getHiddenDangerList().forEach(hiddenDangerListApi -> {
            HiddenDangerTemp hiddenDangerTemp = new HiddenDangerTemp();
            Long hiddenDangerId = SnGeneratorUtil.getId();
            hiddenDangerTemp.setId(hiddenDangerId);
            hiddenDangerTemp.setCreateTime(new Date());
            hiddenDangerTemp.setOperator(adminUser.getRealName());
            hiddenDangerTemp.setOperatorId(adminUser.getId());
            BeanUtils.copyProperties(hiddenDangerListApi, hiddenDangerTemp);
            hiddenDangerTemp.setPictures(JSON.toJSONString(hiddenDangerListApi.getPictures()));
            hiddenDangerTemp.setFiles(JSON.toJSONString(hiddenDangerListApi.getFiles()));
            hiddenDangerTemp.setStandard(JSON.toJSONString(hiddenDangerListApi.getStandard()));
            hiddenDangerTemp.setPossibleConsequence(JSON.toJSONString(hiddenDangerListApi.getPossibleConsequence()));
            hiddenDangerTemp.setStatus(hiddenDangerListApi.getStatus().name());
            hiddenDangerTemp.setIsWrongSubmit(false);
            hiddenDangerTemp.setIsFindByCallback(false);

            OperationLogsTemp operationLogsTemp = new OperationLogsTemp();
            operationLogsTemp.setId(SnGeneratorUtil.getId());
            operationLogsTemp.setCreateTime(new Date());
            operationLogsTemp.setCorporationId(hiddenDangerListApi.getCorporationId());
            operationLogsTemp.setHiddenDangerId(hiddenDangerId);
            operationLogsTemp.setOperationType(OperationType.submit.name());
            operationLogsTemp.setInChargeName(adminUser.getRealName());
            operationLogsTemp.setOperatorId(adminUser.getId());
            operationLogsTemp.setOperator(adminUser.getRealName());

            hiddenDangerTempService.submitHiddenDanger(hiddenDangerTemp, operationLogsTemp);

            RectificationTemp rectificationTemp = new RectificationTemp();
            rectificationTemp.setId(SnGeneratorUtil.getId());
            rectificationTemp.setCreateTime(new Date());
            rectificationTemp.setHiddenDangerId(hiddenDangerId);
            rectificationTemp.setInChargeName(adminUser.getRealName());
            rectificationTemp.setPictures(JSON.toJSONString(hiddenDangerListApi.getRectPictures()));
            rectificationTemp.setDescription(hiddenDangerListApi.getRectDescription());
            rectificationTemp.setIsSubmit(true);

            OperationLogsTemp rectificationLogsTemp = new OperationLogsTemp();
            rectificationLogsTemp.setId(SnGeneratorUtil.getId());
            rectificationLogsTemp.setCreateTime(new Date());
            rectificationLogsTemp.setCorporationId(hiddenDangerTemp.getCorporationId());
            rectificationLogsTemp.setHiddenDangerId(hiddenDangerId);
            rectificationLogsTemp.setOperationType(OperationType.rectification.name());
            rectificationLogsTemp.setInChargeName(adminUser.getRealName());
            rectificationLogsTemp.setOperatorId(adminUser.getId());
            rectificationLogsTemp.setOperator(adminUser.getRealName());

            rectificationTempService.submitRectification(rectificationTemp, rectificationLogsTemp);

            VerificationTemp verificationTemp = new VerificationTemp();
            verificationTemp.setId(SnGeneratorUtil.getId());
            verificationTemp.setCreateTime(new Date());
            verificationTemp.setHiddenDangerId(hiddenDangerId);
            verificationTemp.setInChargeName(adminUser.getRealName());
            verificationTemp.setIsPass(hiddenDangerListApi.getIsPass());
            verificationTemp.setReason(hiddenDangerListApi.getReason());
            verificationTemp.setPictures(JSON.toJSONString(hiddenDangerListApi.getRectPictures()));

            OperationLogsTemp verificationLogsTemp = new OperationLogsTemp();
            verificationLogsTemp.setId(SnGeneratorUtil.getId());
            verificationLogsTemp.setCreateTime(new Date());
            verificationLogsTemp.setCorporationId(hiddenDangerTemp.getCorporationId());
            verificationLogsTemp.setHiddenDangerId(hiddenDangerId);
            verificationLogsTemp.setOperationType(OperationType.verification.name());
            verificationLogsTemp.setInChargeName(adminUser.getRealName());
            verificationLogsTemp.setOperatorId(adminUser.getId());
            verificationLogsTemp.setOperator(adminUser.getRealName());

            verificationTempService.submitVerification(verificationTemp, verificationLogsTemp);
        });

        return CommonResult.successResult("保存成功");
    }

    @ApiOperation(value = "更新计划安排的审查员信息")
    @PostMapping(value = "/updateArrangedAuditorByProject")
    public CommonResult updateArrangedAuditorByProject(@RequestBody ArrangedAuditorApiIn arrangedAuditorApiIn){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(arrangedAuditorApiIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        HiddenDanger hiddenDanger = new HiddenDanger();
        hiddenDanger.setArrangedAuditorId(arrangedAuditorApiIn.getArrangedAuditorId());
        hiddenDanger.setArrangedAuditor(arrangedAuditorApiIn.getArrangedAuditor());

        hiddenDangerService.updateArrangedAuditorByProject(arrangedAuditorApiIn.getProjectId(), hiddenDanger);

        return CommonResult.successResult(null);
    }

    @ApiOperation(value = "更新隐患中公司名称")
    @PostMapping(value = "/updateCorpName")
    public CommonResult updateCorpName(@RequestBody CorpNameApiIn corpNameApiIn){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(corpNameApiIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        HiddenDanger hiddenDanger = new HiddenDanger();
        hiddenDanger.setCorpName(corpNameApiIn.getCorpName());

        hiddenDangerService.updateCorpName(corpNameApiIn.getCorporationId(), hiddenDanger);

        return CommonResult.successResult(null);
    }

    @ApiOperation(value = "获取隐患分组列表")
    @GetMapping(value = "/getHiddenDangerGroupedList")
    public CommonResult<List<HiddenDangerGroupedOut>> getHiddenDangerGroupedList(HiddenDangerListApiGetIn hiddenDangerListGetIn){

        List<HiddenDanger> hiddenDangerList = hiddenDangerService.getHiddenDangerGroupedList(hiddenDangerListGetIn);
        List<HiddenDangerGroupedOut> outList = BeanUtils.copyToOutList(hiddenDangerList, HiddenDangerGroupedOut.class);

        return CommonResult.successData(outList);
    }

//    @ApiOperation(value = "获取隐患企业分组列表")
//    @GetMapping(value = "/getHDCropGroupedList")
//    public CommonResult<List<HiddenDangerGroupedOut>> getHDCropGroupedList(CommonPage commonPage,Long projectId, String corpName, Integer hasMajorHd){
//
//        List<HiddenDanger> hiddenDangerList = hiddenDangerService.getHDCropGroupedList(projectId,corpName);
//
//        return CommonResult.successData(outList);
//    }

    @ApiOperation(value = "检查危害因素分类是否已使用")
    @GetMapping(value = "/checkRiskFactorTypeUsed")
    public CommonResult<Boolean> checkRiskFactorTypeUsed(LabelValue labelValue){

        Boolean isUsed = hiddenDangerService.checkRiskFactorTypeUsed(labelValue);

        return CommonResult.successData(isUsed);
    }

    @ApiOperation(value = "推荐的专项类别")
    @PostMapping(value = "/recommendSpecialCategory")
    public CommonResult<List<RecommendSpecialCategoryOut>> recommendSpecialCategory(@RequestBody Set<Long> checkContentIdSet){
        List<RecommendSpecialCategoryOut> list = hiddenDangerMapper.recommendSpecialCategory(checkContentIdSet);
        return CommonResult.successData(list);
    }

    @ApiOperation(value = "推荐的危害因素分类")
    @PostMapping(value = "/recommendRiskFactorType")
    public CommonResult<List<RecommendSpecialCategoryOut>> recommendRiskFactorType(@RequestBody Set<Long> checkContentIdSet){
        List<RecommendSpecialCategoryOut> list = hiddenDangerMapper.recommendRiskFactorType(checkContentIdSet);
        return CommonResult.successData(list);
    }

    @ApiOperation(value = "排名前五的可能造成的后果")
    @PostMapping(value = "/top5PossibleConsequenceName")
    public CommonResult<Map<Long, List<String>>> top5PossibleConsequenceName(@RequestBody Set<Long> checkContentIdSet){
        List<RecommendPossibleConsequenceName> names = hiddenDangerMapper.top5PossibleConsequenceName(checkContentIdSet);
        Map<Long, List<String>> collect = names.stream().collect(Collectors.groupingBy(RecommendPossibleConsequenceName::getCheckContentId, Collectors.mapping(RecommendPossibleConsequenceName::getPossibleConsequenceName, Collectors.toList())));
        return CommonResult.successData(collect);
    }
}
