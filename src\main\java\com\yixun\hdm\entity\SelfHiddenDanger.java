package com.yixun.hdm.entity;



import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.hdm.bean.common.LabelValueInfo;
import com.yixun.hdm.convert.LabelValueListTypeHandler;
import com.yixun.hdm.convert.ListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自查隐患详情表(SelfHiddenDanger)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-27 17:02:50
 */
@SuppressWarnings("serial")
@Data
@ApiModel("自查隐患详情表")
@TableName(value = "self_hidden_danger",autoResultMap = true)
public class SelfHiddenDanger extends Model<SelfHiddenDanger> {

    @NotNull(message="[${column.comment}]不能为空")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message="[创建时间]不能为空")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间起始值")
    private Date createTimeMin;

    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间终止值")
    private Date createTimeMax;

    @ApiModelProperty("查报人员id")
    private Long creatorId;

    @ApiModelProperty("查报人员")
    private String creatorName;

    @ApiModelProperty("项目id")
    @NotNull(message="[项目id]不能为空")
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("公司id")
    @NotNull(message="[公司id]不能为空")
    private Long corpId;

    @ApiModelProperty("公司名称")
    private String corpName;

    @ApiModelProperty("隐患状态;1:待整改;2:整改中;3:待验证;4已验证")
    private Integer status;

    @TableField(exist = false)
    @ApiModelProperty("隐患状态")
    private String statusStr;

    @ApiModelProperty("隐患照片（可多图）")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> pictures;

    @ApiModelProperty("隐患区域")
    private String hdRegion;

    @ApiModelProperty("隐患描述")
    private String description;

    @TableField(typeHandler = LabelValueListTypeHandler.class)
    @ApiModelProperty("危害因素分类明细")
    private List<LabelValueInfo> riskFactorTypeDetail;

    @TableField(exist = false)
    @ApiModelProperty("危害因素分类明细")
    private String riskFactorTypeDetailStr;

    @TableField(typeHandler = ListTypeHandler.class)
    @ApiModelProperty("可能导致后果")
    private List<String> possibleConsequence;

    @TableField(exist = false)
    @ApiModelProperty("可能导致后果")
    private String possibleConsequenceStr;

    @ApiModelProperty("隐患门类")
    private String type;

    @ApiModelProperty("隐患级别")
    private String level;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("整改期限")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date rectifyDeadline;

    @ApiModelProperty("整改人员ID")
    private Long rectifyUserId;

    @ApiModelProperty("整改人员名称")
    private String rectifyUserName;

    @ApiModelProperty("整改措施")
    private String rectifyMeasures;

    @ApiModelProperty("整改资金（元）")
    private Double rectifyFund;

    @ApiModelProperty("整改完成时间")
    private Date rectifyFinishTime;

    @ApiModelProperty("整改照片")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> rectifyPictures;

    @ApiModelProperty("消除状态;0:未消除；1：已消除")
    private Integer eliminateStatus;

    @TableField(exist = false)
    @ApiModelProperty("消除状态")
    private String eliminateStatusStr;

    @ApiModelProperty("验证时间")
    private Date verifyTime;

    @ApiModelProperty("验证描述")
    private String verifyDesc;

    @ApiModelProperty("验证照片")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> verifyPictures;

    @ApiModelProperty("验证人员ID")
    private Long verifyUserId;

    @ApiModelProperty("验证人员名称")
    private String verifyUserName;

    public void setEliminateStatus(Integer eliminateStatus) {
        this.eliminateStatus = eliminateStatus;
        switch (eliminateStatus){
            case 0:
                this.eliminateStatusStr = "未消除";
                break;
            case 1:
                this.eliminateStatusStr = "已消除";
                break;
            default:
                this.eliminateStatusStr = "";
        }
    }
    public void setStatus(Integer status) {
        this.status = status;
        switch (status){
            case 1:
                this.statusStr = "待整改";
                break;
            case 2:
                this.statusStr = "整改中";
                break;
            case 3:
                this.statusStr = "待验证";
                break;
            case 4:
                this.statusStr = "已验证";
                break;
            default:
                this.statusStr = "";
        }
    }

    public void setRiskFactorTypeDetail(List<LabelValueInfo> riskFactorTypeDetail) {
        this.riskFactorTypeDetail = riskFactorTypeDetail;
        if(riskFactorTypeDetail != null && !riskFactorTypeDetail.isEmpty()){
            this.riskFactorTypeDetailStr = riskFactorTypeDetail.stream().map(LabelValueInfo::getLabel).collect(Collectors.joining("/"));
        }else {
            this.riskFactorTypeDetailStr = "";
        }
    }

    public void setPossibleConsequence(List<String> possibleConsequence) {
        this.possibleConsequence = possibleConsequence;
        if(possibleConsequence != null && !possibleConsequence.isEmpty()){
            this.possibleConsequenceStr = String.join(",", possibleConsequence);
        }else {
            this.possibleConsequenceStr = "";
        }
    }
}

