package com.yixun.hdm.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.in.RegulationsMemoIn;
import com.yixun.hdm.bean.out.RegulationsMemoOut;
import com.yixun.hdm.entity.RegulationsMemo;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.RegulationsMemoService;
import com.yixun.hdm.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "admin法规备忘录")
@RestController
@RequestMapping("/admin/regulationsMemo")
public class AdminRegulationsMemoController {

    @Resource
    private RegulationsMemoService regulationsMemoService;

    @RequiredPermission("get:getList:regulationsMemo")
    @GetMapping("/getList")
    @ApiOperation(value = "获取法规备忘录列表")
    public CommonResult<List<RegulationsMemoOut>> getList(CommonPage page){

        Page<RegulationsMemo> regulationsMemoPage = regulationsMemoService.getList(page);
        Page<RegulationsMemoOut> outList = BeanUtils.copyToOutListPage(regulationsMemoPage, RegulationsMemoOut.class);

        return CommonResult.successData(outList);
    }

    @RequiredPermission("update:confirm:regulationsMemo")
    @ApiOperation(value = "确认放入正式库")
    @PostMapping(value = "/confirm/{regulationsMemoId}")
    public CommonResult confirm(@PathVariable("regulationsMemoId") Long regulationsMemoId, @RequestBody RegulationsMemoIn regulationsMemoIn){

        RegulationsMemo regulationsMemo = regulationsMemoService.getById(regulationsMemoId);
        if (regulationsMemo ==null){
            throw new DataErrorException("该法规备忘录不存在");
        }

        BeanUtils.copyProperties(regulationsMemoIn, regulationsMemo, true);

        regulationsMemoService.confirm(regulationsMemo);

        return CommonResult.successResult("操作成功");
    }

    @RequiredPermission("update:update:regulationsMemo")
    @ApiOperation(value = "关联到隐患")
    @PostMapping(value = "/update/{regulationsMemoId}")
    public CommonResult update(@PathVariable("regulationsMemoId") Long regulationsMemoId, @RequestBody RegulationsMemoIn regulationsMemoIn){

        RegulationsMemo regulationsMemo = regulationsMemoService.getById(regulationsMemoId);
        if (regulationsMemo ==null){
            throw new DataErrorException("该法规备忘录不存在");
        }

        BeanUtils.copyProperties(regulationsMemoIn, regulationsMemo, true);

        regulationsMemoService.update(regulationsMemo);

        return CommonResult.successResult("操作成功");
    }

    @RequiredPermission("delete:doDelete:regulationsMemo")
    @ApiOperation(value = "删除法规备忘录关系")
    @PostMapping(value = "/doDelete/{regulationsMemoId}")
    public CommonResult doDelete(@PathVariable("regulationsMemoId") Long regulationsMemoId){

        RegulationsMemo regulationsMemo = regulationsMemoService.getById(regulationsMemoId);
        if (regulationsMemo ==null){
            throw new DataErrorException("法规备忘录关系不存在");
        }

        regulationsMemoService.removeById(regulationsMemoId);

        return CommonResult.successResult("删除成功");
    }

}
