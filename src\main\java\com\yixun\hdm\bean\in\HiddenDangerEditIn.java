package com.yixun.hdm.bean.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.hdm.entity.HdAudit;
import com.yixun.hdm.entity.HdAuditLog;
import com.yixun.hdm.entity.HiddenDangerSuggestion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class HiddenDangerEditIn{


    @ApiModelProperty(value="编辑隐患信息")
    private HiddenDangerIn hiddenDangerIn;

    @ApiModelProperty(value="隐患标注信息")
    private HdAuditLogIn hdAuditLog;

}
