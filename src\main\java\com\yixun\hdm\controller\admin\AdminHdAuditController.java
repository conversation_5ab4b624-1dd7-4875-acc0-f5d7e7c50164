package com.yixun.hdm.controller.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.common.ErrorMessage;
import com.yixun.hdm.bean.in.*;
import com.yixun.hdm.bean.out.HdAuditOut;
import com.yixun.hdm.bean.out.HiddenDangerOut;
import com.yixun.hdm.bean.out.StaffEvaluationOut;
import com.yixun.hdm.entity.*;
import com.yixun.hdm.entity.dto.CorporationProjectDto;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.entity.em.AuditStatus;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.exception.ParameterErrorException;
import com.yixun.hdm.service.*;
import com.yixun.hdm.utils.AdminUserHelper;
import com.yixun.hdm.utils.BeanUtils;
import com.yixun.hdm.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "admin隐患审查")
@RestController
@RequestMapping(value = "/admin/hdAudit")
public class AdminHdAuditController {

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource
    private HdAuditService hdAuditService;

    @Resource
    private HdAuditLogService hdAuditLogService;

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private StaffEvaluationService staffEvaluationService;

    @Autowired
    SecurityTankApiService securityTankApiService;

    @RequiredPermission("get:getAuditList:hiddenDanger")
    @ApiOperation(value = "获取隐患审查列表")
    @GetMapping(value = "/getAuditList")
    public CommonResult<List<HdAuditOut>> getAuditList(HdAuditListGetIn hdAuditListGetIn, CommonPage page){

        Page<HdAudit> hdAuditPage = hdAuditService.getAuditPage(hdAuditListGetIn, page);
        Page<HdAuditOut> outPage = BeanUtils.copyToOutListPage(hdAuditPage, HdAuditOut.class);

        return CommonResult.successData(outPage);
    }

    @ApiOperation(value = "获取隐患提交人")
    @GetMapping(value = "/getAuditSubmitter")
    public CommonResult<Long> getAuditSubmitter(@RequestParam Long corporationId){

        Long submitterId = hdAuditService.getAuditSubmitter(corporationId);

        return CommonResult.successData(submitterId);
    }

    @RequiredPermission("get:getCorpAuditList:hiddenDanger")
    @ApiOperation(value = "获取公司待审查列表")
    @GetMapping(value = "/getCorpAuditList")
    public CommonResult<List<HiddenDangerOut>> getCorpAuditList(HdCorpAuditListGetIn hdCorpAuditListGetIn, CommonPage page){

        if (hdCorpAuditListGetIn.getHdAuditId()==null){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        HdAudit hdAudit = hdAuditService.getById(hdCorpAuditListGetIn.getHdAuditId());
        List<Long> submitList = JSON.parseArray(hdAudit.getSubmitList(), Long.class);
        Page<HiddenDanger> hiddenDangerList = hiddenDangerService.getCorpAuditList(submitList,
                hdCorpAuditListGetIn.getAuditStatus(), page);
        Page<HiddenDangerOut> outPage = BeanUtils.copyToOutListPage(hiddenDangerList, HiddenDangerOut.class);

        return CommonResult.successData(outPage);
    }

    /**
     * 接口逻辑前提：提交审查的都是同一个项目，同一个公司的隐患id列表。
     */
    @Deprecated
    @RequiredPermission("update:submitAudit:hiddenDanger")
    @ApiOperation(value = "提交审查")
    @PostMapping(value = "/submitAudit")
    public CommonResult submitAudit(@RequestBody HdIdListIn hdIdListIn){

        if (hdIdListIn.getHdIdList()==null || hdIdListIn.getHdIdList().isEmpty()){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        hdAuditService.submitAudit(hdIdListIn.getHdIdList());

        return CommonResult.successResult("操作成功");
    }

    @RequiredPermission("update:setRelease:hiddenDanger")
    @ApiOperation(value = "隐患发布")
    @PostMapping(value = "/setRelease")
    public CommonResult setRelease(@RequestBody HdIdListIn hdIdListIn){

        if (hdIdListIn.getHdIdList()==null || hdIdListIn.getHdIdList().isEmpty()){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        List<HiddenDanger> hiddenDangerList = hiddenDangerService.getAuditedList(hdIdListIn.getHdIdList());
        hdAuditService.checkHdCompletion(hiddenDangerList);

        List<Long> hdIds = hiddenDangerList.stream().map(HiddenDanger::getCorporationId).distinct().collect(Collectors.toList());
        if (hdIds.size()>1){
            throw new DataErrorException("请一次发布只针对一家公司");
        }

        for (HiddenDanger hiddenDanger : hiddenDangerList){
            hiddenDanger.setIsNeedEdit(false);
            hiddenDanger.setAuditStatus(AuditStatus.Released.name());
        }

        if (!hiddenDangerList.isEmpty()){
            hiddenDangerService.updateBatchById(hiddenDangerList);
            staffEvaluationService.setRelease(hiddenDangerList.get(0).getCorporationId(), hiddenDangerList.get(0).getProjectId());
        }

        return CommonResult.successResult("操作成功");
    }

    @RequiredPermission("update:setAudit:hiddenDanger")
    @ApiOperation(value = "审查隐患")
    @PostMapping(value = "/setAudit")
    public CommonResult setAudit(@RequestBody HdAuditIn hdAuditIn){

        if (hdAuditIn.getHdAuditId()==null || hdAuditIn.getHiddenDangerId()==null || hdAuditIn.getAuditStatus()==null){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        HiddenDanger hiddenDanger = hiddenDangerService.getById(hdAuditIn.getHiddenDangerId());
        if (hiddenDanger==null){
            throw new DataErrorException("未找到该隐患");
        }

        Long userId = AdminUserHelper.getCurrentUserId();
        StaffUserDto adminUser = userCenterService.getAdminUser(userId);
        if (hiddenDanger.getAuditorId()==null) {
            hiddenDanger.setAuditorId(adminUser.getId());
            hiddenDanger.setAuditor(adminUser.getRealName());
        }else if (!hiddenDanger.getAuditorId().equals(userId)){
            throw new DataErrorException("审查人员不匹配");
        }

        BeanUtils.copyProperties(hdAuditIn, hiddenDanger, true);
        if (hdAuditIn.getPictures()!=null){
            hiddenDanger.setPictures(hdAuditIn.getPictures());
        }
        if (hdAuditIn.getFiles()!=null){
            hiddenDanger.setFiles(hdAuditIn.getFiles());
        }
        if (hdAuditIn.getStandard()!=null){
            hiddenDanger.setStandard(JSON.toJSONString(hdAuditIn.getStandard()));
        }
        if (hdAuditIn.getPossibleConsequence()!=null){
            hiddenDanger.setPossibleConsequence(hdAuditIn.getPossibleConsequence());
        }
        if (hdAuditIn.getSpecialCategoryDetail()!=null){
            hiddenDanger.setSpecialCategoryDetail(JSON.toJSONString(hdAuditIn.getSpecialCategoryDetail()));
        }
        if (hdAuditIn.getRiskFactorTypeDetail()!=null){
            hiddenDanger.setRiskFactorTypeDetail(JSON.toJSONString(hdAuditIn.getRiskFactorTypeDetail()));
        }

        hiddenDanger.setAuditStatus(hdAuditIn.getAuditStatus().name());
//        if (hdAuditIn.getAuditStatus().equals(AuditStatus.Audited)){
//            hiddenDanger.setAuditTime(new Date());
//            HdAudit hdAudit = hdAuditService.getById(hdAuditIn.getHdAuditId());
//            if (hdAudit==null){
//                throw new DataErrorException("未找到该审查记录");
//            }
//            StaffUserDto submitter = userCenterService.getAdminUser(hdAudit.getAuditSubmitterId());
//            hdAuditService.setAudit(hdAudit, hdAuditIn.getHiddenDangerId(), adminUser, submitter);
//
//            //记录审查日志
//            HdAuditLog hdAuditLog = new HdAuditLog();
//            BeanUtils.copyProperties(hdAuditIn, hdAuditLog, true);
//            BeanUtils.copyProperties(hiddenDanger, hdAuditLog, true);
//            hdAuditLog.setId(SnGeneratorUtil.getId());
//            hdAuditLog.setCreateTime(new Date());
//            hdAuditLog.setAuditSubmitterId(hdAudit.getAuditSubmitterId());
//            hdAuditLog.setAuditSubmitter(hdAudit.getAuditSubmitter());
//            hdAuditLogService.save(hdAuditLog);
//        }

        JSONObject object = hiddenDangerService.convertRiskFactorTypeDetail(Collections.singletonList(hiddenDanger));
        hiddenDanger.setRiskFactorTypeDetailAlias(object.getString(hiddenDanger.getId() + ""));
        hiddenDangerService.updateById(hiddenDanger);

        return CommonResult.successResult("操作成功");
    }

    @RequiredPermission("get:getStaffEvaluationList:hiddenDanger")
    @ApiOperation(value = "获取隐患管理考核列表")
    @GetMapping(value = "/getStaffEvaluationList")
    public CommonResult<List<StaffEvaluationOut>> getStaffEvaluationList(StaffEvaluationListGetIn staffEvaluationListGetIn, CommonPage page){

        Page<StaffEvaluation> staffEvaluationList = staffEvaluationService.getList(staffEvaluationListGetIn, page);
        Page<StaffEvaluationOut> outPage = BeanUtils.copyToOutListPage(staffEvaluationList, StaffEvaluationOut.class);

        return CommonResult.successData(outPage);
    }

}
