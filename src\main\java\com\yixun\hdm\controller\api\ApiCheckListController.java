package com.yixun.hdm.controller.api;

import cn.hutool.core.bean.BeanUtil;
import com.yixun.hd.api.CheckResultApi;
import com.yixun.hd.bean.out.CheckResultOut;
import com.yixun.hdm.entity.CheckResult;
import com.yixun.hdm.service.CheckResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@RestController
public class ApiCheckListController implements CheckResultApi {

    @Autowired
    private CheckResultService checkResultService;

    @Override
    public List<CheckResultOut> getList(Long taskId) {
        List<CheckResult> list = checkResultService.lambdaQuery().eq(CheckResult::getTaskId, taskId).list();
        return BeanUtil.copyToList(list, CheckResultOut.class);
    }
}
