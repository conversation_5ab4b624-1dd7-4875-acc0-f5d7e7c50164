package com.yixun.hdm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.SecurityTankApiService;
import com.yixun.hdm.utils.OkHttpKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Slf4j
@Service
public class SecurityTankApiServiceImpl implements SecurityTankApiService {

    @Value("${api.securityTankApi}")
    private String securityTankApi;

    @Value("${api.token}")
    private String token;

    @Override
    public JSONObject transferMatch(Map<String, String> map) {
        try {
            JSONObject connPost = OkHttpKit.apiPostWithToken(securityTankApi + "/api/HazardType/transferMatch",
                    JSON.toJSONString(map), token);
            if (connPost.containsKey("code") && connPost.get("code").equals(200)) {
                return connPost.getJSONObject("data");
            } else {
                throw new DataErrorException(connPost.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }
    @Override
    public JSONArray getAllRiskPoint(Map<String, String> map) {
        try {
            JSONObject connPost = OkHttpKit.apiGetWithToken(securityTankApi + "/api/RiskPoint/getAllRiskPoint",
                    map, token);
            if (connPost.containsKey("code") && connPost.get("code").equals(200)) {
                return connPost.getJSONArray("data");
            } else {
                throw new DataErrorException(connPost.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }
}
