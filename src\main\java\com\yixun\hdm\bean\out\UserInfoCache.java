package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

@Data
public class UserInfoCache {

	@JsonSerialize(using = LongJsonSerializer.class)
	private Long id;
	private String phone;
	private String realName;
	private String avatar;
	private String type;
	private String openId;
	private String organization;
	private String position;
	private Boolean isOperator;


}