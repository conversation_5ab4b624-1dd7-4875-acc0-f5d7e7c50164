package com.yixun.hdm.entity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateJsonSerializer;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TaskDto {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createDate;
    @ApiModelProperty("项目id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    @ApiModelProperty("公司id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;
    @ApiModelProperty("公司名称")
    private String corpName;
    @ApiModelProperty("国民经济行业")
    private List economicIndustry;
    @ApiModelProperty("第几轮调研")
    private Integer turn;
    @ApiModelProperty("1-调研 2-培训 3-回访")
    private Integer type;
    @ApiModelProperty("执行开始时间")
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date startDate;
    @ApiModelProperty("执行结束时间")
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date endDate;
    @ApiModelProperty("执行人员id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long executorId;
    @ApiModelProperty("执行人员")
    private String executor;
    @ApiModelProperty("陪同人员")
    private List accompany;
}
