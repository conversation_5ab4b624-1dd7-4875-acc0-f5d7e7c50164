package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.PreciseCheckListIn;
import com.yixun.hdm.entity.CheckResult;
import com.yixun.hdm.entity.SelfCheckRecord;
import com.yixun.hdm.dao.SelfCheckDetailMapper;
import com.yixun.hdm.entity.SelfCheckDetail;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.SelfCheckDetailService;
import com.yixun.hdm.service.SelfHiddenDangerService;
import com.yixun.hdm.service.biz.AiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自查记录详情(SelfCheckDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-26 18:10:14
 */
@Slf4j
@Service("selfCheckDetailService")
public class SelfCheckDetailServiceImpl extends ServiceImpl<SelfCheckDetailMapper, SelfCheckDetail> implements SelfCheckDetailService {

    @Autowired
    private AiService aiService;

    @Autowired
    private SelfHiddenDangerService selfHiddenDangerService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;

    private static final String RECOMMEND_HD_REDIS_KEY_PREFIX = "hdm:self_check:recommend_hd:";

    @Override
    @Transactional
    public void createDetail(SelfCheckRecord selfCheckRecord) {
        PreciseCheckListIn preciseCheckListIn = new PreciseCheckListIn();
        preciseCheckListIn.setProjectId(selfCheckRecord.getProjectId());
        preciseCheckListIn.setProjectName(selfCheckRecord.getProjectName());
        preciseCheckListIn.setCorpId(selfCheckRecord.getCorpId());
        preciseCheckListIn.setCorpName(selfCheckRecord.getCorpName());

        // 构建Redis缓存Key
        String redisKey = RECOMMEND_HD_REDIS_KEY_PREFIX + selfCheckRecord.getProjectId() + ":" + selfCheckRecord.getCorpId();

        // 从Redis的List中弹出第一个元素（左侧弹出），直接强转为List<CheckResult>
        List<CheckResult> selectedResults = (List<CheckResult>) redisTemplate.opsForList().leftPop(redisKey);

        if (selectedResults == null) {
            // Redis中没有数据，调用AI服务获取
            selectedResults = getAndCacheRecommendHd(preciseCheckListIn, redisKey);
        } else {
            log.info("从Redis中弹出推荐隐患组，共{}条数据", selectedResults.size());

            // 如果这是最后一组数据，记录日志
            Long remainingSize = redisTemplate.opsForList().size(redisKey);
            if (remainingSize != null && remainingSize == 0) {
                log.info("Redis中项目{}企业{}的推荐隐患数据已全部使用完毕", selfCheckRecord.getProjectId(), selfCheckRecord.getCorpId());
            }
        }

        if(selectedResults == null || selectedResults.isEmpty()){
            throw new DataErrorException("推送失败，未获取到检查清单");
        }

        // 保存到数据库
        selectedResults.forEach(checkResult -> {
            SelfCheckDetail detail = new SelfCheckDetail();
            detail.setCheckRecordId(selfCheckRecord.getId());
            detail.setRecommendReason(checkResult.getRecommendReason());
            detail.setRecommendDesc(checkResult.getRecommendHdDesc());
            save(detail);
        });
    }

    /**
     * 获取并缓存推荐隐患数据
     * @param preciseCheckListIn 请求参数
     * @param redisKey Redis键
     * @return batchFlag值最小的组数据
     */
    private List<CheckResult> getAndCacheRecommendHd(PreciseCheckListIn preciseCheckListIn, String redisKey) {
        // 调用AI服务获取推荐隐患
        List<CheckResult> recommendHd = aiService.getRecommendHdV2(preciseCheckListIn);
        if(recommendHd.isEmpty()){
            return new ArrayList<>();
        }

        // 按batchFlag分组
        Map<String, List<CheckResult>> batchGroups = recommendHd.stream()
                .collect(Collectors.groupingBy(CheckResult::getBatchFlag));

        // 获取排序后的batchFlag列表
        List<String> sortedBatchFlags = batchGroups.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        // 获取batchFlag最小的组
        List<CheckResult> minBatchFlagGroup = new ArrayList<>();
        if (!sortedBatchFlags.isEmpty()) {
            // 获取第一个组（batchFlag最小的组）
            String minBatchFlag = sortedBatchFlags.get(0);
            minBatchFlagGroup = batchGroups.get(minBatchFlag);
			log.info("获取到batchFlag={}的推荐隐患组，共{}条数据", minBatchFlag, minBatchFlagGroup.size());

            // 将剩余的组按batchFlag顺序存入Redis的List（从右侧插入，保证小的batchFlag在左侧）
            for (int i = 1; i < sortedBatchFlags.size(); i++) {
                String batchFlag = sortedBatchFlags.get(i);
                List<CheckResult> groupData = batchGroups.get(batchFlag);

                // 直接将List<CheckResult>对象存入Redis
                redisTemplate.opsForList().rightPush(redisKey, groupData);
                log.info("将batchFlag={}的推荐隐患组（{}条数据）存入Redis List", batchFlag, groupData.size());
            }
        } else {
            return recommendHd;
        }

        return minBatchFlagGroup;
    }

    @Override
    public Page<SelfCheckDetail> pageList(CommonPage commonPage, SelfCheckDetail selfCheckDetail) {
        Page<SelfCheckDetail> page = new Page<>(commonPage.getPageNum(),commonPage.getPageSize());
        page(page, new QueryWrapper<>(selfCheckDetail));
        page.getRecords().forEach(detail -> {
            if(detail.getSelfHdId() != null){
                detail.setSelfHiddenDanger(selfHiddenDangerService.getById(detail.getSelfHdId()));
            }
        });
        return page;
    }
}

