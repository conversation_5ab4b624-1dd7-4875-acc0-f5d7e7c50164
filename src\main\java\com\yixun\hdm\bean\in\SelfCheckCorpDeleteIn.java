package com.yixun.hdm.bean.in;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 自查企业(SelfCheckCorp)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-26 16:38:31
 */
@SuppressWarnings("serial")
@Data
public class SelfCheckCorpDeleteIn {

    @ApiModelProperty("主键ID")
    @NotNull(message="[主键ID]不能为空")
    private Long id;

    @ApiModelProperty("删除模式；1：不删除记录；2：删除记录")
    @NotNull(message="[删除模式]不能为空")
    private Integer mode;

}
