package com.yixun.hdm.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.yixun.hdm.entity.SelfCheckCorp;

/**
 * 自查企业(SelfCheckCorp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-26 15:54:38
 */
public interface SelfCheckCorpMapper extends BaseMapper<SelfCheckCorp> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<SelfCheckCorp> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<SelfCheckCorp> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<SelfCheckCorp> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<SelfCheckCorp> entities);

}

