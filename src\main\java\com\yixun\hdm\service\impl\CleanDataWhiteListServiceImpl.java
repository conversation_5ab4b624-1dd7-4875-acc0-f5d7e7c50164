package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.CleanDataWhiteListGetIn;
import com.yixun.hdm.entity.CleanDataWhiteList;
import com.yixun.hdm.dao.CleanDataWhiteListMapper;
import com.yixun.hdm.service.CleanDataWhiteListService;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class CleanDataWhiteListServiceImpl extends ServiceImpl<CleanDataWhiteListMapper, CleanDataWhiteList>
    implements CleanDataWhiteListService{

    @Override
    public Page<CleanDataWhiteList> getPageList(CleanDataWhiteListGetIn getIn, CommonPage commonPage) {
        QueryWrapper<CleanDataWhiteList> queryWrapper = new QueryWrapper<>();
        if (getIn.getName()!=null){
            queryWrapper.like("name", getIn.getName());
        }
        if (getIn.getPhone()!=null){
            queryWrapper.eq("phone", getIn.getPhone());
        }
        if (getIn.getCorpName()!=null){
            queryWrapper.like("corp_name", getIn.getCorpName());
        }

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<CleanDataWhiteList>) page(page, queryWrapper);
    }
}




