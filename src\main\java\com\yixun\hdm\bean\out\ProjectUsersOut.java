package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

@Data
public class ProjectUsersOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    private String projectName;

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;

    private String userName;

    private String phone;

    private String email;

    private String position;

    private String job;

    private String avatar;

    private String organization;
}
