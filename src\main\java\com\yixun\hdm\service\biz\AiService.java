package com.yixun.hdm.service.biz;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.ParserConfig;
import com.yixun.hdm.bean.in.PreciseCheckListIn;
import com.yixun.hdm.entity.CheckResult;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.utils.OkHttpKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class AiService {

    @Value("${api.aiPlatformApi}")
    private String aiPlatformApi;

    @Value("${api.recommendStandardUrl}")
    private String recommendStandardUrl;

    @Value("${api.aiYqyc}")
    private String aiYqyc;

    @Value("${api.aiBaseUrl}")
    private String aiBaseUrl;

    @Value("${api.token}")
    private String token;

	@Value("${api.aiRecommendHdV2Url}")
	private String aiRecommendHdV2Url;

    public List<CheckResult> getRecommendHd(PreciseCheckListIn in) {

        // 获取AI推荐隐患
        try {
            JSONObject connPost = OkHttpKit.apiPostWithToken(aiBaseUrl + "/hidden_danger_recommend/query_hds_from_recall_routes",
                    JSON.toJSONString(in), token);
            if ("success".equals(connPost.getString("status"))) {
                JSONArray data = connPost.getJSONArray("data");
                return makeList(data);
            } else {
                throw new DataErrorException("AI服务器返回失败" + connPost.toJSONString());
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

	public List<CheckResult> getRecommendHdV2(PreciseCheckListIn in) {

		// 获取AI推荐隐患
		try {
			log.info("AI推荐隐患请求参数: {}", JSON.toJSONString(in));
			JSONObject connPost = OkHttpKit.apiPostWithToken(aiRecommendHdV2Url,
				JSON.toJSONString(in), token);
			log.info("AI推荐隐患响应: {}", connPost.toJSONString());
			if ("success".equals(connPost.getString("status"))) {
				JSONArray data = connPost.getJSONArray("data");
				return makeListV2(data);
			} else {
				throw new DataErrorException("AI服务器返回失败" + connPost.toJSONString());
			}
		} catch (IOException e) {
			log.error(e.getMessage());
			throw new DataErrorException(e.getMessage());
		}
	}

    public List<String> getRecommendReasonList() {
        // 获取AI推荐隐患
        try {
            JSONObject connPost = OkHttpKit.apiGetWithToken(aiPlatformApi + "/query_recall_types",
                    null, token);
            if ("success".equals(connPost.getString("status"))) {
                return connPost.getJSONArray("data").toJavaList(String.class);
            } else {
                throw new DataErrorException("AI服务器返回失败" + connPost.toJSONString());
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

	private List<CheckResult> makeListV2(JSONArray data){
		List<CheckResult> list = new ArrayList<>();
		for (int i = 0; i < data.size(); i++) {
			JSONObject item = data.getJSONObject(i);
			CheckResult checkResult = new CheckResult();
			checkResult.setRecommendHdId(item.getString("hiddenDangerId"));
			checkResult.setRecommendHdDesc(item.getString("hiddenDangerDescription"));
			checkResult.setRecommendReason(item.getString("hiddenDangerRoutes"));
			checkResult.setRecommendHdArea(item.getString("firstTierVenue"));
			checkResult.setRecommendHdAreaSeq(item.getInteger("firstTierVenueSeq"));
			checkResult.setRecommendHdAddress(item.getString("tierVenue"));
			try {
				JSONArray hurtTypes = item.getJSONArray("hurtTypes");
				hurtTypes.removeIf(o -> o == null || o.toString().trim().isEmpty());
				checkResult.setRecommendHdHurtType(hurtTypes.toJSONString());
			}catch (Exception e){
				checkResult.setRecommendHdHurtType(item.getString("hurtTypes"));
			}
			checkResult.setRecommendHdTierCategory(item.getString("tierVenue"));
			checkResult.setRecommendHdHurtType(item.getString("hurtTypes"));
			checkResult.setRecommendHdAccidentHistoryAbstract(item.getString("accidentHistoryAbstract"));
			checkResult.setRecommendHdAccidentHistory(item.getString("accidentHistory"));
			checkResult.setRecommendHdAccidentSid(item.getString("sid"));
			checkResult.setRecommendHdJobActivity(item.getString("operation"));
			checkResult.setRecommendHdWorkCategory(item.getString("job"));
			checkResult.setRecommendHdFirstTierCategory(item.getString("firstTierCategory"));
			checkResult.setRecommendHdSecondTierCategory(item.getString("secondTierCategory"));
			checkResult.setRecommendHdThirdTierCategory(item.getString("secondTierCategory"));
			checkResult.setRecommendAiReason(item.getString("recommendReason"));
			checkResult.setRecommendHdType(item.getString("hiddenDangerType"));
			checkResult.setRecommendHdOrRisk(item.getString("hiddenDangersOrRisks"));
			checkResult.setRecommendFirstLevelClassification(item.getString("firstLevelClassification"));
			checkResult.setRecommendSecondLevelClassification(item.getString("secondLevelClassification"));
			checkResult.setBatchFlag(item.getString("batch_flag"));
			list.add(checkResult);
		}
		return list;
	}

    public JSONArray getRecommendStandard(String desc) {
        // 获取AI推荐检查依据
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("input_description", desc);
        http:
//aiapp.yxgsyf.com:19080/check_option_matcher/
        try {
            return OkHttpKit.apiPostWithTokenArray(recommendStandardUrl + "/recommend_standards",
                    jsonObject.toJSONString(), token);
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    public JSONObject recommendHdByDesc(String desc) {
        // 获取AI推荐检查依据
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("hidden_danger_desc", desc);
        try (HttpResponse execute = HttpUtil.createPost(aiYqyc + "/hidden_danger_by_data")
                .header("secret", "zhesiyigemimi")
                .body(jsonObject.toJSONString())
                .execute()) {
            if (execute.isOk()) {
                String body = execute.body();
                return JSONObject.parseObject(body);
            } else {
                throw new DataErrorException("AI服务器返回失败" + execute.getStatus());
            }
        }
    }

    private List<CheckResult> makeList(JSONArray data){
        List<CheckResult> list = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject item = data.getJSONObject(i);
            CheckResult checkResult = new CheckResult();
            checkResult.setRecommendHdId(item.getString("hiddenDangerId"));
            checkResult.setRecommendHdDesc(item.getString("hiddenDangerDescription"));
            checkResult.setRecommendReason(item.getString("hiddenDangerRoutes"));
            checkResult.setRecommendHdArea(item.getString("firstTierVenue"));
            checkResult.setRecommendHdAreaSeq(item.getInteger("firstTierVenueSeq"));
            checkResult.setRecommendHdAddress(item.getString("tierVenue"));
            try {
                JSONArray hurtTypes = item.getJSONArray("hurtTypes");
                hurtTypes.removeIf(o -> o == null || o.toString().trim().isEmpty());
                checkResult.setRecommendHdHurtType(hurtTypes.toJSONString());
            }catch (Exception e){
                checkResult.setRecommendHdHurtType(item.getString("hurtTypes"));
            }
            checkResult.setRecommendHdTierCategory(item.getString("tierVenue"));
            checkResult.setRecommendHdHurtType(item.getString("hurtTypes"));
            checkResult.setRecommendHdAccidentHistoryAbstract(item.getString("accidentHistoryAbstract"));
            checkResult.setRecommendHdAccidentHistory(item.getString("accidentHistory"));
            checkResult.setRecommendHdAccidentSid(item.getString("sid"));
            checkResult.setRecommendHdJobActivity(item.getString("operation"));
            checkResult.setRecommendHdWorkCategory(item.getString("job"));
            checkResult.setRecommendHdFirstTierCategory(item.getString("firstTierCategory"));
            checkResult.setRecommendHdSecondTierCategory(item.getString("secondTierCategory"));
            checkResult.setRecommendHdThirdTierCategory(item.getString("secondTierCategory"));
            checkResult.setRecommendAiReason(item.getString("recommendReason"));
            checkResult.setRecommendHdType(item.getString("hiddenDangerType"));
            checkResult.setRecommendHdOrRisk(item.getString("hiddenDangersOrRisks"));
            checkResult.setRecommendFirstLevelClassification(item.getString("firstLevelClassification"));
            checkResult.setRecommendSecondLevelClassification(item.getString("secondLevelClassification"));
            checkResult.setRecommendDimension(item.getString("recommendDimension"));
            checkResult.setRecommendDimensionOrder(item.getInteger("recommendDimensionOrder"));
            list.add(checkResult);
        }
        return list;
    }

}
