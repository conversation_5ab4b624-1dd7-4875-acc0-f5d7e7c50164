package com.yixun.hdm.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PreciseCheckListIn {

    @ApiModelProperty("ids，多个使用,分隔")
    private String ids;
    @ApiModelProperty(value="任务id")
    private Long taskId;
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("项目名称")
    private String projectName;
    @ApiModelProperty("公司id")
    private Long corpId;
    @ApiModelProperty("公司名称")
    private String corpName;
    @ApiModelProperty("行业")
    private String industry;
    @ApiModelProperty("社会统一信用代码")
    private String creditCode;
    @ApiModelProperty("推荐检查的原因")
    private String recommendReason;
    @ApiModelProperty("检查结果")
    private Integer result;
    @ApiModelProperty("风险或隐患；风险、隐患")
    private String hiddenDangersOrRisks;
    @ApiModelProperty("推荐隐患一级分类")
    private String recommendFirstLevelClassification;
    @ApiModelProperty("推荐隐患二级分类")
    private String recommendSecondLevelClassification;
    @ApiModelProperty("推荐隐患的场所")
    private String recommendHdTierCategory;
    @ApiModelProperty("推荐隐患类型;精准隐患、工伤隐患")
    private String recommendHdType;
    @ApiModelProperty("推荐隐患的工伤sid")
    private String recommendHdAccidentSid;
    @ApiModelProperty("刷新")
    private Boolean refresh = false;

}
