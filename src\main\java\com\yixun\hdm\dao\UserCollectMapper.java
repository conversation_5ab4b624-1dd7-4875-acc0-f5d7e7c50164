package com.yixun.hdm.dao;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.yixun.hdm.entity.UserCollect;

/**
 * 用户收藏表(UserCollect)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-19 16:10:20
 */
public interface UserCollectMapper extends BaseMapper<UserCollect> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<UserCollect> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<UserCollect> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<UserCollect> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<UserCollect> entities);

}

