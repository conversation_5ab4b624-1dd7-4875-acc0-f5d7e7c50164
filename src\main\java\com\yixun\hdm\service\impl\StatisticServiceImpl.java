package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.out.HdCorpInfoOut;
import com.yixun.hdm.bean.out.ProjectCorpHdStatisticOut;
import com.yixun.hdm.bean.out.statistic.HdCorporationOut;
import com.yixun.hdm.bean.out.statistic.HdCountOut;
import com.yixun.hdm.bean.out.statistic.HdStatusOut;
import com.yixun.hdm.bean.out.statistic.HdTypeOut;
import com.yixun.hdm.dao.StatisticMapper;
import com.yixun.hdm.entity.dto.CorporationProjectDto;
import com.yixun.hdm.entity.dto.YqycInfoDto;
import com.yixun.hdm.service.StatisticService;
import com.yixun.hdm.service.UserCenterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class StatisticServiceImpl implements StatisticService {

    @Resource
    private StatisticMapper statisticMapper;

    @Resource
    private UserCenterService userCenterService;

    @Override
    public List<HdCountOut> getMonthlyTotalCount(Long projectId, Date firstMonth) {

        return statisticMapper.getMonthlyTotalCount(projectId, firstMonth);
    }

    @Override
    public List<HdCountOut> getMonthlyDoneCount(Long projectId, Date firstMonth) {

        return statisticMapper.getMonthlyDoneCount(projectId, firstMonth);
    }

    @Override
    public List<HdTypeOut> getHdTypeByProject(Long projectId, Date startDate, Date endDate) {

        return statisticMapper.getHdTypeByProject(projectId, startDate, endDate);
    }

    @Override
    public List<HdStatusOut> getHdStatusByProject(Long projectId, Date startDate, Date endDate) {

        return statisticMapper.getHdStatusByProject(projectId, startDate, endDate);
    }

    @Override
    public int getHdWrongByProject(Long projectId, Date startDate, Date endDate) {

        return statisticMapper.getHdWrongByProject(projectId, startDate, endDate);
    }

    @Override
    public int getCorpCountByProject(Long projectId) {

        return userCenterService.getCorporationCount(projectId);
    }

    @Override
    public int getHdCountByProject(Long projectId, Date startDate, Date endDate) {

        return statisticMapper.getHdCountByProject(projectId, startDate, endDate);
    }

    @Override
    public List<HdTypeOut> getHdTierOneTypeByProject(Long projectId, Date startDate, Date endDate) {

        return statisticMapper.getHdTierOneTypeByProject(projectId, startDate, endDate);
    }

    @Override
    public List<HdTypeOut> getHdRiskFactorByProject(Long projectId) {

        return statisticMapper.getHdRiskFactorByProject(projectId);
    }

    @Override
    public int getHdLevelCountByProject(Long projectId, String level) {

        return statisticMapper.getHdLevelCountByProject(projectId, level);
    }

    @Override
    public List<HdCorporationOut> getHdCorporationByProject(Long projectId) {

        return statisticMapper.getHdCorporationByProject(projectId);
    }

    @Override
    public Page<HdCorpInfoOut> getHdCorpInfoPage(Long projectId, List<Long> corpIds, CommonPage commonPage) {

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return statisticMapper.getHdCorpInfoPage(page, projectId, corpIds);
    }

    @Override
    public List<HdCorpInfoOut> getHdCorpInfoList(Long projectId, List<Long> corpIds) {

        return statisticMapper.getHdCorpInfoList(projectId, corpIds);
    }

    @Override
    public List<YqycInfoDto> getYqycInfoList(List<HdCorpInfoOut> records) {

        if (records.isEmpty()){
            return new ArrayList<>();
        }

        List<CorporationProjectDto> list = new ArrayList<>();
        records.forEach(r->{
            CorporationProjectDto dto = new CorporationProjectDto();
            dto.setCorporationId(r.getCorporationId());
            dto.setProjectId(r.getProjectId());
            list.add(dto);
        });

        return userCenterService.getYqycInfoList(list);
    }

    @Override
    public Page<ProjectCorpHdStatisticOut> getProjectCorpHdStatistic(List<Long> corpIds, Long projectId, CommonPage commonPage) {

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return statisticMapper.getProjectCorpHdStatistic(page, projectId, corpIds);
    }

    @Override
    public List<ProjectCorpHdStatisticOut> getProjectCorpHdList(List<Long> corpIds, Long projectId) {

        return statisticMapper.getProjectCorpHdCount(projectId, corpIds);
    }

}
