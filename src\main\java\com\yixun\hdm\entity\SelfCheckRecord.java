package com.yixun.hdm.entity;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 自查记录(SelfCheckRecord)表实体类
 *
 * <AUTHOR>
 * @since 2025-01-09 15:00:50
 */
@SuppressWarnings("serial")
@Data
@ApiModel("自查记录")
@TableName("self_check_record")
public class SelfCheckRecord extends Model<SelfCheckRecord> {

    @ApiModelProperty("主键ID")
    @NotNull(message="[主键ID]不能为空")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @ApiModelProperty("检查记录名")
    private String name;

    @ApiModelProperty("检查开始时间")
    private Date startTime;

    @ApiModelProperty("检查结束时间")
    private Date endTime;

    @ApiModelProperty("项目ID")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("企业ID")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corpId;

    @ApiModelProperty("企业名称")
    private String corpName;

    @ApiModelProperty("是否查出隐患；1：有隐患；0：无隐患")
    private Integer hasHd;

    @ApiModelProperty("最近检查时间")
    private Date lastCheckTime;

    @ApiModelProperty("检查人员ID")
    private Long checkUserId;

    @ApiModelProperty("检查人员姓名")
    private String checkUserName;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("检查期限")
    private Date checkDeadline;

    @TableField(exist = false)
    @ApiModelProperty("检查内容数")
    private Integer checkCount;

    @TableField(exist = false)
    @ApiModelProperty("检查合格数")
    private Integer passCount;

    @TableField(exist = false)
    @ApiModelProperty("查出隐患数")
    private Integer hdCount;

    @TableField(exist = false)
    @ApiModelProperty("不涉及数")
    private Integer uninvolvedCount;

    @TableField(exist = false)
    @ApiModelProperty("重复数")
    private Integer repeatCount;

    @TableField(exist = false)
    @ApiModelProperty("未检查数")
    private Integer unCheckCount;

    @TableField(exist = false)
    @ApiModelProperty("检查进度")
    private String checkPercent;

    @ApiModelProperty("创建时间")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

}

