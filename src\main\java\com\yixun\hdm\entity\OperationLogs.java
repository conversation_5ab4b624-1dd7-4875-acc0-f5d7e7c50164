package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 隐患治理历史
 * @TableName hdm_operation_logs
 */
@TableName(value ="hdm_operation_logs")
@Data
public class OperationLogs implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Date createTime;

    /**
     * 隐患id
     */
    private Long hiddenDangerId;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 公司id
     */
    private Long corporationId;

    /**
     * 负责人
     */
    private String inChargeName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}