<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yixun.hdm.dao.StaffEvaluationMapper">
    <select id="getList" resultType="com.yixun.hdm.entity.StaffEvaluation">
        select * from hdm_staff_evaluation
        <where>
            <if test="surveyManager!=null">
                and survey_manager = #{surveyManager}
            </if>
            <if test="auditor!=null">
                and auditor like CONCAT('%',#{auditor},'%')
            </if>
            <if test="surveyMonthStart!=null">
                and survey_date between #{surveyMonthStart} and #{surveyMonthEnd}
            </if>
            <if test="reportMonthStart!=null">
                and release_time between #{reportMonthStart} and #{reportMonthEnd}
            </if>
            <if test="isSubmitCountOK==true">
                and first_submit_count >= 25
            </if>
            <if test="isSubmitCountOK==false">
                and 25 > first_submit_count
            </if>
            <if test="isAuditExpired==true">
                and audit_finish_time > audit_expired_time
            </if>
            <if test="isAuditExpired==false">
                and audit_expired_time > audit_finish_time
            </if>
            <if test="isSubmitExpired==true">
                and submit_time > submit_expired_time
            </if>
            <if test="isSubmitExpired==false">
                and submit_expired_time > submit_time
            </if>
            <if test="isReleaseExpired==true">
                and release_time > release_expired_time
            </if>
            <if test="isReleaseExpired==false">
                and release_expired_time > release_time
            </if>
        </where>
    </select>
</mapper>