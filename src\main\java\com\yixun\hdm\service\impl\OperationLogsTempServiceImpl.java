package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.dao.OperationLogsTempMapper;
import com.yixun.hdm.entity.OperationLogsTemp;
import com.yixun.hdm.service.OperationLogsTempService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class OperationLogsTempServiceImpl extends ServiceImpl<OperationLogsTempMapper, OperationLogsTemp>
    implements OperationLogsTempService{

    @Override
    public List<OperationLogsTemp> getByCorporation(Long corporationId) {
        QueryWrapper<OperationLogsTemp> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("corporation_id", corporationId);
        return list(queryWrapper);
    }
}




