package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.List;

@Data
public class CustomerUserOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    private String phone;
    private String realName;
    private String avatar;
    private String email;
    private String type;
    private List<UserProjectOut> userOrganizationList;

}
