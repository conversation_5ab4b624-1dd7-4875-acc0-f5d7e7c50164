package com.yixun.hdm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yixun.hdm.bean.weixin.AccessToken;
import com.yixun.hdm.bean.weixin.JsApiTicket;
import com.yixun.hdm.bean.weixin.MiniSession;
import com.yixun.hdm.bean.weixin.WeixinUserInfo;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.WeixinService;
import com.yixun.hdm.utils.OkHttpKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class WeixinServiceImpl implements WeixinService {

    private static final Logger log = LoggerFactory.getLogger(WeixinServiceImpl.class);

    private String baseUrl = "https://api.weixin.qq.com/sns";

    @Value("${weixin.appId}")
    private String appId;

    @Value("${weixin.appSecret}")
    private String appSecret;

    @Resource(name="customRedisTemplate")
    private RedisTemplate redisTemplate;

    @Override
    public WeixinUserInfo getWeixinUserInfo(String weixinCode, String loginType, String deviceType) {
        //根据code获取AccessToken
        AccessToken token = getAccessToken(weixinCode, loginType, deviceType);
        String accessToken = token.getAccess_token();
        String openId = token.getOpenid();

        //获取用户信息
        Map<String, String> params = new HashMap<>();
        params.put("access_token", accessToken);
        params.put("openid", openId);
        try {
            JSONObject connGet = OkHttpKit.connGet(baseUrl + "/userinfo", params);
            if (connGet.containsKey("errcode")) {
                throw new DataErrorException("获取微信用户信息出错: " + connGet.getString("errmsg"));
            } else {
                WeixinUserInfo userInfo = connGet.toJavaObject(WeixinUserInfo.class);
                if (StringUtils.isEmpty(userInfo.getUnionid())) {
                    throw new DataErrorException("获取微信UnionId失败");
                }
                return userInfo;
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException("获取微信用户信息出错");
        }
    }


    public AccessToken getAccessToken(String weixinCode, String loginType, String deviceType) {

        String accessTokenKey = "AccessToken:"+weixinCode;
        AccessToken accessToken = (AccessToken) redisTemplate.opsForValue().get(accessTokenKey);
        if (accessToken == null) {
            Map<String, String> params = new HashMap<>();
            params.put("appId", appId);
            params.put("secret", appSecret);
            params.put("code", weixinCode);
            params.put("grant_type", "authorization_code");
            try {
                JSONObject connGet = OkHttpKit.connGet(baseUrl + "/oauth2/access_token", params);
                if (connGet.containsKey("errcode")) {
                    throw new DataErrorException("获取token出错: " + connGet.getString("errmsg"));
                } else {
                    accessToken = connGet.toJavaObject(AccessToken.class);
                }
            } catch (IOException e) {
                log.error(e.getMessage());
                throw new DataErrorException("获取token出错");
            }
            redisTemplate.opsForValue().set(accessTokenKey, accessToken, 7200, TimeUnit.SECONDS);
        }
        return accessToken;
    }

    @Override
    public AccessToken getJSAccessToken() {

        AccessToken accessToken = null;

        Map<String, String> params = new HashMap<>();
        params.put("appId", appId);
        params.put("secret", appSecret);
        params.put("grant_type", "client_credential");
        try {
            JSONObject connGet = OkHttpKit.connGet("https://api.weixin.qq.com/cgi-bin/token", params);
            if (connGet.containsKey("errcode")) {
                throw new DataErrorException("获取accessToken出错: " + connGet.getString("errmsg"));
            } else {
                accessToken = connGet.toJavaObject(AccessToken.class);
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }

        return accessToken;
    }

    @Override
    public JsApiTicket getJSApiTicket() {

        String jsApiTicketTokenKey = "JsApiTicketToken";
        JsApiTicket jsApiTicket = (JsApiTicket) redisTemplate.opsForValue().get(jsApiTicketTokenKey);
        if (jsApiTicket == null) {
            Map<String, String> params = new HashMap<>();
            params.put("access_token", getJSAccessToken().getAccess_token());
            params.put("type", "jsapi");
            try {
                JSONObject connGet = OkHttpKit.connGet("https://api.weixin.qq.com/cgi-bin/ticket/getticket", params);
                if (connGet.containsKey("errcode") && !connGet.getString("errcode").equals("0")) {
                    throw new DataErrorException("获取jsApiTicket出错: " + connGet.getString("errmsg"));
                } else {
                    jsApiTicket = connGet.toJavaObject(JsApiTicket.class);
                    redisTemplate.opsForValue().set(jsApiTicketTokenKey, jsApiTicket, 7200, TimeUnit.SECONDS);
                }
            } catch (IOException e) {
                log.error(e.getMessage());
                throw new DataErrorException(e.getMessage());
            }
        }

        return jsApiTicket;
    }

    @Override
    public MiniSession getMiniSessionByCode(String weixinCode) {
        String sessionKey = "MiniSession:"+weixinCode;
        MiniSession miniSession = (MiniSession) redisTemplate.opsForValue().get(sessionKey);
        if (miniSession == null) {
            miniSession = getMiniSession(weixinCode);
            redisTemplate.opsForValue().set(sessionKey, miniSession, 2, TimeUnit.HOURS);
            redisTemplate.opsForValue().set("MiniOpenId:"+miniSession.getOpenid(), miniSession, 2, TimeUnit.HOURS);
        }
        return miniSession;
    }

    @Override
    public MiniSession getMiniSessionByOpenId(String openId) {
        MiniSession miniSession = (MiniSession) redisTemplate.opsForValue().get("MiniOpenId:"+openId);
        if (miniSession == null) {
            throw new DataErrorException("获取微信信息错误，请重新登录。");
        }
        return miniSession;
    }

    @Override
    public void pushWxMiniMessage(String openId, String templateId, String data) {
        Map<String, Object> params = new HashMap<>();
        params.put("touser", openId);
        params.put("template_id", templateId);
        params.put("data", JSONObject.parseObject(data));
        try {
            JSONObject connPost = OkHttpKit.connPostInJson("https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token="
                    + getJSAccessToken().getAccess_token(), JSONObject.toJSONString(params));
            if (connPost.containsKey("errcode") && !connPost.getString("errcode").equals("0")) {
                throw new DataErrorException("推送小程序消息出错: " + connPost.getString("errmsg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    private MiniSession getMiniSession(String weixinCode) {
        Map<String, String> params = new HashMap<>();
        params.put("appid", appId);
        params.put("secret", appSecret);
        params.put("js_code", weixinCode);
        params.put("grant_type", "authorization_code");
        try {
            JSONObject connGet = OkHttpKit.connGet("https://api.weixin.qq.com/sns/jscode2session", params);
            if (connGet.containsKey("errcode") && !connGet.getString("errcode").equals("0")) {
                throw new DataErrorException("获取MiniSession出错: " + connGet.getString("errmsg"));
            } else {
                return connGet.toJavaObject(MiniSession.class);
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

}
