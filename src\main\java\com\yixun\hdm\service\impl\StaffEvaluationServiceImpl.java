package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.api.ProjectConfigApi;
import com.yixun.bean.out.YqycProjectConfig;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.StaffEvaluationListGetIn;
import com.yixun.hdm.dao.StaffEvaluationMapper;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.StaffEvaluation;
import com.yixun.hdm.service.StaffEvaluationService;
import com.yixun.hdm.service.UserCenterService;
import com.yixun.hdm.utils.SnGeneratorUtil;
import com.yixun.hdm.utils.WorkDayUtils;
import com.yixun.teacher.api.TaskApi;
import com.yixun.teacher.bean.out.TaskOut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

/**
 *
 */
@Service
public class StaffEvaluationServiceImpl extends ServiceImpl<StaffEvaluationMapper, StaffEvaluation>
    implements StaffEvaluationService{

    @Autowired
    private TaskApi taskApi;

    @Autowired
    private ProjectConfigApi projectConfigApi;

    @Override
    public void init(HiddenDanger hiddenDanger, int hdCount) {

        StaffEvaluation staffEvaluation = getByCidPid(hiddenDanger.getCorporationId(), hiddenDanger.getProjectId());
        if (staffEvaluation==null){
            TaskOut task = taskApi.getById(hiddenDanger.getTaskId());
            String name = task.getExecutor();
            Date date = task.getStartDate();
            staffEvaluation = new StaffEvaluation();
            staffEvaluation.setId(SnGeneratorUtil.getId());
            staffEvaluation.setCreateTime(new Date());
            staffEvaluation.setProjectId(hiddenDanger.getProjectId());
            staffEvaluation.setProjectName(hiddenDanger.getProjectName());
            staffEvaluation.setCorporationId(hiddenDanger.getCorporationId());
            staffEvaluation.setCorpName(hiddenDanger.getCorpName());
            staffEvaluation.setSurveyManager(name);
            staffEvaluation.setSurveyDate(date);
            staffEvaluation.setFirstSubmitCount(hdCount);
            staffEvaluation.setSubmitTime(new Date());
            YqycProjectConfig info = projectConfigApi.info(hiddenDanger.getProjectId());
            Integer hdSubmitDeadline = 1;
            if(info != null && info.getHdSubmitDeadline() != null){
                hdSubmitDeadline = info.getHdSubmitDeadline();
            }
            Integer hdAuditDeadline = 2;
            if(info != null && info.getHdAuditDeadline() != null){
                hdAuditDeadline = info.getHdAuditDeadline();
            }
            staffEvaluation.setSubmitExpiredTime(WorkDayUtils.getWorkDate2(date, hdSubmitDeadline));
            staffEvaluation.setAuditExpiredTime(WorkDayUtils.getWorkDate2(new Date(), hdAuditDeadline));
            staffEvaluation.setReleaseExpiredTime(WorkDayUtils.getWorkDate2(date, 4));

            save(staffEvaluation);
        }

    }

    @Override
    public void setFinish(Long corporationId, Long projectId, String auditor) {
        StaffEvaluation staffEvaluation = getByCidPid(corporationId, projectId);
        if (staffEvaluation!=null){
            staffEvaluation.setAuditor(auditor);
            staffEvaluation.setAuditFinishTime(new Date());
            updateById(staffEvaluation);
        }
    }

    @Override
    public void setRelease(Long corporationId, Long projectId) {
        StaffEvaluation staffEvaluation = getByCidPid(corporationId, projectId);
        if (staffEvaluation!=null){
            staffEvaluation.setReleaseTime(new Date());
            updateById(staffEvaluation);
        }
    }

    @Override
    public Page<StaffEvaluation> getList(StaffEvaluationListGetIn getIn, CommonPage commonPage) {

        Date surveyMonthStart = null;
        Date surveyMonthEnd = null;
        if (getIn.getSurveyMonth()!=null){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(getIn.getSurveyMonth());
            calendar.add(Calendar.MONTH, 1);
            surveyMonthStart = getIn.getSurveyMonth();
            surveyMonthEnd = calendar.getTime();
        }
        Date reportMonthStart = null;
        Date reportMonthEnd = null;
        if (getIn.getReportMonth()!=null){
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(getIn.getReportMonth());
            calendar.add(Calendar.MONTH, 1);
            reportMonthStart = getIn.getReportMonth();
            reportMonthEnd = calendar.getTime();
        }

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return  getBaseMapper().getList(page, getIn.getSurveyManager(), getIn.getAuditor(),
                surveyMonthStart, surveyMonthEnd, reportMonthStart, reportMonthEnd, getIn.getIsSubmitCountOK(),
                getIn.getIsAuditExpired(), getIn.getIsSubmitExpired(), getIn.getIsReleaseExpired());
    }

    private StaffEvaluation getByCidPid(Long corporationId, Long projectId) {
        QueryWrapper<StaffEvaluation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.eq("corporation_id", corporationId);
        return getOne(queryWrapper);
    }
}




