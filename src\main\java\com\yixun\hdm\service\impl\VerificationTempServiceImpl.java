package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.dao.VerificationTempMapper;
import com.yixun.hdm.entity.OperationLogsTemp;
import com.yixun.hdm.entity.VerificationTemp;
import com.yixun.hdm.service.OperationLogsTempService;
import com.yixun.hdm.service.VerificationTempService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *
 */
@Service
public class VerificationTempServiceImpl extends ServiceImpl<VerificationTempMapper, VerificationTemp>
    implements VerificationTempService{

    @Resource
    private OperationLogsTempService operationLogsTempService;

    @Override
    public void submitVerification(VerificationTemp verificationTemp, OperationLogsTemp verificationLogsTemp) {
        save(verificationTemp);
        operationLogsTempService.save(verificationLogsTemp);
    }
}




