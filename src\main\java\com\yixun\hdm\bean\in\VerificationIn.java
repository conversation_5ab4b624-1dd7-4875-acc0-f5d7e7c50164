package com.yixun.hdm.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class VerificationIn {

    @ApiModelProperty(value="隐患验证id", required = false)
    private Long verificationId;

    @ApiModelProperty(value="隐患id", required = true)
    private Long hiddenDangerId;

    @ApiModelProperty(value="隐患照片（可多图）", required = true)
    private List pictures;

    @ApiModelProperty(value="改善负责人", required = true)
    private String inChargeName;

    @ApiModelProperty(value="是否合格", required = true)
    private Boolean isPass;

    @ApiModelProperty(value="不合格原因")
    private String reason;

}
