package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.entity.SelfCheckDetail;
import com.yixun.hdm.entity.SelfCheckRecord;

/**
 * 自查记录详情(SelfCheckDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-26 18:10:14
 */
public interface SelfCheckDetailService extends IService<SelfCheckDetail> {

    void createDetail(SelfCheckRecord selfCheckRecord);

    Page<SelfCheckDetail> pageList(CommonPage commonPage, SelfCheckDetail selfCheckDetail);
}

