package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.dao.RectificationMapper;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.OperationLogs;
import com.yixun.hdm.entity.Rectification;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.service.OperationLogsService;
import com.yixun.hdm.service.RectificationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 隐患改善服务
 */
@Service
public class RectificationServiceImpl extends ServiceImpl<RectificationMapper, Rectification>
    implements RectificationService{

    @Resource
    private OperationLogsService operationLogsService;

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Override
    @Transactional
    public void submitRectification(HiddenDanger hiddenDanger, Rectification rectification, OperationLogs operationLogs) {
        save(rectification);
        operationLogsService.save(operationLogs);
        if (hiddenDanger!=null){
            hiddenDangerService.updateById(hiddenDanger);
        }
    }

    @Override
    public List<Rectification> getByHiddenDanger(Long hiddenDangerId) {
        QueryWrapper<Rectification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hidden_danger_id", hiddenDangerId);

        return list(queryWrapper);
    }

    @Override
    public Rectification getLastRectification(Long hiddenDangerId) {
        QueryWrapper<Rectification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hidden_danger_id", hiddenDangerId);
        queryWrapper.orderByDesc("create_time");

        List<Rectification> rectificationList = list(queryWrapper);
        if (rectificationList.isEmpty()){
            return null;
        }

        return rectificationList.get(0);

    }

    @Override
    @Transactional
    public void editSubmitRectification(HiddenDanger hiddenDanger, Rectification rectification, OperationLogs operationLogs) {
        updateById(rectification);
        operationLogsService.save(operationLogs);
        hiddenDangerService.updateById(hiddenDanger);
    }
}




