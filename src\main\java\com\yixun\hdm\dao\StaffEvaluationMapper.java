package com.yixun.hdm.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.entity.StaffEvaluation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Date;

/**
 * @Entity com.yixun.hdm.entity.StaffEvaluation
 */
public interface StaffEvaluationMapper extends BaseMapper<StaffEvaluation> {

    Page<StaffEvaluation> getList(Page page, String surveyManager, String auditor, Date surveyMonthStart, Date surveyMonthEnd,
                   Date reportMonthStart, Date reportMonthEnd, Boolean isSubmitCountOK, Boolean isAuditExpired,
                   Boolean isSubmitExpired, Boolean isReleaseExpired);
}




