package com.yixun.hdm.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.yixun.bean.CommonResult;
import com.yixun.hd.bean.in.HdReleaseIn;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.*;
import com.yixun.hdm.bean.out.CorpSafeListOut;
import com.yixun.hdm.bean.out.HdStatisticOut;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.LabelValue;
import com.yixun.hdm.entity.OperationLogs;
import com.yixun.hdm.entity.em.AuditStatus;

import java.util.List;

/**
 *
 */
public interface HiddenDangerService extends IService<HiddenDanger> {

    PageInfo<HiddenDanger> getHiddenDangerPage(HiddenDangerListGetIn hiddenDangerListGetIn);

    List<HiddenDanger> getHiddenDangerList(HiddenDangerListApiGetIn hiddenDangerListApiIn);

    void submitHiddenDanger(HiddenDanger hiddenDanger, OperationLogs operationLogs);

    void handleRegulationsMemo(HiddenDanger hiddenDanger);

    List<HiddenDanger> getHiddenDangerAllList(HiddenDangerListGetIn hiddenDangerListGetIn);

    List<HiddenDanger> getHiddenDangerGroupedList(HiddenDangerListApiGetIn hiddenDangerListGetIn);

    List<HiddenDanger> getUnSubmittedList(List<Long> hdIdList);

    List<HiddenDanger> getAuditedList(List<Long> hdIdList);

    void sendDingMessage(List<HiddenDanger> hiddenDangerList);

    void updateArrangedAuditorByProject(Long projectId, HiddenDanger hiddenDanger);

    Page<HiddenDanger> getCorpAuditList(List<Long> submitList, AuditStatus auditStatus, CommonPage page);

    void updateCorpName(Long corporationId, HiddenDanger hiddenDanger);

    Boolean checkRiskFactorTypeUsed(LabelValue labelValue);

    void updateHdLedgerStatus(Long projectId, Long corporationId, Integer turn, String realName);

    List<HiddenDanger> getProjectCooperateStatistic(Long projectId, List<Long> corpIds, Boolean isFindByCallback);

    HdStatisticOut getNewHdStatistic(HiddenDangerListGetIn hiddenDangerListGetIn);

    JSONObject convertRiskFactorTypeDetail(List<HiddenDanger> list);

    void doSaveList(HiddenDangerListIn hiddenDangerListIn);

    void checkHiddenDangerContent(HiddenDanger hiddenDanger);

    List<Object> smartRecommendStandby(String description);

    void updateHiddenDanger(HiddenDanger hiddenDanger);

    JSONObject recommendHdByDesc(String description);

    void doEdit(HiddenDangerEditIn in);

    Boolean delete(Long id);

    Boolean release(HdReleaseIn in);

    CommonResult<List<CorpSafeListOut>> corpSafeList(CorpSafeListIn in);

    void exportCorpSafeList(CorpSafeListIn in);
}
