package com.yixun.hdm.service;

import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.OperationLogs;
import com.yixun.hdm.entity.Rectification;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface RectificationService extends IService<Rectification> {

    void submitRectification(HiddenDanger hiddenDanger, Rectification rectification, OperationLogs operationLogs);

    List<Rectification> getByHiddenDanger(Long hiddenDangerId);

    Rectification getLastRectification(Long hiddenDangerId);

    void editSubmitRectification(HiddenDanger hiddenDanger, Rectification rectification, OperationLogs operationLogs);
}
