package com.yixun.hdm.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HdAuditListGetIn {

    @ApiModelProperty(value="公司id")
    private Long corporationId;
    @ApiModelProperty(value="项目id")
    private Long projectId;
    @ApiModelProperty(value="计划安排的审查员id")
    private Long arrangedAuditorId;
    @ApiModelProperty(value="审查状态：ongoing，done，all")
    private String status;
    @ApiModelProperty(value="紧急程度：1-今日已到期，2-已过期，3-其他")
    private Integer urgencyLevel;

}
