package com.yixun.hdm.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class StatisticIn {

    @ApiModelProperty(value="项目id")
    private Long projectId;
    @ApiModelProperty(value="开始日期")
    private Date startDate;
    @ApiModelProperty(value="结束日期")
    private Date endDate;
    @ApiModelProperty(value="是否回访时发现")
    private Boolean isFindByCallback;
}
