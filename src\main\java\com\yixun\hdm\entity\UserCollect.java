package com.yixun.hdm.entity;



import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户收藏表(UserCollect)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-19 16:10:45
 */
@SuppressWarnings("serial")
@Data
@ApiModel("用户收藏表")
public class UserCollect extends Model<UserCollect> {

    @NotNull(message="[${column.comment}]不能为空")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
    * 用户ID
    */
    @ApiModelProperty("用户ID")
    @NotNull(message="[用户ID]不能为空")
    private Long userId;

    /**
    * 内容id
    */
    @ApiModelProperty("内容id")
    private Long contentId;

    /**
    * 内容类型；1：检查内容；2：法律法规
    */
    @ApiModelProperty("内容类型；1：检查内容；2：法律法规")
    private Integer contentType;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createTime;
}

