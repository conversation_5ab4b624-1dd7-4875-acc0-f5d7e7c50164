package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.StaffEvaluationListGetIn;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.StaffEvaluation;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *
 */
public interface StaffEvaluationService extends IService<StaffEvaluation> {

    void init(HiddenDanger hiddenDanger, int hdCount);

    void setFinish(Long corporationId, Long projectId, String auditor);

    void setRelease(Long corporationId, Long projectId);

    Page<StaffEvaluation> getList(StaffEvaluationListGetIn staffEvaluationListGetIn, CommonPage page);
}
