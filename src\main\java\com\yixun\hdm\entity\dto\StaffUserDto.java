package com.yixun.hdm.entity.dto;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class StaffUserDto {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    private String username;
    private String realName;
    private String phone;
    private String email;
    private String avatar;
    private String type;
    private String groupName;
    private List<String> roles;
    private List<String> permissions;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date lastLoginTime;
    private String lastLoginIp;

    public void setRoles(String roles) {
        this.roles = JSON.parseArray(roles, String.class);
    }

}
