package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.entity.RegulationsMemo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface RegulationsMemoService extends IService<RegulationsMemo> {

    Page<RegulationsMemo> getList(CommonPage page);

    void confirm(RegulationsMemo regulationsMemo);

    void update(RegulationsMemo regulationsMemo);
}
