package com.yixun.hdm.bean.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 自查企业分页查询参数
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
@Data
public class SelfCheckCorpPageQueryIn extends CommonPage {

    @ApiModelProperty("项目ID")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    @ApiModelProperty("企业ID")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corpId;

    @ApiModelProperty("企业名称")
    private String corpName;

    @ApiModelProperty("推送状态；0：未推送；1：已推送")
    private Integer pushStatus;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
}
