package com.yixun.hdm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.api.ProjectConfigApi;
import com.yixun.bean.out.YqycProjectConfig;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.HdAuditListGetIn;
import com.yixun.hdm.dao.HdAuditMapper;
import com.yixun.hdm.entity.HdAudit;
import com.yixun.hdm.entity.HdAuditLog;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.entity.em.AuditStatus;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.*;
import com.yixun.hdm.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class HdAuditServiceImpl extends ServiceImpl<HdAuditMapper, HdAudit>
    implements HdAuditService{

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private MessageService messageService;

    @Resource
    private HdAuditLogService hdAuditLogService;

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private StaffEvaluationService staffEvaluationService;

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Autowired
    private ProjectConfigApi projectConfigApi;

    @Override
    public void insert(List<HiddenDanger> hiddenDangerList, StaffUserDto adminUser) {
        Date auditSubmitTime = new Date();
        Integer hdAuditDeadline = 2;
        YqycProjectConfig info = projectConfigApi.info(hiddenDangerList.get(0).getProjectId());
        if(info != null && info.getHdAuditDeadline() != null){
            hdAuditDeadline = info.getHdAuditDeadline();
        }
        Date auditExpiredTime = WorkDayUtils.getWorkDate2(auditSubmitTime, hdAuditDeadline);

        List<Long> idList = hiddenDangerList.stream().map(HiddenDanger::getId).collect(Collectors.toList());

        HdAudit hdAudit = new HdAudit();
        BeanUtils.copyProperties(hiddenDangerList.get(0), hdAudit);
        hdAudit.setId(SnGeneratorUtil.getId());
        hdAudit.setAuditSubmitterId(adminUser.getId());
        hdAudit.setAuditSubmitter(adminUser.getRealName());
        hdAudit.setAuditSubmitTime(auditSubmitTime);
        hdAudit.setAuditExpiredTime(auditExpiredTime);
        hdAudit.setSubmitCount(idList.size());
        hdAudit.setSubmitList(JSON.toJSONString(idList));
        hdAudit.setFinishCount(0);
        hdAudit.setFinishList("[]");
        hdAudit.setIsFinish(0);

        save(hdAudit);
    }

    @Override
    public void checkAuditSubmitter(Long corporationId, Long userId) {

        QueryWrapper<HdAudit> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("corporation_id", corporationId);

        List<HdAudit> list = list(queryWrapper);
        if (!list.isEmpty() && !list.get(0).getAuditSubmitterId().equals(userId)){
            throw new DataErrorException("提交人与上次不一致");
        }
    }

    @Override
    public Page<HdAudit> getAuditPage(HdAuditListGetIn hdAuditListGetIn, CommonPage commonPage) {
        QueryWrapper<HdAudit> queryWrapper = new QueryWrapper<>();
        if (hdAuditListGetIn.getProjectId()!=null){
            queryWrapper.eq("project_id", hdAuditListGetIn.getProjectId());
        }
        if (hdAuditListGetIn.getCorporationId()!=null){
            queryWrapper.eq("corporation_id", hdAuditListGetIn.getCorporationId());
        }
        if (hdAuditListGetIn.getArrangedAuditorId()!=null){
            queryWrapper.eq("arranged_auditor_id", hdAuditListGetIn.getArrangedAuditorId());
        }
        if (hdAuditListGetIn.getStatus()!=null){
            if (hdAuditListGetIn.getStatus().equals("ongoing")){
                queryWrapper.apply("submit_count > finish_count");
            }else if (hdAuditListGetIn.getStatus().equals("done")){
                queryWrapper.apply("submit_count = finish_count");
            }
        }
        if (hdAuditListGetIn.getUrgencyLevel()!=null){
            if (hdAuditListGetIn.getUrgencyLevel()==1){
                Calendar c = Calendar.getInstance();
                c.set(Calendar.HOUR_OF_DAY, 0);
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
                Date start = c.getTime();
                c.add(Calendar.DAY_OF_MONTH, 1);
                Date end = c.getTime();
                queryWrapper.between("audit_expired_time", start, end);
            }else if (hdAuditListGetIn.getUrgencyLevel()==2){
                queryWrapper.lt("audit_expired_time", new Date());
            }else if (hdAuditListGetIn.getUrgencyLevel()==3){
                Calendar c = Calendar.getInstance();
                c.set(Calendar.HOUR_OF_DAY, 0);
                c.set(Calendar.MINUTE, 0);
                c.set(Calendar.SECOND, 0);
                c.add(Calendar.DAY_OF_MONTH, 1);
                Date time = c.getTime();
                queryWrapper.gt("audit_expired_time", time);
            }
        }
        queryWrapper.orderByAsc("is_finish", "audit_submit_time");

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<HdAudit>) page(page, queryWrapper);
    }

    @Override
    public void setAudit(HdAudit hdAudit, Long hiddenDangerId, StaffUserDto adminUser, StaffUserDto submitter) {

        List<Long> finishList = JSON.parseArray(hdAudit.getFinishList(), Long.class);
        if (!finishList.contains(hiddenDangerId)){
            finishList.add(hiddenDangerId);
            hdAudit.setFinishList(JSON.toJSONString(finishList));
        }
        hdAudit.setFinishCount(finishList.size());
        if (hdAudit.getAuditor()!=null){
            if (!hdAudit.getAuditor().contains(adminUser.getRealName())){
                hdAudit.setAuditor(hdAudit.getAuditor()+","+adminUser.getRealName());
            }
        }else {
            hdAudit.setAuditor(adminUser.getRealName());
        }

        if (hdAudit.getSubmitCount().equals(hdAudit.getFinishCount())){
            hdAudit.setIsFinish(1);
        }
        updateById(hdAudit);

        if (hdAudit.getSubmitCount().equals(hdAudit.getFinishCount())){
            String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String message = hdAudit.getAuditSubmitter()+"老师，" + hdAudit.getProjectName() + "-" + hdAudit.getCorpName()
                    + "已完成隐患审查，请知晓。若需修改完善，请按审查要求进行，谢谢！审查人：" + hdAudit.getAuditor()
                    + "  " + date;
            messageService.sendDing(message, submitter.getPhone());

            staffEvaluationService.setFinish(hdAudit.getCorporationId(), hdAudit.getProjectId(), hdAudit.getAuditor());
        }
    }

    @Override
    public Long getAuditSubmitter(Long corporationId) {
        QueryWrapper<HdAudit> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("corporation_id", corporationId);

        List<HdAudit> list = list(queryWrapper);
        if (list.isEmpty()){
            return null;
        }else {
            return list.get(0).getAuditSubmitterId();
        }
    }

    @Override
    public List<HdAudit> getExpiredAudit(Date date) {
        QueryWrapper<HdAudit> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("audit_expired_time", date);
        queryWrapper.eq("is_finish", false);

        return list(queryWrapper);
    }

    @Override
    public void submitAudit(List<Long> hdIdList) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("隐患考核");
        try {
            RLock lock = redissonClient.getLock("SubmitAuditHdIdList:"+JSON.toJSONString(hdIdList));
            if (lock.tryLock(10,10, TimeUnit.SECONDS)) {
                Long userId = AdminUserHelper.getCurrentUserId();
                HiddenDanger aHd = hiddenDangerService.getById(hdIdList.get(0));
//                checkAuditSubmitter(aHd.getCorporationId(), userId);

                //生成隐患管理考核
                staffEvaluationService.init(aHd, hdIdList.size());

                stopWatch.stop();
                stopWatch.start("检查隐患信息");
                List<HiddenDanger> hiddenDangerList = hiddenDangerService.getUnSubmittedList(hdIdList);

                boolean nonArrangedAuditor = hiddenDangerList.stream().anyMatch(hd -> hd.getArrangedAuditorId() == null);
                if (nonArrangedAuditor){
                    throw new DataErrorException("项目的技术助理为空，请联系相关人员处理后再提交");
                }

                checkHdCompletion(hiddenDangerList);
                stopWatch.stop();
                stopWatch.start("发送ding");
                for (HiddenDanger hiddenDanger : hiddenDangerList){
                    hiddenDanger.setHdQualityScore(HdQualityScoreUtils.calculateScore(hiddenDanger));
//                    hiddenDanger.setAuditStatus(AuditStatus.Submitted.name());
                }
                if (!hiddenDangerList.isEmpty()){
                    StaffUserDto adminUser = userCenterService.getAdminUser(userId);
                    insert(hiddenDangerList, adminUser);
                    hiddenDangerService.sendDingMessage(hiddenDangerList);
                    stopWatch.stop();
                    hiddenDangerService.updateBatchById(hiddenDangerList);
                    stopWatch.start("添加审核日志");
                    //记录审查日志
                    for (HiddenDanger hiddenDanger : hiddenDangerList){
                        HdAuditLog hdAuditLog = new HdAuditLog();
                        BeanUtils.copyProperties(hiddenDanger, hdAuditLog, true);
                        hdAuditLog.setId(SnGeneratorUtil.getId());
                        hdAuditLog.setCreateTime(new Date());
                        hdAuditLog.setHiddenDangerId(hiddenDanger.getId());
                        hdAuditLog.setAuditSubmitter(adminUser.getRealName());
                        hdAuditLog.setAuditSubmitterId(userId);
                        hdAuditLogService.save(hdAuditLog);
                    }
                    stopWatch.stop();
                }
            }
            log.info(stopWatch.prettyPrint());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

    }

    @Override
    public void checkHdCompletion(List<HiddenDanger> hiddenDangerList) {
        for (HiddenDanger hiddenDanger : hiddenDangerList){
            String description = hiddenDanger.getDescription();
            if (hiddenDanger.getPictures() ==  null || hiddenDanger.getPictures().isEmpty()){
                throw new DataErrorException("请上传隐患图片--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getSpecialCategory())){
                throw new DataErrorException("请填写专项类别--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getDescription())){
                throw new DataErrorException("请填写隐患描述--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getType())){
                throw new DataErrorException("请填写隐患类别--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getLevel())){
                throw new DataErrorException("请填写隐患级别--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getRiskFactorType())){
                throw new DataErrorException("请填写危害因素分类--"+description);
            }
            if(StringUtils.hasLength(hiddenDanger.getRiskFactorTypeDetail())){
                JSONArray array = JSON.parseArray(hiddenDanger.getRiskFactorTypeDetail());
                if (array.size()<=1){
                    throw new DataErrorException("危害因素分类必须填到二级以上"+description);
                }
            }
            if (hiddenDanger.getPossibleConsequence() == null || hiddenDanger.getPossibleConsequence().isEmpty()){
                throw new DataErrorException("请填写可能导致后果--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getRectificationSuggestion()) && hiddenDanger.getSuggestionList().isEmpty()){
                throw new DataErrorException("请填写建议改善措施--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getStandard()) || hiddenDanger.getStandard().length()<3){
                throw new DataErrorException("请填写依据标准--"+description);
            }
        }
    }

}




