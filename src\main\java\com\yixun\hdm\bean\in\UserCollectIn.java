package com.yixun.hdm.bean.in;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户收藏表(UserCollect)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-19 16:10:45
 */
@SuppressWarnings("serial")
@Data
@ApiModel("用户收藏输入")
public class UserCollectIn {



    /**
    * 内容id
    */
    @NotNull(message="[内容id]不能为空")
    @ApiModelProperty("内容id")
    private Long contentId;

    /**
    * 内容类型；1：检查内容；2：法律法规
    */
    @NotNull(message="[内容类型]不能为空")
    @ApiModelProperty("内容类型；1：检查内容；2：法律法规")
    private Integer contentType;


}

