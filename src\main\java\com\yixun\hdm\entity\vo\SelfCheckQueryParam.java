package com.yixun.hdm.entity.vo;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotNull;

/**
 * 自查记录查询参数
 */
@Data
@FieldNameConstants
public class SelfCheckQueryParam {

    /**
     * 项目 ID（必填）
     */
	@NotNull(message = "项目 ID 不能为空")
    private Long projectId;

	/**
	 * 项目名称 注意 导出时必填
	 */
	private String projectName;

	/**
	 * 检查进度
	 */
	private Double selfCheckProgress;

	/**
	 * 检查进度符号 >=: 1 <=: 2
	 */
	private Integer selfCheckProgressSymbol;

    /**
     * 最小检查进度（%），例如 60 表示 ≥60%
     */
    private Double minSelfCheckProgress;

    /**
     * 最大检查进度（%），例如 90 表示 ≤90%
     */
    private Double maxSelfCheckProgress;

	/**
	 * 隐患查出率
	 */
	private Double hiddenDangerDetectionRate;

	/**
	 * 检查进度符号 >=: 1 <=: 2
	 */
	private Integer hiddenDangerDetectionRateSymbol;

    /**
     * 最小隐患查出率（%）
     */
    private Double minHiddenDangerDetectionRate;

    /**
     * 最大隐患查出率（%）
     */
    private Double maxHiddenDangerDetectionRate;

	/**
	 * 隐患整改率
	 */
	private Double hiddenDangersRectifiedRate;

	/**
	 * 检查进度符号 >=: 1 <=: 2
	 */
	private Integer hiddenDangersRectifiedRateSymbol;

    /**
     * 最小隐患整改率（%）
     */
    private Double minHiddenDangersRectifiedRate;

    /**
     * 最大隐患整改率（%）
     */
    private Double maxHiddenDangersRectifiedRate;

    /**
     * 当前页码，从 1 开始
     */
    private int pageNum = 1;

    /**
     * 每页大小
     */
    private int pageSize = 20;

	/**
	 * 企业名称（模糊查询）
	 */
	private String corpName;

    /**
     * 计算分页偏移量（供 MyBatis XML 中使用）
     */
    public long getOffset() {
        return (pageNum - 1) * pageSize;
    }
}
