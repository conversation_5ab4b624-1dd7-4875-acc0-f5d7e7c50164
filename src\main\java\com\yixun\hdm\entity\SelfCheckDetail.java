package com.yixun.hdm.entity;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 自查记录详情(SelfCheckDetail)表实体类
 *
 * <AUTHOR>
 * @since 2025-01-09 14:57:07
 */
@SuppressWarnings("serial")
@Data
@ApiModel("自查记录详情")
@TableName("self_check_detail")
public class SelfCheckDetail extends Model<SelfCheckDetail> {

    @ApiModelProperty("主键ID")
    @NotNull(message="[主键ID]不能为空")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @ApiModelProperty("自查记录ID")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long checkRecordId;

    @ApiModelProperty("推荐原因")
    private String recommendReason;

    @ApiModelProperty("推荐描述")
    private String recommendDesc;

    @ApiModelProperty("检查结果;-1:未检查；0：合格；1：不合格；2：不涉及；3：重复项")
    private Integer result;

    @ApiModelProperty("图片")
    private String picture;

    @ApiModelProperty("自检隐患ID")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long selfHdId;

    @TableField(exist = false)
    @ApiModelProperty("自检隐患信息")
    private SelfHiddenDanger selfHiddenDanger;

    @ApiModelProperty("检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @ApiModelProperty("创建时间")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

}

