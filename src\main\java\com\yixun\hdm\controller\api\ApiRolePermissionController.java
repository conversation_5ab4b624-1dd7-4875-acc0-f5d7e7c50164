package com.yixun.hdm.controller.api;

import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.Permission;
import com.yixun.hdm.utils.SpringContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/rolePermission")
public class ApiRolePermissionController {

	@GetMapping(value = "/getPermissionList")
	public CommonResult<List<Permission>> getPermissionList(){

		List<Permission> permissionList = loadPermissions();

		return CommonResult.successData(permissionList);
	}

	private List<Permission> loadPermissions() {
		Map<String, Object> map = SpringContextHolder.getContext().getBeansWithAnnotation(RestController.class);
		Collection<Object> set = map.values();

		List<Permission> permissionList = new ArrayList<>();
		for (Object cls : set) {
			if (!"com.yixun.hdm.controller.admin".equals(cls.getClass().getPackage().getName())) {
				continue;
			}

			//类型
			String type = "";
			Api api = AnnotationUtils.findAnnotation(cls.getClass(), Api.class);
			if (api != null) {
				String[] tags = api.tags();
				if (tags.length > 0) {
					type = tags[0];
				}
			}

			//获取各接口的方法注解
			Method[] methods = cls.getClass().getMethods();
			for (Method method : methods) {
				String label = "";
				String name = "";

				ApiOperation apiOperation = AnnotationUtils.findAnnotation(method, ApiOperation.class);
				if (apiOperation != null) {
					label = apiOperation.value();

					RequiredPermission requiredPermission = AnnotationUtils.findAnnotation(method, RequiredPermission.class);
					if (requiredPermission != null) {
						if (requiredPermission.value().length() > 0) {
							name = requiredPermission.value();
						}
					} else {
						continue;
					}

					Permission permission = new Permission();
					permission.setName(name);
					permission.setLabel(label);
					permission.setType(type);
					permissionList.add(permission);
				}
			}
		}

		return permissionList;
	}
}
