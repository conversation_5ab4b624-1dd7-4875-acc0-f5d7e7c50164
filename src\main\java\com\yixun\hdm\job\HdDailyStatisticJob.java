package com.yixun.hdm.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.in.HiddenDangerListGetIn;
import com.yixun.hdm.bean.out.HdStatisticOut;
import com.yixun.hdm.service.HiddenDangerService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
public class HdDailyStatisticJob {

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;

    @XxlJob("hdDailyStatisticHandler")
    public CommonResult<Void> hdDailyStatisticHandler() {

        String redisKey = "HdDailyStatistic";
        HiddenDangerListGetIn hiddenDangerListGetIn = new HiddenDangerListGetIn();
        HdStatisticOut statisticOut = hiddenDangerService.getNewHdStatistic(hiddenDangerListGetIn);
        redisTemplate.opsForValue().set(redisKey, statisticOut, 24, TimeUnit.HOURS);

        return CommonResult.successResult(null);
    }

}
