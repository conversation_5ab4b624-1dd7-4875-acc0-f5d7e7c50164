package com.yixun.hdm.entity;

import lombok.Data;

import java.util.List;

@Data
public class SurveyInfo {

    /**
     * surveyDate : [""]
     * surveyManager : {"name":"","email":"","phone":""}
     * surveyTogether : [{"name":"","email":"","phone":""}]
     */

    private SurveyManagerBean surveyManager;
    private List<String> surveyDate;
    private List<SurveyTogetherBean> surveyTogether;

    public SurveyManagerBean getSurveyManager() {
        return surveyManager;
    }

    public void setSurveyManager(SurveyManagerBean surveyManager) {
        this.surveyManager = surveyManager;
    }

    public List<String> getSurveyDate() {
        return surveyDate;
    }

    public void setSurveyDate(List<String> surveyDate) {
        this.surveyDate = surveyDate;
    }

    public List<SurveyTogetherBean> getSurveyTogether() {
        return surveyTogether;
    }

    public void setSurveyTogether(List<SurveyTogetherBean> surveyTogether) {
        this.surveyTogether = surveyTogether;
    }

    public static class SurveyManagerBean {

        private String name;
        private String email;
        private String phone;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }

    public static class SurveyTogetherBean {

        private String name;
        private String email;
        private String phone;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }
}
