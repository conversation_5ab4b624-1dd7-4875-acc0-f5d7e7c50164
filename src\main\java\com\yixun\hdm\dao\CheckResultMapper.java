package com.yixun.hdm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.out.CheckResultStatisticsOut;
import com.yixun.hdm.entity.CheckResult;

import java.util.List;

/**
 * @Entity com.yixun.hdm.entity.CheckResult
 */
public interface CheckResultMapper extends BaseMapper<CheckResult> {

    Page<CheckResultStatisticsOut> getStatisticsPage(Page page, Long projectId, Long corporationId, Boolean hasHd);

    List<CheckResultStatisticsOut> getStatisticsList(Long projectId, Long corporationId, Boolean hasHd);
}




