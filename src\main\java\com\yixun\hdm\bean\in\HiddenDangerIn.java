package com.yixun.hdm.bean.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.hdm.entity.HiddenDangerSuggestion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class HiddenDangerIn {

    @ApiModelProperty(value="隐患ID", required = true)
    private Long id;

    @ApiModelProperty(value="项目id", required = true)
    private Long projectId;

    @ApiModelProperty(value="公司id", required = true)
    private Long corporationId;

    @ApiModelProperty(value="调研轮次")
    private Integer turn;

    @ApiModelProperty(value="任务id")
    private Long taskId;

    @ApiModelProperty(value="任务类型 1-调研 2-辅助调查培训 3-回访 4-联合调研")
    private Integer taskType;

    @ApiModelProperty(value="任务时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskWorkDate;

    @ApiModelProperty(value="专项类别", required = true)
    private String specialCategory;

    @ApiModelProperty(value="专项类别明细", required = true)
    private List specialCategoryDetail;

    @ApiModelProperty(value="隐患照片（可多图）", required = true)
    private List pictures;

    @ApiModelProperty(value="隐患动图（可多图）")
    private List gifPictures;

    @ApiModelProperty(value="隐患描述", required = true)
    private String description;

    @ApiModelProperty(value="隐患门类", required = true)
    private String type;

    @ApiModelProperty("归属类别；1：制度与操作规程；2：教育培训；3：设备管理；4：物料管理；5：作业环境管理；6：事故四不放过管理；7：应急管理；8：职业健康管理")
    private Integer belongCategory;

    @ApiModelProperty(value="隐患级别", required = true)
    private String level;

    @ApiModelProperty(value="临时应急处置", required = true)
    private String tempEmergency;

    @ApiModelProperty(value="备注")
    private String remark;

    @ApiModelProperty(value="文件（可多个）")
    private List files;

    @ApiModelProperty(value="依据标准", required = true)
    private List standard;

    @ApiModelProperty(value="可能导致后果", required = true)
    private List possibleConsequence;

    @ApiModelProperty(value="危害因素分类", required = true)
    private String riskFactorType;

    @ApiModelProperty(value="危害因素分类明细", required = true)
    private List riskFactorTypeDetail;

    @ApiModelProperty(value="所属风险点id")
    private Long riskPointId;

    @ApiModelProperty(value="所属风险点")
    private String riskPointName;

    @ApiModelProperty(value="建议改善措施")
    private String rectificationSuggestion;

    @ApiModelProperty(value="持表检查项目id")
    private Long checkOptionId;

    @ApiModelProperty(value="改善期限（天）")
    private Integer rectifyDeadline;

    @ApiModelProperty(value="是否回访时发现")
    private Boolean isFindByCallback;

    /**
     * 隐患区域
     */
    @ApiModelProperty("隐患区域")
    private String hdRegion;

    /**
     * 设备设施/物料
     */
    @ApiModelProperty("设备设施/物料")
    private String equipment;

    /**
     * 作业活动
     */
    @ApiModelProperty("作业活动")
    private String jobActivity;

    /**
     * 岗位/工种
     */
    @ApiModelProperty("岗位/工种")
    private String workCategory;

    @ApiModelProperty("建议分项录入列表")
    private List<HiddenDangerSuggestion> suggestionList = new ArrayList<>();

}
