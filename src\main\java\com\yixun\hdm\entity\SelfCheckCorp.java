package com.yixun.hdm.entity;



import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.bean.common.CodeNameInfo;
import com.yixun.hdm.bean.common.LabelValueInfo;
import com.yixun.hdm.convert.CodeNameListTypeHandler;
import com.yixun.hdm.convert.LabelValueListTypeHandler;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 自查企业(SelfCheckCorp)表实体类
 *
 * <AUTHOR>
 * @since 2025-01-09 14:59:56
 */
@SuppressWarnings("serial")
@Data
@ApiModel("自查企业")
@TableName(value = "self_check_corp",autoResultMap = true)
public class SelfCheckCorp extends Model<SelfCheckCorp> {

    @ApiModelProperty("主键ID")
    @NotNull(message="[主键ID]不能为空")
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @ApiModelProperty("项目ID")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("企业ID")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corpId;

    @ApiModelProperty("企业名称")
    private String corpName;

    @TableField(typeHandler = CodeNameListTypeHandler.class)
    @ApiModelProperty("国民经济行业")
    private List<CodeNameInfo> economicIndustry;

    @TableField(typeHandler = CodeNameListTypeHandler.class)
    @ApiModelProperty("企业地区")
    private List<CodeNameInfo> region;

    @ApiModelProperty("推送状态；0：未推送；1：已推送")
    private Integer pushStatus;

    @ApiModelProperty("最近推送时间")
    private Date lastPushTime;

    @TableField(exist = false)
    @ApiModelProperty("是否检查过")
    private Boolean hasChecked;

    @TableField(exist = false)
    @ApiModelProperty("最近检查时间")
    private Date lastCheckTime;

    @ApiModelProperty("创建时间")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    @ApiModelProperty("更新时间")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

}

