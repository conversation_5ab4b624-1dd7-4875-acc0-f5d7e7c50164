package com.yixun.hdm.controller.api;

import com.yixun.bean.CommonResult;
import com.yixun.hd.api.HiddenDangerApi;
import com.yixun.hd.bean.CropHdNumIn;
import com.yixun.hd.bean.CropHdNumOut;
import com.yixun.hd.bean.in.HdReleaseIn;
import com.yixun.hd.bean.in.HiddenDangerListApiGetIn;
import com.yixun.hd.bean.out.HiddenDangerApiOut;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.dao.HiddenDangerMapper;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.Rectification;
import com.yixun.hdm.entity.Verification;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import com.yixun.hdm.service.*;
import com.yixun.hdm.utils.BeanUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class FeignApiHiddenDangerController implements HiddenDangerApi {

    @Autowired
    private HiddenDangerMapper hiddenDangerMapper;

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource
    private HiddenDangerTempService hiddenDangerTempService;

    @Resource
    private RectificationService rectificationService;

    @Resource
    private RectificationTempService rectificationTempService;

    @Resource
    private VerificationService verificationService;

    @Resource
    private VerificationTempService verificationTempService;

    @Resource
    private StatisticService statisticService;

    @Override
    public List<CropHdNumOut> getCropHdNum(CropHdNumIn cropHdNumIn) {
        return hiddenDangerMapper.getCropHdNum(cropHdNumIn.getProjectId(),cropHdNumIn.getCorpIdList());
    }

    @Override
    public List<CropHdNumOut> getLatestTurnCropHdNum(CropHdNumIn cropHdNumIn) {
        return hiddenDangerMapper.getLatestTurnCropHdNum(cropHdNumIn);
    }


    @Override
    public CommonResult<List<HiddenDangerApiOut>> getHiddenDangerList(HiddenDangerListApiGetIn hiddenDangerListGetIn) {
        com.yixun.hdm.bean.in.HiddenDangerListApiGetIn hiddenDangerListApiGetIn = BeanUtils.copyNew(hiddenDangerListGetIn, com.yixun.hdm.bean.in.HiddenDangerListApiGetIn.class);
        if(hiddenDangerListGetIn.getType() != null){
            hiddenDangerListApiGetIn.setType(hiddenDangerListGetIn.getType().name());
        }
        List<HiddenDanger> hiddenDangerList = hiddenDangerService.getHiddenDangerList(hiddenDangerListApiGetIn);
        List<HiddenDangerApiOut> outList = BeanUtils.copyToOutList(hiddenDangerList, HiddenDangerApiOut.class);
        outList.forEach(hd->{
            if (hd.getStatus().equals(HiddenDangerStatus.已改善.name())){
                Rectification rectification = rectificationService.getLastRectification(hd.getId());
                if (rectification!=null){
                    hd.setRectificationTime(rectification.getCreateTime());
                    hd.setRectificationDescription(rectification.getDescription());
                    hd.setRectificationPictures(rectification.getPictures());
                    hd.setRectificationOperator(rectification.getInChargeName());
                }
                Verification verification = verificationService.getLastVerification(hd.getId());
                if (verification!=null){
                    hd.setVerifyTime(verification.getCreateTime());
                    hd.setVerifyDescription(verification.getReason());
                    hd.setVerifyPictures(verification.getPictures());
                    hd.setVerifyOperator(verification.getInChargeName());
                }
            }
        });

        return CommonResult.successData(outList);
    }

    @Override
    public Boolean release(HdReleaseIn in) {
        return hiddenDangerService.release(in);
    }

}
