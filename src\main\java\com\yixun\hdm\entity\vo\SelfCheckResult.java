package com.yixun.hdm.entity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 企业统计列表
 */
@Data
public class SelfCheckResult {

    private Long id;

	/**
	 * 企业名称
	 */
	private String corpName;

    /**
     * 统计字段：推荐检查内容
     */
    private Long totalRecommendSelfCheck;

    /**
     * 统计字段：查出隐患数（result=1）
     */
    private Long totalHiddenDanger;

    /**
     * 统计字段：整改隐患数量（status=3）
     */
    private Long hiddenDangersRectifiedNum;

    /**
     * 检查进度，单位百分比，保留两位小数
     */
    private BigDecimal selfCheckProgress;

    /**
     * 隐患查出率，单位百分比，保留两位小数
     */
    private BigDecimal hiddenDangerDetectionRate;

    /**
     * 隐患整改率，单位百分比，保留两位小数
     */
    private BigDecimal hiddenDangersRectifiedRate;
}
