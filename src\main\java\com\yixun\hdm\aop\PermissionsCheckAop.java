package com.yixun.hdm.aop;

import com.yixun.hdm.service.UserCenterService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

@Aspect
@Component
public class PermissionsCheckAop {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private UserCenterService userCenterService;

    @Around("@annotation(com.yixun.hdm.aop.RequiredPermission)")
    public Object postExec(ProceedingJoinPoint invocation) throws Throwable {
        return checkPermissions(invocation);
    }

    private Object checkPermissions(ProceedingJoinPoint pjp) throws Throwable {
        // 获取切入的 Method
        MethodSignature joinPointObject = (MethodSignature) pjp.getSignature();
        Method method = joinPointObject.getMethod();
        RequiredPermission requiredPermission = method.getAnnotation(RequiredPermission.class);
        String required = requiredPermission.value();
        userCenterService.checkPermissions(required);

        return pjp.proceed();
    }

}
