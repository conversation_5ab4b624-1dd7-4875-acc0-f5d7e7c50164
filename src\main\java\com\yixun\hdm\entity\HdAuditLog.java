package com.yixun.hdm.entity;



import com.baomidou.mybatisplus.annotation.TableField;
import com.yixun.hdm.convert.ListTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 隐患审查历史记录表(HdAuditLog)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-28 11:20:51
 */
@SuppressWarnings("serial")
@Data
@ApiModel("隐患审查历史记录表")
@TableName("hdm_hd_audit_log")
public class HdAuditLog extends Model<HdAuditLog> {

    @NotNull(message="[${column.comment}]不能为空")
    private Long id;

    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    @NotNull(message="[创建时间]不能为空")
    private Date createTime;

    /**
    * 隐患id
    */
    @ApiModelProperty("隐患id")
    @NotNull(message="[隐患id]不能为空")
    private Long hiddenDangerId;

    /**
    * 项目id
    */
    @ApiModelProperty("项目id")
    @NotNull(message="[项目id]不能为空")
    private Long projectId;

    /**
    * 项目名称
    */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
    * 公司id
    */
    @ApiModelProperty("公司id")
    @NotNull(message="[公司id]不能为空")
    private Long corporationId;

    /**
    * 公司名称
    */
    @ApiModelProperty("公司名称")
    private String corpName;

    /**
    * 岗位/工种批注
    */
    @ApiModelProperty("岗位/工种批注")
    private String workCategoryComment;

    /**
    * 岗位/工种
    */
    @ApiModelProperty("岗位/工种")
    private String workCategory;

    /**
    * 作业活动批注
    */
    @ApiModelProperty("作业活动批注")
    private String jobActivityComment;

    /**
    * 作业活动
    */
    @ApiModelProperty("作业活动")
    private String jobActivity;

    /**
    * 设备设施/物料批注
    */
    @ApiModelProperty("设备设施/物料批注")
    private String equipmentComment;

    /**
    * 设备设施/物料
    */
    @ApiModelProperty("设备设施/物料")
    private String equipment;

    /**
    * 隐患区域批注
    */
    @ApiModelProperty("隐患区域批注")
    private String hdRegionComment;

    /**
    * 隐患区域
    */
    @ApiModelProperty("隐患区域")
    private String hdRegion;

    /**
    * 隐患照片（可多图）
    */
    @ApiModelProperty("隐患照片（可多图）")
    @NotNull(message="[隐患照片（可多图）]不能为空")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> pictures;

    /**
    * 隐患照片审查批注
    */
    @ApiModelProperty("隐患照片审查批注")
    private String picturesComment;

    /**
    * 隐患描述
    */
    @ApiModelProperty("隐患描述")
    @NotNull(message="[隐患描述]不能为空")
    private String description;

    /**
    * 隐患描述审查批注
    */
    @ApiModelProperty("隐患描述审查批注")
    private String descriptionComment;

    /**
    * 专项类别
    */
    @ApiModelProperty("专项类别")
    private String specialCategory;

    /**
    * 专项类别明细
    */
    @ApiModelProperty("专项类别明细")
    private String specialCategoryDetail;

    /**
    * 专项类别审查批注
    */
    @ApiModelProperty("专项类别审查批注")
    private String specialCategoryComment;

    /**
    * 隐患类型
    */
    @ApiModelProperty("隐患类型")
    @NotNull(message="[隐患类型]不能为空")
    private String type;

    /**
    * 隐患类型审查批注
    */
    @ApiModelProperty("隐患类型审查批注")
    private String typeComment;

    /**
    * 隐患级别
    */
    @ApiModelProperty("隐患级别")
    @NotNull(message="[隐患级别]不能为空")
    private String level;

    /**
    * 隐患级别审查批注
    */
    @ApiModelProperty("隐患级别审查批注")
    private String levelComment;

    /**
    * 一级业务
    */
    @ApiModelProperty("一级业务")
    private String tierOneBusiness;

    /**
    * 一级业务审查批注
    */
    @ApiModelProperty("一级业务审查批注")
    private String tierOneBusinessComment;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 依据标准
    */
    @ApiModelProperty("依据标准")
    @NotNull(message="[依据标准]不能为空")
    private String standard;

    /**
    * 依据标准审查批注
    */
    @ApiModelProperty("依据标准审查批注")
    private String standardComment;

    /**
    * 可能导致后果
    */
    @ApiModelProperty("可能导致后果")
    @NotNull(message="[可能导致后果]不能为空")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> possibleConsequence;

    /**
    * 可能导致后果审查批注
    */
    @ApiModelProperty("可能导致后果审查批注")
    private String possibleConsequenceComment;

    /**
    * 危害因素分类
    */
    @ApiModelProperty("危害因素分类")
    private String riskFactorType;

    /**
    * 危害因素分类明细
    */
    @ApiModelProperty("危害因素分类明细")
    private String riskFactorTypeDetail;

    /**
    * 危害因素分类审查批注
    */
    @ApiModelProperty("危害因素分类审查批注")
    private String riskFactorTypeComment;

    /**
    * 风险点描述
    */
    @ApiModelProperty("风险点描述")
    private String riskPointName;

    /**
    * 风险点描述审查批注
    */
    @ApiModelProperty("风险点描述审查批注")
    private String riskPointComment;

    /**
    * 管理追溯
    */
    @ApiModelProperty("管理追溯")
    private String manageSource;

    /**
    * 管理追溯审查批注
    */
    @ApiModelProperty("管理追溯审查批注")
    private String manageSourceComment;

    /**
    * 管理追溯内容
    */
    @ApiModelProperty("管理追溯内容")
    private String manageSourceContent;

    /**
    * 建议整改措施
    */
    @ApiModelProperty("建议整改措施")
    private String rectificationSuggestion;

    /**
    * 建议整改措施审查批注
    */
    @ApiModelProperty("建议整改措施审查批注")
    private String rectificationSuggestionComment;

    /**
    * 整改期限（天）
    */
    @ApiModelProperty("整改期限（天）")
    private Integer rectifyDeadline;

    /**
    * 整改期限审查批注
    */
    @ApiModelProperty("整改期限审查批注")
    private String rectifyDeadlineComment;

    /**
    * 是否回访时发现
    */
    @ApiModelProperty("是否回访时发现")
    private Integer isFindByCallback;

    /**
    * 审查提交人
    */
    @ApiModelProperty("审查提交人")
    private String auditSubmitter;

    /**
    * 审查提交人id
    */
    @ApiModelProperty("审查提交人id")
    @NotNull(message="[审查提交人id]不能为空")
    private Long auditSubmitterId;

    /**
    * 审查员
    */
    @ApiModelProperty("审查员")
    private String auditor;

    /**
    * 审查员id
    */
    @ApiModelProperty("审查员id")
    private Long auditorId;

    /**
    * 审核状态
    */
    @ApiModelProperty("审核状态")
    @NotNull(message="[审核状态]不能为空")
    private String auditStatus;

    /**
    * 审查意见
    */
    @ApiModelProperty("审查意见")
    private String auditComment;

    @ApiModelProperty("归属类别；1：制度与操作规程；2：教育培训；3：设备管理；4：物料管理；5：作业环境管理；6：事故四不放过管理；7：应急管理；8：职业健康管理")
    private Integer belongCategory;

    @ApiModelProperty(value="归属类别批注", required = true)
    private String belongCategoryComment;

}

