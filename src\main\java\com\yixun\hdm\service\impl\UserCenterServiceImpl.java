package com.yixun.hdm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yixun.hdm.entity.dto.*;
import com.yixun.hdm.entity.em.DictType;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.UserCenterService;
import com.yixun.hdm.utils.AdminUserHelper;
import com.yixun.hdm.utils.OkHttpKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Slf4j
@Service
public class UserCenterServiceImpl implements UserCenterService {

    @Value("${api.userCenterApi}")
    private String userCenterApi;

    @Value("${api.token}")
    private String token;

    @Override
    public StaffUserDto getAdminUser(Long adminUserId) {
        StaffUserDto staffUser = null;
        //获取用户信息
        Map<String, String> params = new HashMap<>();
        params.put("userId", adminUserId.toString());
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/admin/getById",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                staffUser = connGet.getJSONObject("data").toJavaObject(StaffUserDto.class);
                return staffUser;
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public CorporationDto getCorporation(Long corporationId) {
        Map<String, String> params = new HashMap<>();
        params.put("corporationId", corporationId.toString());
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/corporation/getById",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getJSONObject("data").toJavaObject(CorporationDto.class);
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public ProjectDto getProject(Long projectId) {
        Map<String, String> params = new HashMap<>();
        params.put("projectId", projectId.toString());
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/project/getById",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getJSONObject("data").toJavaObject(ProjectDto.class);
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public Boolean checkPermissions(String required) {
        Long userId = AdminUserHelper.getCurrentUserId();
        //获取用户信息
        Map<String, String> params = new HashMap<>();
        params.put("id", userId.toString());
        params.put("required", required);
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/admin/getPermissionsForAdmin",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return Boolean.valueOf(connGet.getString("data"));
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public List<CorporationProjectDto> getCorporationList(Long projectId) {
        Map<String, String> params = new HashMap<>();
        params.put("projectId", projectId.toString());
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/corporation/getCorporationProjectList",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getJSONArray("data").toJavaList(CorporationProjectDto.class);
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public Integer getCorporationCount(Long projectId) {
        Map<String, String> params = new HashMap<>();
        if (projectId!=null){
            params.put("projectId", projectId.toString());
        }
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/corporation/getCorporationProjectCount",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getInteger("data");
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public OccupInjuryStatisticsDto getOccupInjuryStatistics(Long projectId) {
        Map<String, String> params = new HashMap<>();
        if (projectId!=null){
            params.put("projectId", projectId.toString());
        }
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/projectManagement/getOccupationalInjuryStatistics",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getJSONObject("data").toJavaObject(OccupInjuryStatisticsDto.class);
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public CorporationProjectDto getCorporationProjectById(Long corporationProjectId) {
        Map<String, String> params = new HashMap<>();
        if (corporationProjectId!=null){
            params.put("corporationProjectId", corporationProjectId.toString());
        }
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/corporation/getCorporationProjectById",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getJSONObject("data").toJavaObject(CorporationProjectDto.class);
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public List<YqycInfoDto> getYqycInfoList(List<CorporationProjectDto> corpProjList) {
        Map<String, Object> params = new HashMap<>();
        params.put("corpProjList", corpProjList);
        try {
            JSONObject connGet = OkHttpKit.apiPostWithToken(userCenterApi + "/api/corporation/getYqycInfoList",
                    JSON.toJSONString(params), token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getJSONArray("data").toJavaList(YqycInfoDto.class);
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public CorporationProjectDto getCorporationProject(Long corporationId, Long projectId) {
        Map<String, String> params = new HashMap<>();
        params.put("corporationId", corporationId.toString());
        params.put("projectId", projectId.toString());
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/corporationProject/getCorporationProject",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getJSONObject("data").toJavaObject(CorporationProjectDto.class);
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    public List<DataDict> getDataDictionary(DictType type) {
        Map<String, String> params = new HashMap<>();
        params.put("datatype", type.name());
        try {
            JSONObject connGet = OkHttpKit.apiGetWithToken(userCenterApi + "/api/dict/get-dict-list",
                    params, token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                return connGet.getJSONArray("data").toJavaList(DataDict.class);
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

}
