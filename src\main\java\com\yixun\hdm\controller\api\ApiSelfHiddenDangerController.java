package com.yixun.hdm.controller.api;

import com.yixun.hd.api.SelfCheckRecordApi;
import com.yixun.hd.api.SelfHiddenDangerApi;
import com.yixun.hd.bean.in.SelfHiddenDangerIn;
import com.yixun.hd.bean.out.SelfCheckRecordOut;
import com.yixun.hd.bean.out.SelfHiddenDangerOut;
import com.yixun.hdm.entity.SelfCheckRecord;
import com.yixun.hdm.entity.SelfHiddenDanger;
import com.yixun.hdm.service.SelfCheckRecordService;
import com.yixun.hdm.service.SelfHiddenDangerService;
import com.yixun.hdm.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;


@RestController
public class ApiSelfHiddenDangerController implements SelfHiddenDangerApi {

    @Autowired
    private SelfHiddenDangerService selfHiddenDangerService;

    @Override
    public List<SelfHiddenDangerOut> getList(SelfHiddenDangerIn in) {
        List<SelfHiddenDanger> list = selfHiddenDangerService.getList(in);
        return BeanUtils.copyToOutList(list, SelfHiddenDangerOut.class);
    }
}
