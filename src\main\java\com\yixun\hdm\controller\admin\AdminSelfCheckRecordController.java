package com.yixun.hdm.controller.admin;



import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.SelfCheckRecord;
import com.yixun.hdm.service.SelfCheckRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.Serializable;
import java.util.List;

/**
 * 自查记录(SelfCheckRecord)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-26 17:09:51
 */
@Api(tags = "自查记录")
@RestController
@RequestMapping("/admin/self/checkRecord")
public class AdminSelfCheckRecordController {
    /**
     * 服务对象
     */
    @Autowired
    private SelfCheckRecordService selfCheckRecordService;

    /**
     * 分页查询所有数据
     *
     * @param commonPage 分页对象
     * @param selfCheckRecord 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页列表查询")
    @GetMapping("pageList")
    public CommonResult<List<SelfCheckRecord>> pageList(CommonPage commonPage, SelfCheckRecord selfCheckRecord) {
        Page<SelfCheckRecord> page = new Page<>(commonPage.getPageNum(),commonPage.getPageSize());
        return CommonResult.successData(selfCheckRecordService.pageList(page,selfCheckRecord));
    }



    /**
     * 删除数据
     *
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("delete/{id}")
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        return CommonResult.successData(this.selfCheckRecordService.delete(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping("export")
    public void export(SelfCheckRecord selfCheckRecord) {
        selfCheckRecordService.export(selfCheckRecord);
    }
}

