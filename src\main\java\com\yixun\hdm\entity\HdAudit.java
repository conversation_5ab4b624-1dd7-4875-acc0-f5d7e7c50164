package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 隐患审查记录表
 * @TableName hdm_hd_audit
 */
@TableName(value ="hdm_hd_audit")
@Data
public class HdAudit implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 公司id
     */
    private Long corporationId;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 审查提交人
     */
    private String auditSubmitter;

    /**
     * 审查提交人id
     */
    private Long auditSubmitterId;

    /**
     * 计划安排的审查员
     */
    private String arrangedAuditor;

    /**
     * 计划安排的审查员id
     */
    private Long arrangedAuditorId;

    /**
     * 审查提交时间
     */
    private Date auditSubmitTime;

    /**
     * 审查过期时间
     */
    private Date auditExpiredTime;

    /**
     * 审查员列表
     */
    private String auditor;

    /**
     * 提交数量
     */
    private Integer submitCount;

    /**
     * 提交隐患id列表
     */
    private String submitList;

    /**
     * 完成审查数量
     */
    private Integer finishCount;

    /**
     * 完成审查隐患id列表
     */
    private String finishList;

    /**
     * 是否完成: 0-否，1-是
     */
    private Integer isFinish;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}