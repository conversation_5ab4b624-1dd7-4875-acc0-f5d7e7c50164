package com.yixun.hdm.bean.in;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 自查企业(SelfCheckCorp)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-26 16:38:31
 */
@SuppressWarnings("serial")
@Data
public class SelfCheckCorpPushIn {

    @ApiModelProperty("主键ID")
    @NotNull(message="[主键ID]不能为空")
    private Long id;

    @ApiModelProperty("是否强制推送")
    @NotNull(message="[是否强制推送]不能为空")
    private Boolean force;

}

