package com.yixun.hdm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.MessageService;
import com.yixun.hdm.utils.OkHttpKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class MessageServiceImpl implements MessageService {

    @Value("${api.messageCenterApi}")
    private String messageCenterApi;

    private String token="{\"appId\":13509559603962367,\"appSecretEncrypt\":\"f16c3647a87472c1570efdc1da2acd1b\"}";

    @Override
    @Async
    public void sendDing(String message, String phone) {
        Map<String, Object> params = new HashMap<>();
        params.put("content", message);
        params.put("phones", Arrays.asList(phone));
        try {
            JSONObject connGet = OkHttpKit.apiPostWithToken(messageCenterApi + "/api/dingding/sendTextSms",
                    JSON.toJSONString(params), token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                log.info(JSON.toJSONString(connGet));
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }
}
