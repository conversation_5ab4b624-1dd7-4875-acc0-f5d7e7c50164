package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.dao.HiddenDangerSuggestionMapper;
import com.yixun.hdm.entity.HiddenDangerSuggestion;
import com.yixun.hdm.service.HiddenDangerSuggestionService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 隐患建议改善措施(HiddenDangerSuggestion)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-27 14:06:54
 */
@Service("hiddenDangerSuggestionService")
public class HiddenDangerSuggestionServiceImpl extends ServiceImpl<HiddenDangerSuggestionMapper, HiddenDangerSuggestion> implements HiddenDangerSuggestionService {

    @Override
    public List<HiddenDangerSuggestion> list(Long hdId) {
        LambdaQueryWrapper<HiddenDangerSuggestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HiddenDangerSuggestion::getHdId,hdId);
        return list(wrapper);
    }
}

