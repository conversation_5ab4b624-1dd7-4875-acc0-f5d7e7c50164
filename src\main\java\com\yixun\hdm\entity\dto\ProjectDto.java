package com.yixun.hdm.entity.dto;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateJsonSerializer;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ProjectDto {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createDate;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateDate;
    private Integer pmId;
    private Integer year;
    private String projectName;
    private String projectCode;
    private Integer type;
    private Integer status;
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date startDate;
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date endDate;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long governmentId;
    private String customer;
    private List region;
    private String projectBrief;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long winningPrice;
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date winningDate;
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date contractDate;
    private String projectManager;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectManagerId;
    private String techManager;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long techManagerId;
    private String techAssistant;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long techAssistantId;
    private String publicityManager;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long publicityManagerId;
    private Integer turnCount;
    private String attorneyUrl;
    private String riskCategory;
    private String diagBasis;
    private String diagFlow;
    private Boolean isSelfRegister;
    private Boolean hasRedPacket;
    private Boolean hasCertificate;

    public void setRegion(String region) {
        this.region = JSON.parseObject(region, List.class);
    }

}
