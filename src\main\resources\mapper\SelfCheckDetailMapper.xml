<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yixun.hdm.dao.SelfCheckDetailMapper">

	<select id="selectSelfCheckCorpStatistic" resultType="com.yixun.hdm.entity.vo.SelfCheckCorpStatisticVO" parameterType="string">
        SELECT COALESCE(SUM(all_not_negative_one_flag), 0)                             AS completedSelfCheckCorp,
               COALESCE(SUM(CASE WHEN all_negative_one_flag = 0 THEN 1 ELSE 0 END), 0) AS alreadySelfCheckCorp
        FROM (SELECT t1.check_record_id,
                     -- 是否全为 -1
                     CASE
                         WHEN COUNT(*) = SUM(t1.result = -1) THEN 1
                         ELSE 0
                         END AS all_negative_one_flag,
                     -- 是否全为非 -1
                     CASE
                         WHEN COALESCE(SUM(t1.result = -1), 0) = 0 THEN 1
                         ELSE 0
                         END AS all_not_negative_one_flag
              FROM self_check_detail t1
                       JOIN self_check_record t2 ON t1.check_record_id = t2.id
              WHERE t2.project_id = #{projectId}
              GROUP BY t1.check_record_id) AS sub
    </select>

	<select id="totalRecommendSelfCheck" resultType="com.yixun.hdm.entity.vo.SelfCheckSituationStatisticVO" parameterType="string">
		SELECT count(t1.id) AS totalRecommendSelfCheck
		FROM self_check_detail AS t1
			     LEFT JOIN self_check_record AS t2 ON t1.check_record_id = t2.id
		WHERE t2.project_id = #{projectId};
	</select>

	<select id="totalHiddenDanger" resultType="com.yixun.hdm.entity.vo.SelfCheckSituationStatisticVO" parameterType="string">
		SELECT count(t1.id) AS totalHiddenDanger
		FROM self_check_detail AS t1
			     LEFT JOIN self_check_record AS t2 ON t1.check_record_id = t2.id
		WHERE t2.project_id = #{projectId} AND
		t1.result = 1;
	</select>

	<select id="hiddenDangersRectifiedNum" resultType="com.yixun.hdm.entity.vo.HiddenDangerRectificationVO" parameterType="string">
		SELECT
			count(t1.id) AS hiddenDangersRectifiedNum
		FROM
			self_check_detail AS t1
				LEFT JOIN self_check_record AS t2 ON t1.check_record_id = t2.id
				LEFT JOIN self_hidden_danger AS t3 ON t1.self_hd_id = t3.id
		WHERE
			t2.project_id = #{projectId}
		  AND t1.result = 1
		  AND t3.`status` = 3;
	</select>

	<select id="corpSelfCheckStatistic" resultType="com.yixun.hdm.entity.vo.SelfCheckResult">
		SELECT
		t1.*,
		COUNT(t2.id) AS totalRecommendSelfCheck,
		COALESCE(SUM(t2.result = 1), 0) AS totalHiddenDanger,
		COALESCE(SUM(t2.result = -1), 0) AS totalNoSelfCheck,
		COALESCE(SUM(t3.`status` = 3), 0) AS hiddenDangersRectifiedNum,
		COALESCE(
		ROUND(COALESCE(SUM(t2.result != -1), 0) * 100.0 / NULLIF(COUNT(t2.id), 0), 2),
		0
		) AS selfCheckProgress,
		ROUND(
		COALESCE(SUM(t2.result = 1), 0) * 100.0 / NULLIF(COUNT(t2.id), 0),
		2
		) AS hiddenDangerDetectionRate,
		COALESCE(
		ROUND(
		COALESCE(SUM(t3.`status` = 3), 0) * 100.0 / NULLIF(COALESCE(SUM(t2.result = 1), 0), 0),
		2
		),
		0
		) AS hiddenDangersRectifiedRate
		FROM self_check_record AS t1
		LEFT JOIN self_check_detail AS t2 ON t1.id = t2.check_record_id
		LEFT JOIN self_hidden_danger AS t3 ON t2.self_hd_id = t3.id
		WHERE t1.project_id = #{projectId}
		<if test="corpName != null and corpName.trim() != ''">
			AND t1.corp_name LIKE CONCAT('%', #{corpName}, '%')
		</if>
		GROUP BY t1.corp_id
		HAVING 1 = 1
		<if test="minSelfCheckProgress != null">
			AND selfCheckProgress &gt;= #{minSelfCheckProgress}
		</if>
		<if test="maxSelfCheckProgress != null">
			AND selfCheckProgress &lt;= #{maxSelfCheckProgress}
		</if>
		<if test="minHiddenDangersRectifiedRate != null">
			AND hiddenDangersRectifiedRate &gt;= #{minHiddenDangersRectifiedRate}
		</if>
		<if test="maxHiddenDangersRectifiedRate != null">
			AND hiddenDangersRectifiedRate &lt;= #{maxHiddenDangersRectifiedRate}
		</if>
		<if test="minHiddenDangerDetectionRate != null">
			AND hiddenDangerDetectionRate &gt;= #{minHiddenDangerDetectionRate}
		</if>
		<if test="maxHiddenDangerDetectionRate != null">
			AND hiddenDangerDetectionRate &lt;= #{maxHiddenDangerDetectionRate}
		</if>
		ORDER BY selfCheckProgress DESC
	</select>

</mapper>

