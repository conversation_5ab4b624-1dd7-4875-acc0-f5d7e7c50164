package com.yixun.hdm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.Regulations;
import com.yixun.hdm.entity.RegulationsMemo;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.service.RegulationsMemoService;
import com.yixun.hdm.dao.RegulationsMemoMapper;
import com.yixun.hdm.utils.BeanUtils;
import com.yixun.hdm.utils.OkHttpKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 *
 */
@Slf4j
@Service
public class RegulationsMemoServiceImpl extends ServiceImpl<RegulationsMemoMapper, RegulationsMemo>
    implements RegulationsMemoService{

    @Value("${api.regulationSearchApi}")
    private String regulationSearchApi;

    @Value("${api.token}")
    private String token;

    @Resource
    @Lazy
    private HiddenDangerService hiddenDangerService;

    @Override
    public Page<RegulationsMemo> getList(CommonPage commonPage) {
        QueryWrapper<RegulationsMemo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_used", false);

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<RegulationsMemo>) page(page, queryWrapper);
    }

    @Override
    @Transactional
    public void confirm(RegulationsMemo regulationsMemo) {
        regulationsMemo.setIsUsed(true);
        updateById(regulationsMemo);

        HiddenDanger hiddenDanger = hiddenDangerService.getById(regulationsMemo.getSourceId());
        if (hiddenDanger!=null){
            List<Regulations> regulationsList = JSON.parseArray(hiddenDanger.getStandard(), Regulations.class);
            Optional<Regulations> first = regulationsList.stream().filter(r -> regulationsMemo.getInnerStandardId().equals(r.getId())).findFirst();
            if (first.isPresent()){
                Regulations regulations = first.get();
                BeanUtils.copyProperties(regulationsMemo, regulations);
                regulations.setId(regulationsMemo.getInnerStandardId());
                hiddenDanger.setStandard(JSON.toJSONString(regulationsList));
                hiddenDangerService.updateById(hiddenDanger);
            }
        }

        try {
            JSONObject connGet = OkHttpKit.apiPostWithToken(regulationSearchApi + "/api/search/insert",
                    JSON.toJSONString(regulationsMemo), token);
            if (connGet.containsKey("code") && connGet.get("code").equals(200)) {
                log.info("regulationSearchApi:"+JSON.toJSONString(connGet));
            } else {
                throw new DataErrorException(connGet.getString("msg"));
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new DataErrorException(e.getMessage());
        }
    }

    @Override
    @Transactional
    public void update(RegulationsMemo regulationsMemo) {
        regulationsMemo.setIsUsed(true);
        updateById(regulationsMemo);

        HiddenDanger hiddenDanger = hiddenDangerService.getById(regulationsMemo.getSourceId());
        if (hiddenDanger!=null){
            List<Regulations> regulationsList = JSON.parseArray(hiddenDanger.getStandard(), Regulations.class);
            Optional<Regulations> first = regulationsList.stream().filter(r -> regulationsMemo.getInnerStandardId().equals(r.getId())).findFirst();
            if (first.isPresent()){
                Regulations regulations = first.get();
                BeanUtils.copyProperties(regulationsMemo, regulations);
                regulations.setId(regulationsMemo.getInnerStandardId());
                hiddenDanger.setStandard(JSON.toJSONString(regulationsList));
                hiddenDangerService.updateById(hiddenDanger);
            }
        }
    }
}




