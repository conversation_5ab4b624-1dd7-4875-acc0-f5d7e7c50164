<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yixun.hdm.dao.UserRegionMapper">
    <select id="getByMonth" resultType="com.yixun.hdm.bean.out.UserMonthlyCountOut">
        SELECT create_time, DATE_FORMAT(create_time, '%m') AS `month`, COUNT(*) AS `count`
        FROM hdm_user_region
        where create_time between #{startTime} and #{endTime}
        <if test="level==1">
            and corp_region_l1_code = #{regionCode}
        </if>
        <if test="level==2">
            and corp_region_l2_code = #{regionCode}
        </if>
        <if test="level==3">
            and corp_region_l3_code = #{regionCode}
        </if>
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
        GROUP BY `month`
        order by create_time
    </select>
    <select id="getByRegion" resultType="com.yixun.hdm.bean.out.UserRegionCountOut">
        SELECT corp_region_l1_name as l1, corp_region_l2_name as l2, corp_region_l3_name as l3, COUNT(*) AS `count`
        FROM hdm_user_region
        <if test="level==0">
            GROUP BY `corp_region_l1_name`
        </if>
        <where>
            <if test="projectId!=null">
                and project_id = #{projectId}
            </if>
            <if test="level==1">
                and corp_region_l1_code = #{regionCode}
                GROUP BY `corp_region_l2_name`
            </if>
            <if test="level==2">
                and corp_region_l2_code = #{regionCode}
                GROUP BY `corp_region_l3_name`
            </if>
            <if test="level==3">
                and corp_region_l3_code = #{regionCode}
                GROUP BY `corp_region_l3_name`
            </if>
        </where>
    </select>
    <select id="getNoneRegion" resultType="java.lang.Integer">
        SELECT COUNT(*) AS `count`
        FROM hdm_user_region
        where corp_region_l1_code is null
        <if test="projectId!=null">
            and project_id = #{projectId}
        </if>
    </select>
</mapper>