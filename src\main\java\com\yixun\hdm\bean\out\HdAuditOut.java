package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class HdAuditOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    private String projectName;
    private String corpName;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;
    private String auditor;
    private String auditSubmitter;
    private int submitCount;
    private int finishCount;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date auditSubmitTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date auditExpiredTime;

}
