package com.yixun.hdm.controller.admin;



import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.SelfCheckDetail;
import com.yixun.hdm.service.SelfCheckDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.Serializable;
import java.util.List;

/**
 * 自查记录详情(SelfCheckDetail)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-26 18:10:14
 */
@Api(tags = "自查记录详情")
@RestController
@RequestMapping("/admin/self/checkDetail")
public class AdminSelfCheckDetailController {
    /**
     * 服务对象
     */
    @Autowired
    private SelfCheckDetailService selfCheckDetailService;

    /**
     * 分页查询所有数据
     *
     * @param commonPage 分页对象
     * @param selfCheckDetail 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页列表查询")
    @GetMapping("pageList")
    public CommonResult<List<SelfCheckDetail>> pageList(CommonPage commonPage, SelfCheckDetail selfCheckDetail) {
        return CommonResult.successData(selfCheckDetailService.pageList(commonPage,selfCheckDetail));
    }



    /**
     * 删除数据
     *
     * @param id 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("delete/{id}")
    public CommonResult<Boolean> delete(@PathVariable("id") Long id) {
        return CommonResult.successData(selfCheckDetailService.removeById(id));
    }

}

