package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CorpSafeListOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    @ApiModelProperty(value="项目id")
    private Long projectId;

    @ApiModelProperty(value="项目名称")
    private String projectName;

    @JsonSerialize(using = LongJsonSerializer.class)
    @ApiModelProperty("企业ID")
    private Long corpId;

    @ApiModelProperty("公司名称")
    private String corpName;

    @ApiModelProperty("周期新发工伤")
    private Integer workInjuryNum;

    @ApiModelProperty("工伤同比增长率")
    private String workInjuryGrowthRate;

    private String workInjuryGrowthRateStr;

    @ApiModelProperty("调研查出隐患")
    private Integer surveyHdNum;

    @ApiModelProperty("已整改隐患数")
    private Integer improvedHdNum;

    @ApiModelProperty("总体整改率")
    private String improveRate;

    private String improveRateStr;

    @ApiModelProperty("逾期未整改隐患")
    private Integer overdueHdNum;

    @ApiModelProperty("逾期5天未整改数")
    private Integer overdue5DaysHdNum;

    @ApiModelProperty("逾期10天未整改数")
    private Integer overdue10DaysHdNum;

}