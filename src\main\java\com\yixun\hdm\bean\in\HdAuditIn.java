package com.yixun.hdm.bean.in;

import com.yixun.hdm.entity.em.AuditStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class HdAuditIn {

    @ApiModelProperty(value="隐患审查id")
    private Long hdAuditId;

    @ApiModelProperty(value="隐患id")
    private Long hiddenDangerId;

    @ApiModelProperty(value="审查状态")
    private AuditStatus auditStatus;

    @ApiModelProperty(value="审查意见")
    private String auditComment;

    @ApiModelProperty(value="隐患照片（可多图）")
    private List pictures;

    @ApiModelProperty(value="专项类别")
    private String specialCategory;

    @ApiModelProperty(value="专项类别明细")
    private List specialCategoryDetail;

    @ApiModelProperty(value="隐患描述")
    private String description;

    @ApiModelProperty(value="隐患类型")
    private String type;

    @ApiModelProperty(value="隐患级别")
    private String level;

    @ApiModelProperty(value="备注")
    private String remark;

    @ApiModelProperty(value="文件（可多个）")
    private List files;

    @ApiModelProperty(value="依据标准")
    private List standard;

    @ApiModelProperty(value="可能导致后果")
    private List possibleConsequence;

    @ApiModelProperty(value="所属风险点")
    private String riskPointName;

    @ApiModelProperty(value="危害因素分类")
    private String riskFactorType;

    @ApiModelProperty(value="危害因素分类明细")
    private List riskFactorTypeDetail;

    @ApiModelProperty(value="建议改善措施")
    private String rectificationSuggestion;

    @ApiModelProperty(value="建议改善措施批注")
    private String rectificationSuggestionComment;

    @ApiModelProperty(value="改善期限（天）")
    private Integer rectifyDeadline;

    @ApiModelProperty(value="改善期限批注")
    private Integer rectifyDeadlineComment;

    @ApiModelProperty(value="是否回访时发现")
    private Boolean isFindByCallback;

    @ApiModelProperty(value="隐患照片审查批注")
    private String picturesComment;

    @ApiModelProperty(value="隐患描述审查批注")
    private String descriptionComment;

    @ApiModelProperty(value="专项类别审查批注")
    private String specialCategoryComment;

    @ApiModelProperty(value="隐患类型审查批注")
    private String typeComment;

    @ApiModelProperty(value="隐患级别审查批注")
    private String levelComment;

    @ApiModelProperty(value="依据标准审查批注")
    private String standardComment;

    @ApiModelProperty(value="可能导致后果审查批注")
    private String possibleConsequenceComment;

    @ApiModelProperty(value="危害因素分类审查批注")
    private String riskFactorTypeComment;

    @ApiModelProperty(value="所属风险点审查批注")
    private String riskPointComment;

    @ApiModelProperty(value="是否需老师修改")
    private Boolean isNeedEdit;

    /**
     * 岗位/工种批注
     */
    @ApiModelProperty("岗位/工种批注")
    private String workCategoryComment;

    /**
     * 岗位/工种
     */
    @ApiModelProperty("岗位/工种")
    private String workCategory;

    /**
     * 作业活动批注
     */
    @ApiModelProperty("作业活动批注")
    private String jobActivityComment;

    /**
     * 作业活动
     */
    @ApiModelProperty("作业活动")
    private String jobActivity;

    /**
     * 设备设施/物料批注
     */
    @ApiModelProperty("设备设施/物料批注")
    private String equipmentComment;

    /**
     * 设备设施/物料
     */
    @ApiModelProperty("设备设施/物料")
    private String equipment;

    /**
     * 隐患区域批注
     */
    @ApiModelProperty("隐患区域批注")
    private String hdRegionComment;

    /**
     * 隐患区域
     */
    @ApiModelProperty("隐患区域")
    private String hdRegion;

    @ApiModelProperty("归属类别；1：制度与操作规程；2：教育培训；3：设备管理；4：物料管理；5：作业环境管理；6：事故四不放过管理；7：应急管理；8：职业健康管理")
    private Integer belongCategory;

    @ApiModelProperty(value="归属类别批注", required = true)
    private String belongCategoryComment;

}
