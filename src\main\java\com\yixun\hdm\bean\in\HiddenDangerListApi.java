package com.yixun.hdm.bean.in;

import com.yixun.hdm.entity.em.HiddenDangerStatus;
import lombok.Data;

import java.util.List;

@Data
public class HiddenDangerListApi {

    private Long projectId;
    private String projectName;
    private Long corporationId;
    private String corpName;
    private HiddenDangerStatus status;
    private String gifFile;
    private List pictures;
    private String description;
    private String type;
    private String level;
    private String tierOneBusiness;
    private String remark;
    private List files;
    private List standard;
    private List possibleConsequence;
    private String riskFactorType;
    private String riskDescription;
    private String manageSource;
    private String manageSourceContent;
    private String rectificationSuggestion;
    private Integer rectifyDeadline;

    //rectification
    private List rectPictures;
    private String rectDescription;

    //verify
    private Boolean isPass;
    private String reason;
}
