package com.yixun.hdm.bean.in;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RectificationIn {

    @ApiModelProperty(value="隐患id", required = true)
    private Long hiddenDangerId;

    @ApiModelProperty(value="隐患照片（可多图）", required = true)
    private List pictures;

    @ApiModelProperty(value="改善描述", required = true)
    private String description;

    @ApiModelProperty(value="改善负责人", required = true)
    private String inChargeName;

}
