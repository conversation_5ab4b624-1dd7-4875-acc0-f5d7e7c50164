package com.yixun.hdm.bean.weixin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WxMsg {

	private String touser;
	private String template_id;
	private String url;
	private Map<String, Map<String, String>> data;
	
	public Map<String, Map<String, String>> getData() {
		return data;
	}

	public void setData(Map<String, Map<String, String>> data) {
		this.data = data;
	}

	public String getTouser() {
		return touser;
	}

	public void setTouser(String touser) {
		this.touser = touser;
	}

	public String getTemplate_id() {
		return template_id;
	}

	public void setTemplate_id(String template_id) {
		this.template_id = template_id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public static Map<String, Map<String, String>> formatData(List<NotificationItem> params ){
		Map<String, Map<String, String>> data = new HashMap<>();
		
		for(NotificationItem item:params ) {
			Map<String, String> temp = new HashMap<>();
			temp.put("color", item.getColor());
			temp.put("value", item.getContent() );
			data.put( item.getKey() , temp);
		}
		
		return  data;
	}

}
