package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.SelfCheckCorpDeleteIn;
import com.yixun.hdm.bean.in.SelfCheckCorpPageQueryIn;
import com.yixun.hdm.entity.SelfCheckCorp;

/**
 * 自查企业(SelfCheckCorp)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-26 15:54:42
 */
public interface SelfCheckCorpService extends IService<SelfCheckCorp> {

    Boolean push(Long id,boolean isForce);

    Boolean remove(SelfCheckCorpDeleteIn in);

    Boolean pushAll(Long projectId);

    void pushAllAsync(SelfCheckCorpPageQueryIn queryIn, String userPhone, String projectName);

    void insert(SelfCheckCorp selfCheckCorp);

    Page<SelfCheckCorp> pageList(SelfCheckCorpPageQueryIn queryIn);

	Long countByProjectId(String projectId);

}

