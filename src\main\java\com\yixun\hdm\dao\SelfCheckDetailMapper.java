package com.yixun.hdm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.entity.SelfCheckDetail;
import com.yixun.hdm.entity.vo.*;

import java.util.List;

/**
 * 自查记录详情(SelfCheckDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-26 18:10:14
 */
public interface SelfCheckDetailMapper extends BaseMapper<SelfCheckDetail> {

	SelfCheckCorpStatisticVO selectSelfCheckCorpStatistic(String projectId);

	SelfCheckSituationStatisticVO totalRecommendSelfCheck(String projectId);

	SelfCheckSituationStatisticVO totalHiddenDanger(String projectId);

	HiddenDangerRectificationVO hiddenDangersRectifiedNum(String projectId);

	List<SelfCheckResult> corpSelfCheckStatistic(SelfCheckQueryParam selfCheckQueryParam);


}

