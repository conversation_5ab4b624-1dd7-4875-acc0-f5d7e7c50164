<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yixun.hdm.dao.UserCollectMapper">

    <resultMap type="com.yixun.hdm.entity.UserCollect" id="UserCollectMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="contentId" column="content_id" jdbcType="INTEGER"/>
        <result property="contentType" column="content_type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hidden_danger.user_collect(user_idcontent_idcontent_typecreate_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.userId}#{entity.contentId}#{entity.contentType}#{entity.createTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hidden_danger.user_collect(user_idcontent_idcontent_typecreate_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userId}#{entity.contentId}#{entity.contentType}#{entity.createTime})
        </foreach>
        on duplicate key update
user_id = values(user_id) content_id = values(content_id) content_type = values(content_type) create_time = values(create_time)     </insert>

</mapper>

