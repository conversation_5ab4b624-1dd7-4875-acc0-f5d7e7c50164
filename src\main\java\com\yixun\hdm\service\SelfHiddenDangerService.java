package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.hd.bean.in.SelfHiddenDangerIn;
import com.yixun.hd.bean.out.SelfHiddenDangerOut;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.entity.SelfHiddenDanger;

import java.util.List;

/**
 * 自查隐患详情表(SelfHiddenDanger)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-27 16:33:59
 */
public interface SelfHiddenDangerService extends IService<SelfHiddenDanger> {

    void export(SelfHiddenDanger selfHiddenDanger);

    Page<SelfHiddenDanger> pageList(CommonPage commonPage, SelfHiddenDanger selfHiddenDanger);

    List<SelfHiddenDanger> getList(SelfHiddenDangerIn in);
}

