package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.entity.OperationLogs;
import com.yixun.hdm.service.OperationLogsService;
import com.yixun.hdm.dao.OperationLogsMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class OperationLogsServiceImpl extends ServiceImpl<OperationLogsMapper, OperationLogs>
    implements OperationLogsService{

    @Override
    public List<OperationLogs> getByCorporation(Long corporationId) {
        QueryWrapper<OperationLogs> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("corporation_id", corporationId);
        return list(queryWrapper);
    }
}




