package com.yixun.hdm.service.impl;

import com.alibaba.fastjson.JSON;
import com.yixun.hdm.entity.HdmPostLogs;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.service.AsyncService;
import com.yixun.hdm.service.PostLogsService;
import com.yixun.hdm.service.UserCenterService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class AsyncServiceImpl implements AsyncService {

    @Resource
    private PostLogsService postLogsService;

    @Resource
    private UserCenterService userCenterService;

    @Override
    @Async
    public void recordLogAdmin(ProceedingJoinPoint invocation, String servletPath, Long userId) {

        StaffUserDto staffUser = userCenterService.getAdminUser(userId);

        HdmPostLogs postLogs = new HdmPostLogs();
        postLogs.setOptTime(new Date());
        postLogs.setOperator(staffUser.getRealName());
        postLogs.setOperatorId(userId);
        postLogs.setPath(servletPath);

        MethodSignature method = (MethodSignature) invocation.getSignature();
        ApiOperation apiOperation = AnnotationUtils.findAnnotation(method.getMethod(), ApiOperation.class);
        if (apiOperation != null) {
            String label = apiOperation.value();
            postLogs.setFuncName(label);
        }
        postLogs.setType("staff");
        postLogs.setArgs(JSON.toJSONString(invocation.getArgs()));

        postLogsService.insert(postLogs);
    }

    @Override
    public void recordLog(ProceedingJoinPoint invocation, String servletPath, String type) {

        HdmPostLogs postLogs = new HdmPostLogs();
        postLogs.setOptTime(new Date());
        postLogs.setOperator(type);
        postLogs.setOperatorId(0L);
        postLogs.setPath(servletPath);

        MethodSignature method = (MethodSignature) invocation.getSignature();
        ApiOperation apiOperation = AnnotationUtils.findAnnotation(method.getMethod(), ApiOperation.class);
        if (apiOperation != null) {
            String label = apiOperation.value();
            postLogs.setFuncName(label);
        }
        postLogs.setType(type);
        postLogs.setArgs(JSON.toJSONString(invocation.getArgs()));

        postLogsService.insert(postLogs);
    }

}
