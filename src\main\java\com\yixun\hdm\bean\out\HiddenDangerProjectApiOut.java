package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.List;

@Data
public class HiddenDangerProjectApiOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;
    private String corpName;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    private Integer hdTotalCount;
    private Long hdDoneCount;
    private Integer majorHdCount;
    private List<String> majorHdDescList;
}
