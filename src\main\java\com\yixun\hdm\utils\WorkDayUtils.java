package com.yixun.hdm.utils;

import com.alibaba.fastjson.JSONObject;
import com.yixun.hdm.exception.DataErrorException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class WorkDayUtils {

    private static String url = "https://calc.ygcf.info/api/v1/workday/end";
    private static String token = "64dc29b339116";

    public static Date getWorkDate(Date startDate, Integer dayAfter) {

//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        Map<String, String> params = new HashMap<>();
//        params.put("start_date", sdf.format(startDate));
//        params.put("days", dayAfter+1+"");
//        params.put("token", token);
//        try {
//            JSONObject connGet = OkHttpKit.connGet(url, params);
//            if (connGet.containsKey("code") && connGet.get("code").equals(0)) {
//                String data = connGet.getString("data");
//                Date parse = sdf.parse(data);
//                Calendar calendar = Calendar.getInstance();
//                calendar.setTime(parse);
//                calendar.set(Calendar.HOUR_OF_DAY, 23);
//                calendar.set(Calendar.MINUTE, 59);
//                calendar.set(Calendar.SECOND, 59);
//
//                return calendar.getTime();
//            } else {
//                throw new DataErrorException(connGet.getString("msg"));
//            }
//        } catch (IOException | ParseException e) {
//            log.error(e.getMessage());
//        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        calendar.add(Calendar.DAY_OF_YEAR, dayAfter);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 58);

        return calendar.getTime();
    }

    public static Date getWorkDate2(Date startDate, Integer dayAfter) {
        if (startDate == null || dayAfter == null || dayAfter < 0) {
            throw new IllegalArgumentException("startDate and dayAfter must be non-null and dayAfter must be non-negative.");
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        while (dayAfter > 0) {
            calendar.add(Calendar.DAY_OF_YEAR, 1);
            if (calendar.get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY && calendar.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
                dayAfter--;
            }
        }
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 58);
        return calendar.getTime();
    }
}
