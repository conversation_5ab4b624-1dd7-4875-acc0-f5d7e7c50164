package com.yixun.hdm.controller.admin;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.common.ErrorMessage;
import com.yixun.hdm.bean.in.HiddenDangerListGetIn;
import com.yixun.hdm.bean.in.RegionStatisticIn;
import com.yixun.hdm.bean.in.StatisticIn;
import com.yixun.hdm.bean.out.*;
import com.yixun.hdm.bean.out.statistic.HdCorporationOut;
import com.yixun.hdm.bean.out.statistic.HdCountOut;
import com.yixun.hdm.bean.out.statistic.HdStatusOut;
import com.yixun.hdm.bean.out.statistic.HdTypeOut;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.LabelValue;
import com.yixun.hdm.exception.ParameterErrorException;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.service.StatisticService;
import com.yixun.teacher.api.TaskApi;
import com.yixun.teacher.bean.enums.TaskTypeEnum;
import com.yixun.teacher.bean.in.ApiTaskGetIn;
import com.yixun.teacher.bean.out.TaskOut;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "admin统计情况")
@RestController
@RequestMapping("/admin/statistic")
public class AdminStatisticController {

	@Resource
	private HiddenDangerService hiddenDangerService;

	@Resource
	private StatisticService statisticService;

	@Resource(name = "customRedisTemplate")
	private RedisTemplate redisTemplate;

	@Autowired
	private TaskApi taskApi;

	@RequiredPermission("get:getHdStatistic:statistic")
	@ApiOperation(value = "获取新版隐患统计数据")
	@GetMapping(value = "/getHdStatistic")
	public CommonResult<HdStatisticOut> getNewHdStatistic(HiddenDangerListGetIn hiddenDangerListGetIn){

		HdStatisticOut statisticOut;
		if (hiddenDangerListGetIn.getProjectId()==null){
			String redisKey = "HdDailyStatistic";
			statisticOut = (HdStatisticOut) redisTemplate.opsForValue().get(redisKey);
			if (statisticOut == null) {
				statisticOut = hiddenDangerService.getNewHdStatistic(hiddenDangerListGetIn);
				redisTemplate.opsForValue().set(redisKey, statisticOut, 24, TimeUnit.HOURS);
			}
		}else {
			statisticOut = hiddenDangerService.getNewHdStatistic(hiddenDangerListGetIn);
		}

		return CommonResult.successData(statisticOut);
	}

	@RequiredPermission("get:getHdmMonthlyCount:statistic")
    @ApiOperation(value = "获取当前每个月的隐患数量")
	@GetMapping(value = "/getMonthlyCount")
	public CommonResult<List<HdCountOut>> getMonthlyCount(Long projectId){

		Calendar c = Calendar.getInstance();
		c.add(Calendar.MONTH, -12);
		c.set(Calendar.DAY_OF_MONTH, 1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);

		List<HdCountOut> totalList = statisticService.getMonthlyTotalCount(projectId, c.getTime());
		List<HdCountOut> outList = totalList.stream().sorted(Comparator.comparing(HdCountOut::getMonth)).collect(Collectors.toList());

		List<HdCountOut> doneList = statisticService.getMonthlyDoneCount(projectId, c.getTime());
		outList.forEach(o->{
			Optional<HdCountOut> first = doneList.stream().filter(d -> d.getMonth().equals(o.getMonth())).findFirst();
			if (first.isPresent()){
				o.setDoneCount(first.get().getDoneCount());
				o.setUndoneCount(o.getTotalCount()-first.get().getDoneCount());
			}else {
				o.setDoneCount(0);
				o.setUndoneCount(o.getTotalCount());
			}
		});

		return CommonResult.successData(outList);
	}

	@RequiredPermission("get:getHdStatusCount:statistic")
	@ApiOperation(value = "获取隐患状态占比情况")
	@GetMapping(value = "/getHdStatusCount")
	public CommonResult<List<HdStatusOut>> getHdStatusCount(Long projectId){

		List<HdStatusOut> outList = statisticService.getHdStatusByProject(projectId, null, null);
		int wrongCount = statisticService.getHdWrongByProject(projectId, null, null);
		HdStatusOut wrongOut = new HdStatusOut();
		wrongOut.setStatus("错误上报");
		wrongOut.setCount(wrongCount);
		outList.add(wrongOut);

		return CommonResult.successData(outList);
	}

	@RequiredPermission("get:getHdTypeCount:statistic")
	@ApiOperation(value = "获取隐患类别排名")
	@GetMapping(value = "/getHdTypeCount")
	public CommonResult<List<HdTypeOut>> getHdTypeCount(Long projectId){

		List<HdTypeOut> outList = statisticService.getHdTierOneTypeByProject(projectId, null, null);
		outList = outList.stream().sorted(Comparator.comparing(HdTypeOut::getCount).reversed()).collect(Collectors.toList());

		return CommonResult.successData(outList);
	}

	@RequiredPermission("get:getHdLedgerData:statistic")
	@ApiOperation(value = "获取隐患台账数据")
	@GetMapping(value = "/getHdLedgerData")
	public CommonResult<HdLedgerDataOut> getHdLedgerData(Long projectId){

		int hdTotalCount = statisticService.getHdCountByProject(projectId, null, null);
		int hdMajorCount = statisticService.getHdLevelCountByProject(projectId, "重大隐患");

		List<HdTypeOut> hdTypeList = statisticService.getHdTypeByProject(projectId, null, null);
		List<HdCorporationOut> hdCorporationList = statisticService.getHdCorporationByProject(projectId);
		double averageHdCount = hdCorporationList.stream().mapToInt(HdCorporationOut::getCount).average().orElse(0);
		long aboveCorpCount = hdCorporationList.stream().filter(c -> c.getCount() >= averageHdCount).count();
		long belowCorpCount = hdCorporationList.stream().filter(c -> c.getCount() < averageHdCount).count();

		int hdCorpCount = hdCorporationList.size();
		int yqycCorpCount = statisticService.getCorpCountByProject(projectId);
		int errorCorpCount = yqycCorpCount - hdCorpCount;

		HdLedgerDataOut out= new HdLedgerDataOut();
		out.setHdTotalCount(hdTotalCount);
		out.setHdMajorCount(hdMajorCount);
		out.setHdTypeList(hdTypeList);
		out.setAverageHdCount(averageHdCount);
		out.setAboveCorpCount(aboveCorpCount);
		out.setBelowCorpCount(belowCorpCount);
		out.setErrorCorpCount(errorCorpCount);

		return CommonResult.successData(out);
	}

	@RequiredPermission("get:getHdCorpInfoList:statistic")
	@ApiOperation(value = "获取隐患按公司统计数据")
	@GetMapping(value = "/getHdCorpInfoList")
	public CommonResult<List<HdCorpInfoOut>> getHdCorpInfoList(StatisticIn statisticIn, CommonPage page){

		if (statisticIn.getProjectId()==null){
			throw new ParameterErrorException(ErrorMessage.parameter_error);
		}

		ApiTaskGetIn apiTaskGetIn = new ApiTaskGetIn();
		apiTaskGetIn.setProjectId(statisticIn.getProjectId());
		apiTaskGetIn.setTypes(Arrays.asList(TaskTypeEnum.Survey, TaskTypeEnum.Callback));
		List<TaskOut> taskList = taskApi.getTaskList(apiTaskGetIn);
		List<TaskOut> filteredList = new ArrayList<>();
		if (statisticIn.getStartDate()!=null && statisticIn.getEndDate()!=null){
			for (TaskOut taskDto : taskList){
				if (taskDto.getType()==TaskTypeEnum.Survey && taskDto.getStartDate().compareTo(statisticIn.getStartDate()) >= 0
						&& taskDto.getStartDate().compareTo(statisticIn.getEndDate()) <= 0) {
					filteredList.add(taskDto);
				}
			}
		}else {
			filteredList.addAll(taskList);
		}

		List<Long> corpIds = filteredList.stream().map(TaskOut::getCorporationId).distinct().collect(Collectors.toList());
		if (corpIds.isEmpty()){
			return CommonResult.successData(new ArrayList<>());
		}

		List<HdCorpInfoOut> records;
		Page<HdCorpInfoOut> hdCorpInfoOutPage;
		if (page.getPageSize()>1000){
			records = statisticService.getHdCorpInfoList(statisticIn.getProjectId(), corpIds);
			hdCorpInfoOutPage = new Page<>();
			hdCorpInfoOutPage.setRecords(records);
		}else {
			hdCorpInfoOutPage = statisticService.getHdCorpInfoPage(statisticIn.getProjectId(), corpIds, page);
			records = hdCorpInfoOutPage.getRecords();
		}

		records.forEach(hd->{
			if (hd.getHdCount()==0){
				hd.setDoneRate(0f);
			}else {
				hd.setDoneRate(Math.round(hd.getDoneCount() * 100f/ hd.getHdCount()));
			}

			if (hd.getDiaoyanCount()==0){
				hd.setDiaoyanDoneRate(0f);
			}else {
				hd.setDiaoyanDoneRate(Math.round(hd.getDiaoyanDoneCount() * 100f/ hd.getDiaoyanCount()));
			}

			if (hd.getHuifangCount()==0){
				hd.setHuifangDoneRate(0f);
			}else {
				hd.setHuifangDoneRate(Math.round(hd.getHuifangDoneCount() * 100f/ hd.getHuifangCount()));
			}

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
			List<String> surveyDateList = filteredList.stream().filter(t -> t.getType()==TaskTypeEnum.Survey
						&& t.getCorporationId().equals(hd.getCorporationId()))
					.map(t->sdf.format(t.getStartDate())).collect(Collectors.toList());
			List<String> callbackDateList = filteredList.stream().filter(t -> t.getType()==TaskTypeEnum.Callback
						&& t.getCorporationId().equals(hd.getCorporationId()))
					.map(t->sdf.format(t.getStartDate())).collect(Collectors.toList());
			hd.setSurveyDate(String.join(",", surveyDateList));
			hd.setCallbackDate(String.join(",", callbackDateList));
			Set<String> surveyExecutor = filteredList.stream().filter(t -> t.getType()==TaskTypeEnum.Survey
					&& t.getCorporationId().equals(hd.getCorporationId())).map(TaskOut::getExecutor).collect(Collectors.toSet());
			hd.setSurveyExecutor(String.join(",", surveyExecutor));
			Set<String> callbackExecutor = filteredList.stream().filter(t -> t.getType()==TaskTypeEnum.Callback
					&& t.getCorporationId().equals(hd.getCorporationId())).map(TaskOut::getExecutor).collect(Collectors.toSet());
			hd.setCallbackExecutor(String.join(",", callbackExecutor));

		});

		return CommonResult.successData(hdCorpInfoOutPage);
	}

	@RequiredPermission("get:getHdRiskFactorCount:statistic")
	@ApiOperation(value = "获取隐患危害因素分类")
	@GetMapping(value = "/getHdRiskFactorCount")
	public CommonResult<List<HdTypeOut>> getHdRiskFactorCount(Long projectId){

		List<HdTypeOut> outList = statisticService.getHdRiskFactorByProject(projectId);
		outList = outList.stream().sorted(Comparator.comparing(HdTypeOut::getCount).reversed()).collect(Collectors.toList());

		return CommonResult.successData(outList);
	}

	@RequiredPermission("get:getProjectCooperateHd:statistic")
	@ApiOperation(value = "获取项目协同统计")
	@GetMapping(value = "/getProjectCooperateStatistic")
	public CommonResult<ProjectCooperateStatisticOut> getProjectCooperateStatistic(StatisticIn statisticIn){

		if (statisticIn.getProjectId()==null){
			throw new ParameterErrorException(ErrorMessage.parameter_error);
		}

		ApiTaskGetIn apiTaskGetIn = new ApiTaskGetIn();
		apiTaskGetIn.setProjectId(statisticIn.getProjectId());
		apiTaskGetIn.setTypes(Arrays.asList(TaskTypeEnum.Survey, TaskTypeEnum.Callback));
		List<TaskOut> taskList = taskApi.getTaskList(apiTaskGetIn);
		List<TaskOut> filteredList = new ArrayList<>();
		if (statisticIn.getStartDate()!=null && statisticIn.getEndDate()!=null){
			for (TaskOut taskDto : taskList){
				if (taskDto.getType()==TaskTypeEnum.Survey && taskDto.getStartDate().compareTo(statisticIn.getStartDate()) >= 0
						&& taskDto.getStartDate().compareTo(statisticIn.getEndDate()) <= 0) {
					filteredList.add(taskDto);
				}
			}
		}else {
			filteredList.addAll(taskList);
		}

		List<Long> corpIds = filteredList.stream().map(TaskOut::getCorporationId).distinct().collect(Collectors.toList());
		if (corpIds.isEmpty()){
			ProjectCooperateStatisticOut out = new ProjectCooperateStatisticOut();
			out.setCorpCount(0L);
			out.setHdCount(0);
			out.setCallbackCount(0L);
			out.setLevelMap(new HashMap());
			out.setTypeMap(new HashMap());
			out.setStatusMap(new HashMap());
			out.setRiskFactorTypeList(new ArrayList<>());
			return CommonResult.successData(out);
		}

		//隐患概况
		List<HiddenDanger> hiddenDangerList = hiddenDangerService.getProjectCooperateStatistic(statisticIn.getProjectId(),
				corpIds, null);
		int hdCount = hiddenDangerList.size();
		long callbackCount = hiddenDangerList.stream().filter(item -> item .getTaskType() != null && item.getTaskType() == 3).count();
		long corpCount = hiddenDangerList.stream().map(HiddenDanger::getCorporationId).distinct().count();
		//隐患分级
		Map<String, Long> levelMap = hiddenDangerList.stream().filter(h->!h.getLevel().isEmpty())
				.collect(Collectors.groupingBy(HiddenDanger::getLevel, Collectors.counting()));

		//企业调研日期，调研、回访情况过滤
		List<HiddenDanger> hiddenDangerList2 = hiddenDangerService.getProjectCooperateStatistic(statisticIn.getProjectId(),
				corpIds, statisticIn.getIsFindByCallback());
		//隐患门类
		Map<String, Long> typeMap = hiddenDangerList2.stream().filter(h->!h.getType().isEmpty())
				.collect(Collectors.groupingBy(HiddenDanger::getType, Collectors.counting()));
		//隐患状态
		Map<String, Long> statusMap = hiddenDangerList2.stream()
				.collect(Collectors.groupingBy(HiddenDanger::getStatus, Collectors.counting()));

		//危害因素分类
		Map<String, List<HiddenDanger>> riskFactorTypeListMap = hiddenDangerList2.stream()
				.filter(h -> StringUtils.hasLength(h.getRiskFactorType()))
				.collect(Collectors.groupingBy(HiddenDanger::getRiskFactorType));
		List<ProjectCooperateStatisticOut.RiskFactorType> riskFactorTypeList = new ArrayList<>();
		for(Map.Entry<String, List<HiddenDanger>> entry : riskFactorTypeListMap.entrySet()){
			ProjectCooperateStatisticOut.RiskFactorType riskFactorType = new ProjectCooperateStatisticOut.RiskFactorType();
			riskFactorType.setType(entry.getKey());
			List<HiddenDanger> hiddenDangers = entry.getValue();
			riskFactorType.setCount(hiddenDangers.size());
			Map<String, Long> riskFactorTypeMap = hiddenDangers.stream()
					.filter(h -> h.getRiskFactorTypeDetail() != null && JSON.parseArray(h.getRiskFactorTypeDetail(), LabelValue.class).size()>1)
					.map(h -> JSON.parseArray(h.getRiskFactorTypeDetail(), LabelValue.class).get(1))
					.collect(Collectors.groupingBy(LabelValue::getLabel, Collectors.counting()));
			riskFactorType.setRiskFactorTypeMap(riskFactorTypeMap);
			riskFactorTypeList.add(riskFactorType);
		}

		ProjectCooperateStatisticOut out = new ProjectCooperateStatisticOut();
		out.setCorpCount(corpCount);
		out.setHdCount(hdCount);
		out.setCallbackCount(callbackCount);
		out.setLevelMap(levelMap);
		out.setTypeMap(typeMap);
		out.setStatusMap(statusMap);
		out.setRiskFactorTypeList(riskFactorTypeList);

		return CommonResult.successData(out);
	}

	@RequiredPermission("get:getProjectCorpHdStatistic:statistic")
	@ApiOperation(value = "获取项目协同公司隐患数量统计")
	@GetMapping(value = "/getProjectCorpHdStatistic")
	public CommonResult<List<ProjectCorpHdStatisticOut>> getProjectCorpHdStatistic(StatisticIn statisticIn, CommonPage page){

		if (statisticIn.getProjectId()==null){
			throw new ParameterErrorException(ErrorMessage.parameter_error);
		}

		ApiTaskGetIn apiTaskGetIn = new ApiTaskGetIn();
		apiTaskGetIn.setProjectId(statisticIn.getProjectId());
		apiTaskGetIn.setTypes(Arrays.asList(TaskTypeEnum.Survey, TaskTypeEnum.Callback));
		List<TaskOut> taskOutList = taskApi.getTaskList(apiTaskGetIn);
        List<TaskOut> filteredList = new ArrayList<>();
        if (statisticIn.getStartDate()!=null && statisticIn.getEndDate()!=null){
            for (TaskOut taskDto : taskOutList){
                if (taskDto.getType()==TaskTypeEnum.Survey && taskDto.getStartDate().compareTo(statisticIn.getStartDate()) >= 0
                        && taskDto.getStartDate().compareTo(statisticIn.getEndDate()) <= 0) {
                    filteredList.add(taskDto);
                }
            }
        }else {
            filteredList.addAll(taskOutList);
        }

		List<Long> corpIds = filteredList.stream().map(TaskOut::getCorporationId).distinct().collect(Collectors.toList());
		if (corpIds.isEmpty()){
			return CommonResult.successData(new ArrayList<>());
		}

		Page<ProjectCorpHdStatisticOut> outPage = statisticService.getProjectCorpHdStatistic(corpIds, statisticIn.getProjectId(), page);
		outPage.getRecords().forEach(pch->{
			List<TaskOut> taskList = filteredList.stream().filter(f -> f.getCorporationId().equals(pch.getCorporationId()))
					.collect(Collectors.toList());
			if (!taskList.isEmpty()){
				for (TaskOut taskDto : taskList){
					pch.setCorpName(taskDto.getCorpName());
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
					String surveyDate = sdf.format(taskDto.getStartDate());
					pch.setSurveyDate(surveyDate);
				}
			}
		});

		return CommonResult.successData(outPage);
	}

}
