package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UserOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    private Boolean isDisable;
    private String phone;
    private String realName;
    private String idCard;
    private String realNamePic;
    private String email;
    private String avatar;
    private String type;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long organizationId;
    private String organization;
    private String jobTitle;
    private String position;
    private String remark;
    private Boolean isOperator;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date lastLoginTime;
    private String lastLoginIp;
    private List<String> projectIdList;
}
