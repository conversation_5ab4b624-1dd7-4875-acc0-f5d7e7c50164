package com.yixun.hdm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hd.bean.CropHdNumIn;
import com.yixun.hd.bean.CropHdNumOut;
import com.yixun.hdm.bean.in.CorpSafeListIn;
import com.yixun.hdm.bean.in.HiddenDangerListGetIn;
import com.yixun.hdm.bean.out.CorpSafeListOut;
import com.yixun.hdm.bean.out.RecommendPossibleConsequenceName;
import com.yixun.hdm.bean.out.RecommendSpecialCategoryOut;
import com.yixun.hdm.entity.HiddenDanger;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Entity com.yixun.hdm.entity.HiddenDanger
 */
public interface HiddenDangerMapper extends BaseMapper<HiddenDanger> {

    Set<String> frequentlyUseStandard(@Param("userId") Long userId, @Param("from") String from);

    List<HiddenDanger> getHiddenDangerPage(HiddenDangerListGetIn hiddenDangerListGetIn);

    List<RecommendSpecialCategoryOut> recommendSpecialCategory(Set<Long> checkContentIdSet);

    List<RecommendSpecialCategoryOut> recommendRiskFactorType(Set<Long> checkContentIdSet);

    List<RecommendPossibleConsequenceName> top5PossibleConsequenceName(Set<Long> checkContentIdSet);

    List<CropHdNumOut> getCropHdNum(Long projectId, List<Long> corpIdList);

    List<CropHdNumOut> getLatestTurnCropHdNum(CropHdNumIn cropHdNumIn);

    List<CorpSafeListOut> getCropSafeStatistic(CorpSafeListIn in);

}




