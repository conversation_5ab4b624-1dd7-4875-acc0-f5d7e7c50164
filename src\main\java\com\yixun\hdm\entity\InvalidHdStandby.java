package com.yixun.hdm.entity;



import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * (InvalidHdStandby)表实体类
 *
 * <AUTHOR>
 * @since 2024-12-26 16:06:28
 */
@SuppressWarnings("serial")
@Data
@ApiModel("")
@TableName("invalid_hd_standby")
public class InvalidHdStandby extends Model<InvalidHdStandby> {

    @ApiModelProperty("主键ID")
    @NotNull(message="[主键ID]不能为空")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("内容")
    private String content;

    private String contentMd5;

    @ApiModelProperty("更新时间")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

    @ApiModelProperty("创建时间")
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

}

