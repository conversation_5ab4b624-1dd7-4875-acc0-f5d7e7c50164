package com.yixun.hdm.service;

import com.yixun.hdm.bean.out.SelfCheckCorpImportProgressOut;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.multipart.MultipartFile;

/**
 * 自查企业导入服务接口
 *
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface SelfCheckCorpImportService {

    /**
     * 开始异步导入企业数据
     *
     * @param file Excel文件
     * @param projectId 项目ID
     * @return 任务ID
     */
    String startImport(MultipartFile file, Long projectId);

    /**
     * 获取导入进度
     *
     * @param taskId 任务ID
     * @return 导入进度
     */
    SelfCheckCorpImportProgressOut getImportProgress(String taskId);

    /**
     * 异步处理导入
     *
     * @param file Excel文件
     * @param projectId 项目ID
     * @param taskId 任务ID
     */
    void processImportAsync(MultipartFile file, Long projectId, String taskId);

}
