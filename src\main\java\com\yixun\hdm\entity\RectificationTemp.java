package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 改善信息表
 * @TableName hdm_rectification
 */
@TableName(value ="hdm_rectification_temp")
@Data
public class RectificationTemp implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     *
     */
    private Date createTime;

    /**
     * 隐患id
     */
    private Long hiddenDangerId;

    /**
     * 改善描述
     */
    private String description;

    /**
     * 改善负责人
     */
    private String inChargeName;

    /**
     * 改善照片（可多个）
     */
    private String pictures;

    /**
     * 是否正式提交
     */
    private Boolean isSubmit;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}