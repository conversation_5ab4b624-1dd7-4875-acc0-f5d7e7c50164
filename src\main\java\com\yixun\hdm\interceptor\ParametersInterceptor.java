package com.yixun.hdm.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Map.Entry;

/**
 *  请求参数Interceptor
 */
@SuppressWarnings("all")
public class ParametersInterceptor extends HandlerInterceptorAdapter {

	private static final Logger log = LoggerFactory.getLogger(ParametersInterceptor.class);

	/** 忽略参数 */
	private String[] ignoreParameters = new String[] { "password",
			"rePassword", "currentPassword" };

	@Override
	public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {
		log(request, response);
		return true;
	}

	public void log(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String path = request.getServletPath();
		StringBuffer url = new StringBuffer();
		url.append("请求URL： " + path);
		StringBuffer parameter = new StringBuffer();
		Map<String, String[]> parameterMap = request.getParameterMap();
		if (parameterMap != null) {
			for (Entry<String, String[]> entry : parameterMap.entrySet()) {
				String parameterName = entry.getKey();
				// if (!ArrayUtils.contains(ignoreParameters, parameterName)) {
				String[] parameterValues = entry.getValue();
				if (parameterValues != null) {
					for (String parameterValue : parameterValues) {
						parameter.append(parameterName + " = " + parameterValue
								+ " & ");
					}
				}
				// }
			}
		}
		url.append("?" + parameter.toString());
		url.append("[" + getRealIP(request) + ","+request.getHeader("User-Agent")+"]");
		log.info(url.toString());
	}

	public String getRealIP(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}

}