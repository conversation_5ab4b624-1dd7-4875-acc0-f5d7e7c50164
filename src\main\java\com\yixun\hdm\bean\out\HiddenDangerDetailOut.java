package com.yixun.hdm.bean.out;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.entity.HdAuditLog;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.HiddenDangerSuggestion;
import com.yixun.hdm.utils.DateJsonSerializer;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class HiddenDangerDetailOut {

    @ApiModelProperty("隐患信息")
    private HiddenDanger hiddenDanger;

    @ApiModelProperty("隐患标注信息")
    private HdAuditLog hdAuditLog;

    private List<HdManageItemsOut> hdManageItems;
}
