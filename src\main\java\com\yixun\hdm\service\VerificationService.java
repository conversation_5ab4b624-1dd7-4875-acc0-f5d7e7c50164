package com.yixun.hdm.service;

import com.yixun.hdm.bean.in.VerificationIn;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.OperationLogs;
import com.yixun.hdm.entity.Verification;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface VerificationService extends IService<Verification> {

    void submitVerification(HiddenDanger hiddenDanger, Verification verification, OperationLogs operationLogs);

    List<Verification> getByHiddenDanger(Long hiddenDangerId);

    Verification getLastVerification(Long hiddenDangerId);

    List<Verification> getByHiddenDangerIds(String ids);

    Verification getVerification(Long hiddenDangerId);

    void updateVerification(VerificationIn hiddenDangerIn);
}
