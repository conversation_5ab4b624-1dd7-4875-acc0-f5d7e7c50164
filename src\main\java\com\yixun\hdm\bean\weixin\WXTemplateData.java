package com.yixun.hdm.bean.weixin;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class WXTemplateData {
	
	@ApiModelProperty(value="用户身份证")
	private String idCardNo;
	
	@ApiModelProperty(value="数据",required = true)
	private List<NotificationItem> data;
	
	@ApiModelProperty(value="模板id",required = true)
	private String templateId;

	@ApiModelProperty("点击以后重定向地址")
	private String url;

	public String getIdCardNo() {
		return idCardNo;
	}

	public void setIdCardNo(String idCardNo) {
		this.idCardNo = idCardNo;
	}

	public List<NotificationItem> getData() {
		return data;
	}

	public void setData(List<NotificationItem> data) {
		this.data = data;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
}
