package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 清除数据白名单
 * @TableName hdm_clean_data_white_list
 */
@TableName(value ="hdm_clean_data_white_list")
@Data
public class CleanDataWhiteList implements Serializable {
    /**
     * userId
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Date createTime;

    /**
     * 名字
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 公司名
     */
    private String corpName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}