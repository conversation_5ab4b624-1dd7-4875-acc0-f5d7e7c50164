package com.yixun.hdm.aop;

import com.alibaba.fastjson.JSON;
import com.yixun.hdm.service.AsyncService;
import com.yixun.hdm.utils.AdminUserHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
public class PostInJsonParametersAop {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private AsyncService asyncService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate redisTemplate;

    @Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping)")
    public void postInJsonParameters(){}

    @Around("postInJsonParameters()")
    public Object postExec(ProceedingJoinPoint invocation) throws Throwable {
        return getPostInJsonParameters(invocation);
    }

    private Object getPostInJsonParameters(ProceedingJoinPoint invocation) throws Throwable {
        try {
	        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
	        String servletPath = request.getServletPath();

	        if (servletPath.contains("self/checkCorp/import")) {
		        return invocation.proceed();
	        }


	        String args = JSON.toJSONString(invocation.getArgs());
	        logger.info("参数：" + args);
	        //记录所有的修改日志到数据库

            if (servletPath.startsWith("/admin/") && !servletPath.contains("get")){
                Long userId = AdminUserHelper.getCurrentUserId();
                asyncService.recordLogAdmin(invocation, servletPath, userId);
                //访问频率过滤器
                String actionKey = "frequencyCheck:" + servletPath + ":" + args;
                Boolean frequencyCheck = redisTemplate.opsForValue().setIfAbsent(actionKey, "frequencyCheck", 3, TimeUnit.SECONDS);
                if (frequencyCheck==null || !frequencyCheck) {
                    return null;
                }
            }
        }catch (Exception e){
            logger.error("参数log报错出错: " + e.getMessage());
        }

        return invocation.proceed();
    }

}
