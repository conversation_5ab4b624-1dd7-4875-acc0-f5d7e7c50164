package com.yixun.hdm.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.other.LoginToken;
import com.yixun.hdm.utils.RedisKeyResolver;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

@Component
public class AdminInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {
        boolean isLogin = true;
        LoginToken lt = null;
        String token = "";
        String tokenValue = request.getHeader("Authorization");
        if (null == tokenValue)
            isLogin = false;

        if (isLogin) {
            lt = JSON.parseObject(tokenValue, new TypeReference<LoginToken>() {});
            token = null == lt ? null : stringRedisTemplate.opsForValue().get(
                    RedisKeyResolver.getAdminUserToken(lt.getToken()));
            isLogin = null != token && (token.contains(lt.getRandom())
                    && token.contains(lt.getMobileCode()));
        }

        if (isLogin) {
            //redis更新login token
            stringRedisTemplate.expire(RedisKeyResolver.getAdminUserToken(lt.getToken()),
                    12, TimeUnit.HOURS);
            return true;
        } else {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter()
                    .write(JSON.toJSONString(CommonResult.failResult(401, "未登录或登录已过期")));
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request,
                           HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {
    }

}