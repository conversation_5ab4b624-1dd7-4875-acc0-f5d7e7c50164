package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class WithdrawApplyOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long userId;
    private String name;
    private String phone;
    private String projectName;
    private String corpName;
    private Integer amount;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long auditorId;
    private String auditor;
    private Integer studyTime;
    private Integer status;
    private Boolean isArrival;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date successTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date refuseTime;
    private String reason;
}
