package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CheckResultStatisticsOut {

    @ApiModelProperty("任务id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long taskId;

    @ApiModelProperty("项目id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("公司id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;

    @ApiModelProperty("公司名称")
    private String corpName;

    @ApiModelProperty("执行负责人")
    private String executor;

    @ApiModelProperty("陪同人员")
    private String accompany;

    @ApiModelProperty("检查开始时间")
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date startTime;

    @ApiModelProperty("检查结束时间")
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date endTime;

    @ApiModelProperty("检查内容数")
    private Integer checkCount;

    @ApiModelProperty("检查合格数")
    private Integer passCount;

    @ApiModelProperty("查出隐患数")
    private Integer hdCount;

    @ApiModelProperty("不涉及数")
    private Integer uninvolvedCount;

    @ApiModelProperty("重复数")
    private Integer repeatCount;

    @ApiModelProperty("未检查数")
    private Integer unCheckCount;
}
