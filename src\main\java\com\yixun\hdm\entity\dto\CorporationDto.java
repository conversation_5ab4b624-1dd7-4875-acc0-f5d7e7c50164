package com.yixun.hdm.entity.dto;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.bean.common.CodeNameInfo;
import com.yixun.hdm.utils.DateJsonSerializer;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CorporationDto {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createDate;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date updateDate;
    private Integer pmId;
    private String corpName;
    @ApiModelProperty("实体类型 1-企业 2-项目场所")
    private Integer type;
    @ApiModelProperty("企业分级 1-重点 2-次重点 3-一般")
    private Integer classify;
    private String corpBrief;
    private String creditCode;
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date estiblishTime;
    private String regCapital;
    private String website;
    private String keyIndustry;
    private List<CodeNameInfo> economicIndustry;
    private List region;
    private String address;
    private String legalPerson;
    private String contactPerson;
    private String contactNum;
    private Double longitude;
    private Double latitude;

    public void setRegion(String region) {
        this.region = JSON.parseObject(region, List.class);
    }

    public void setEconomicIndustry(String economicIndustry) {
        this.economicIndustry = JSON.parseArray(economicIndustry, CodeNameInfo.class);
    }

}
