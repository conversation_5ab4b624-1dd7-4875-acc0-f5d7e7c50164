<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yixun.hdm.dao.HiddenDangerSuggestionMapper">

    <resultMap type="com.yixun.hdm.entity.HiddenDangerSuggestion" id="HiddenDangerSuggestionMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="hdId" column="hd_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="improvementMeasures" column="improvement_measures" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="trackingTime" column="tracking_time" jdbcType="TIMESTAMP"/>
        <result property="corpResp" column="corp_resp" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hidden_danger.hdm_hidden_danger_suggestion(hd_idtypeimprovement_measuresstatustracking_timecorp_respcreate_timeupdate_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.hdId}#{entity.type}#{entity.improvementMeasures}#{entity.status}#{entity.trackingTime}#{entity.corpResp}#{entity.createTime}#{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hidden_danger.hdm_hidden_danger_suggestion(hd_idtypeimprovement_measuresstatustracking_timecorp_respcreate_timeupdate_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.hdId}#{entity.type}#{entity.improvementMeasures}#{entity.status}#{entity.trackingTime}#{entity.corpResp}#{entity.createTime}#{entity.updateTime})
        </foreach>
        on duplicate key update
hd_id = values(hd_id) type = values(type) improvement_measures = values(improvement_measures) status = values(status) tracking_time = values(tracking_time) corp_resp = values(corp_resp) create_time = values(create_time) update_time = values(update_time)     </insert>

</mapper>

