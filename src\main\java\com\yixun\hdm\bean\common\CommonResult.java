package com.yixun.hdm.bean.common;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.List;

@Data
public class CommonResult<T> {

	private Integer code;
	private String msg;
	private CommonPage page;
	private T data;

	/**
	 * 返回一个默认成功的消息
	 * @return
	 */
	public static <T> CommonResult<T> successResult(String msg){
		CommonResult<T> result =  new CommonResult<>();
		result.setCode(200);
		if (StringUtils.isEmpty(msg)){
			result.setMsg("success");
		}else {
			result.setMsg(msg);
		}
		return result;
	}

	/**
	 * 返回一个带数据的成功消息
	 */
	public static <T> CommonResult<T> successData(T data) {
		CommonResult<T> result = new CommonResult<>();
		result.setCode(200);
		result.setMsg("success");
		result.setData(data);
		return result;
	}

	/**
	 * 返回一个带数据的成功消息，带翻页信息
	 */
	public static <T> CommonResult<List<T>> successData(IPage<T> data) {
		CommonResult<List<T>> result = new CommonResult<>();
		result.setCode(200);
		result.setMsg("success");

		Page<?> pageData = (Page<?>) data;
		CommonPage commonPage = new CommonPage();
		commonPage.setPageNum(pageData.getCurrent());
		commonPage.setPageSize(pageData.getSize());
		commonPage.setPages(pageData.getPages());
		commonPage.setTotal(pageData.getTotal());
		result.setPage(commonPage);
		result.setData((List<T>) pageData.getRecords());

		return result;
	}

	/**
	 * 返回一个默认失败的消息
	 * @return
	 */
	public static <T> CommonResult<T> failResult(Integer code, String desc){
		CommonResult<T> result =  new CommonResult<>();
		result.setCode(code);
		result.setMsg(desc);
		return result;
	}

	/**
	 * 根据pagehelper的分页结果返回分页信息
	 * @param pageInfo
	 * @param <T>
	 * @return
	 */
	public static <T> CommonResult<List<T>> successPageData(PageInfo<T> pageInfo) {
		CommonResult<List<T>> result = new CommonResult<>();
		result.setCode(200);
		result.setMsg("success");
		CommonPage page = new CommonPage();
		BeanUtil.copyProperties(pageInfo, page);
		result.setPage(page);
		result.setData(pageInfo.getList());

		return result;
	}

}
