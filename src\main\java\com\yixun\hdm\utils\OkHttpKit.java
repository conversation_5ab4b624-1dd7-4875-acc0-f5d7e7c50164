package com.yixun.hdm.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.FormBody.Builder;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class OkHttpKit {

    private static final Logger logger = LoggerFactory.getLogger(OkHttpKit.class);

    public static JSONObject connGet(String url, Map<String, String> params) throws IOException {

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();
        client = client.newBuilder()
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .connectTimeout(20, TimeUnit.SECONDS).build();

        if (params != null) {
            url += "?";
            int i = 1;
            int size = params.keySet().size();
            for (String key : params.keySet()) {
                url += key + "=" + params.get(key);
                if (i < size) {
                    url += "&";
                }
                i++;
            }
        }

        URL mURL = new URL(url);

        Request.Builder requestBuilder = new Request.Builder()
                .url(mURL)
                .get();

        Request request = requestBuilder.build();
        Response response = client.newCall(request).execute();

        return getJsonObject(response);
    }

    public static JSONObject apiGetWithToken(String url, Map<String, String> params, String token) throws IOException {

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();
        client = client.newBuilder()
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .connectTimeout(20, TimeUnit.SECONDS).build();

        if (params != null) {
            url += "?";
            int i = 1;
            int size = params.keySet().size();
            for (String key : params.keySet()) {
                url += key + "=" + params.get(key);
                if (i < size) {
                    url += "&";
                }
                i++;
            }
        }

        URL mURL = new URL(url);

        Request.Builder requestBuilder = new Request.Builder()
                .addHeader("ApiToken", token)
                .url(mURL)
                .get();

        Request request = requestBuilder.build();
        Response response = client.newCall(request).execute();

        return getJsonObject(response);
    }

    public static JSONObject apiPostWithToken(String url, String content, String token) throws IOException {

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();
        client = client.newBuilder()
                .readTimeout(300, TimeUnit.SECONDS)
                .writeTimeout(300, TimeUnit.SECONDS)
                .connectTimeout(300, TimeUnit.SECONDS).build();

        RequestBody requestBody = RequestBody.create(
                MediaType.parse("application/json"), content);

        Request.Builder requestBuilder = new Request.Builder()
                .addHeader("ApiToken", token)
                .url(url)
                .post(requestBody);

        Request request = requestBuilder.build();
        Response response = client.newCall(request).execute();

        return getJsonObject(response);
    }

    public static JSONArray apiPostWithTokenArray(String url, String content, String token) throws IOException {

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();
        client = client.newBuilder()
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .connectTimeout(20, TimeUnit.SECONDS).build();

        RequestBody requestBody = RequestBody.create(
                MediaType.parse("application/json"), content);

        Request.Builder requestBuilder = new Request.Builder()
                .addHeader("ApiToken", token)
                .url(url)
                .post(requestBody);

        Request request = requestBuilder.build();
        Response response = client.newCall(request).execute();

        return getJSONArray(response);
    }

    public static JSONObject connPostInJson(String url, String content) throws IOException {

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();
        client = client.newBuilder()
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .connectTimeout(20, TimeUnit.SECONDS).build();

        RequestBody requestBody = RequestBody.create(
                MediaType.parse("application/json"), content);

        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);

        Request request = requestBuilder.build();
        Response response = client.newCall(request).execute();

        return getJsonObject(response);
    }

    public static JSONObject connPost(String url, Map<String, Object> params) throws IOException {

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();
        client = client.newBuilder()
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .connectTimeout(20, TimeUnit.SECONDS).build();

        Builder mBuilder = new Builder();
        if (params != null) {
            for (String key : params.keySet()) {
                mBuilder.add(key, params.get(key) + "");
            }
        }
        RequestBody formBody = mBuilder.build();
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(formBody);

        Request request = requestBuilder.build();
        Response response = client.newCall(request).execute();

        return getJsonObject(response);
    }

    public static JSONObject connPostWithJwt(String url, String jwtToken, Map<String, Object> params) throws IOException {

        // 创建OkHttpClient对象
        OkHttpClient client = new OkHttpClient();
        client = client.newBuilder()
                .readTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .connectTimeout(20, TimeUnit.SECONDS).build();

        Builder mBuilder = new Builder();
        if (params != null) {
            for (String key : params.keySet()) {
                mBuilder.add(key, params.get(key) + "");
            }
        }
        RequestBody formBody = mBuilder.build();
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(formBody);

        requestBuilder.addHeader("Authorization", jwtToken);

        Request request = requestBuilder.build();
        Response response = client.newCall(request).execute();

        return getJsonObject(response);
    }

    private static JSONObject getJsonObject(Response response) throws IOException {
        if (response.isSuccessful()) {
            if (response.body()==null){
                response.close();
                throw new RuntimeException("response body is null");
            }else {
                String body = response.body().string();
                response.close();
                return JSON.parseObject(body);
            }
        } else {
            response.close();
            throw new RuntimeException("错误码：" + response);
        }
    }

    private static JSONArray getJSONArray(Response response) throws IOException {
        if (response.isSuccessful()) {
            if (response.body()==null){
                response.close();
                throw new RuntimeException("response body is null");
            }else {
                String body = response.body().string();
                response.close();
                return JSON.parseArray(body);
            }
        } else {
            response.close();
            throw new RuntimeException("错误码：" + response);
        }
    }

}
