package com.yixun.hdm.utils;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelStyleType;
import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.yixun.hdm.bean.other.CustomRowWriteHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExcelOutUtil {

    /**
     * @param request
     * @param response
     * @param path     resource下的路径
     * @param fileName 导出的文件名称
     */
    public static void downLoadTemp(HttpServletRequest request, HttpServletResponse response, String path,
                                    String fileName) {
        try {
            /// file/answerQuestion.ftl
            ClassPathResource classPathResource = new ClassPathResource(path);
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 避免下载文件名乱码
            // response.addHeader("Content-Disposition",
            // "attachment; filename=" + (new String(fileName.getBytes("UTF-8"), "ISO8859-1")));
            // 读取要下载的文件，保存到文件输入流
            InputStream in = classPathResource.getInputStream();
            // 创建输出流
            OutputStream out = response.getOutputStream();
            // 创建缓冲区
            byte buffer[] = new byte[1024];
            int len = 0;
            // 循环将输入流中的内容读取到缓冲区当中
            while ((len = in.read(buffer)) > 0) {
                // 输出缓冲区的内容到浏览器，实现文件下载
                out.write(buffer, 0, len);
            }
            // 关闭文件输入流
            in.close();
            // 关闭输出流
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //TODO 按照模板导出excel
     * @Param [response, path 模板路径, fileName 导出的文件名, map 输入的map数据无则传null, 输入的list数据无则传null]
     **/
    public static void exportByTemp(HttpServletResponse response, String path, String fileName, Object map,
                                    Object list) {
        OutputStream out = null;
        InputStream in = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource(path);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            in = classPathResource.getInputStream();
            out = response.getOutputStream();
            // 读取Excel
            ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).excelType(ExcelTypeEnum.XLSX).registerWriteHandler(new CustomRowWriteHandler()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // 直接写入Excel数据
            if (map != null) {
                excelWriter.fill(map, writeSheet);
            }
            if (list != null) {
                excelWriter.fill(list, writeSheet);
            }
            excelWriter.finish();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导出文件出错", e);
        }
    }

    /**
     * @param filePath 本地路径
     * @param path     模板路径
     * @param fileName 导出的文件名
     * @param map      输入的map数据无则传null,
     * @param list     输入的list数据无则传null
     */
    public static void exportToLocalByTemp(String filePath, String path, String fileName, Object map,
                                           Object list) {
        OutputStream out = null;
        InputStream in = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource(path);
            in = classPathResource.getInputStream();
            if (!FileUtil.exist(filePath)) {
                FileUtil.mkdir(filePath);
            }
            out = new FileOutputStream(new File(filePath + File.separator + fileName));
            // 读取Excel
            ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).excelType(ExcelTypeEnum.XLSX).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            // 直接写入Excel数据
            if (map != null) {
                excelWriter.fill(map, writeSheet);
            }
            if (list != null) {
                excelWriter.fill(list, writeSheet);
            }
            excelWriter.finish();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导出文件出错", e);
        }
    }

    /**
     * 按照模板导出excel (多个sheet)
     *
     * @param response
     * @param path
     * @param fileName
     * @param sheetNames
     * @param mapDatas
     * @param listDatas
     */
    public static void exportMultiSheetByTemp(HttpServletResponse response, String path, String fileName, List<String> sheetNames, List<Object> mapDatas,
                                              List<Object> listDatas) {
        OutputStream out = null;
        InputStream in = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource(path);
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            in = classPathResource.getInputStream();
            out = response.getOutputStream();
            // 读取Excel
            ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(in).excelType(ExcelTypeEnum.XLSX).build();
            for (int i = 0; i < sheetNames.size(); i++) {
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetNames.get(i)).build();
                // 直接写入Excel数据
                if (!CollectionUtils.isEmpty(mapDatas) && mapDatas.get(i) != null) {
                    excelWriter.fill(mapDatas.get(i), writeSheet);
                }
                if (!CollectionUtils.isEmpty(listDatas) && listDatas.get(i) != null) {
                    excelWriter.fill(listDatas.get(i), writeSheet);
                }
            }
            excelWriter.finish();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导出文件出错", e);
        }
    }

    /**
     * 利用
     *
     * @param param
     * @param response
     */
    public static void exportDataExcel(Map<String, Object> param, String path, String fileName, HttpServletResponse response) {
        try {
            TemplateExportParams params = new TemplateExportParams(path, true);
            //要使用横向遍历必须设置为true
            params.setColForEach(true);
            params.setStyle(ExcelStyleType.BORDER.getClazz());
            params.setSheetNum(new Integer[]{1, 2, 3, 4});
            Workbook book = ExcelExportUtil.exportExcel(params, param);
            // 下载方法
            export(response, book, fileName);
        } catch (Exception e) {
            throw new RuntimeException("导出异常");
        }
    }

    //下载方法
    public static void export(HttpServletResponse response, Workbook workbook, String fileName) throws Exception {
        try {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream output = response.getOutputStream();
            workbook.write(output);
            output.flush();
            workbook.close();
            output.close();
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }

    }

    /**
     * 下载img
     *
     * @param urlImg
     * @return
     * @throws Exception
     */
    public static byte[] getImg(String urlImg) {
        URL url = null;//获取人员照片的地址
        try {
            url = new URL(urlImg);
            //打开链接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置请求方式为"GET"
            conn.setRequestMethod("GET");
            //超时响应时间为5秒
            conn.setConnectTimeout(5 * 1000);
            //通过输入流获取图片数据
            InputStream inStream = conn.getInputStream();
            //得到图片的二进制数据，以二进制封装得到数据，具有通用性
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            //创建一个Buffer字符串
            byte[] buffer = new byte[1024];
            //每次读取的字符串长度，如果为-1，代表全部读取完毕
            int len = 0;
            //使用一个输入流从buffer里把数据读取出来
            while ((len = inStream.read(buffer)) != -1) {
                //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                outStream.write(buffer, 0, len);
            }
            //关闭输入流
            inStream.close();
            //把outStream里的数据写入内存
            return outStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}