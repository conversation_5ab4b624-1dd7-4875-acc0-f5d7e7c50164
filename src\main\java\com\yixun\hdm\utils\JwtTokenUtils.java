package com.yixun.hdm.utils;

import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component
public class JwtTokenUtils {

    public static String createToken(String secret, Map<String, Object> claims, Long expiration) {
        return Jwts.builder().setClaims(claims).setExpiration(generateExpirationDate(expiration))
                .signWith(SignatureAlgorithm.HS512, secret).compact();
    }

    public static String createToken(String payload, String secret, Date expirationData) {
        JwtBuilder jwtBuilder = Jwts.builder().setSubject(payload);
        if (expirationData != null) {
            jwtBuilder.setExpiration(expirationData);
        }

        jwtBuilder = jwtBuilder.signWith(SignatureAlgorithm.HS512, secret);

        return jwtBuilder.compact();
    }

    private static Date generateExpirationDate(Long expiration) {
        return new Date(System.currentTimeMillis() + expiration * 1000);
    }
}
