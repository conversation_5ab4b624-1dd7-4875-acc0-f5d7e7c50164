package com.yixun.hdm.controller.migration;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.em.AuditStatus;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.service.SecurityTankApiService;
import com.yixun.hdm.utils.HdQualityScoreUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "v2.1版本数据迁移")
@RestController
@RequestMapping(value = "/api/data/migration/v2p1")
public class V2p1DataMigrationController {

    private static final Logger log = LoggerFactory.getLogger(V2p1DataMigrationController.class);

    @Autowired
    private HiddenDangerService hiddenDangerService;

    @Autowired
    SecurityTankApiService securityTankApiService;

    @PostMapping("/fillRiskFactorTypeDetailAlias")
    @ApiOperation(value = "填充RiskFactorTypeDetailAlias")
    public CommonResult fillRiskFactorTypeDetailAlias(){
        log.info("开始填充RiskFactorTypeDetailAlias");
        for (int i = 1; i < 10000; i++) {
            LambdaQueryWrapper<HiddenDanger> wrapper = new LambdaQueryWrapper<>();
//            wrapper.eq(HiddenDanger::getAuditStatus, AuditStatus.UnFinished.name());
            wrapper.isNull(HiddenDanger::getRiskFactorTypeDetailAlias);
            wrapper.like(HiddenDanger::getRiskFactorTypeDetail, "[%");
            wrapper.orderByDesc(HiddenDanger::getCreateTime);
            Page<HiddenDanger> objectPage = new Page<>();
            objectPage.setSize(500);
            objectPage.setCurrent(i);
            IPage<HiddenDanger> page = hiddenDangerService.page(objectPage, wrapper);
            List<HiddenDanger> list = page.getRecords();
            if(list == null || list.isEmpty()){
                break;
            }
            JSONObject object = hiddenDangerService.convertRiskFactorTypeDetail(list);
            for (HiddenDanger hiddenDanger : list) {
                hiddenDanger.setRiskFactorTypeDetailAlias(object.getString(hiddenDanger.getId() + ""));
            }
            hiddenDangerService.updateBatchById(list);
        }
        log.info("填充RiskFactorTypeDetailAlias结束");
        return CommonResult.successResult(null);
    }

    @PostMapping("/fillHdQualityScore")
    @ApiOperation(value = "填充fillHdQualityScore")
    public CommonResult fillHdQualityScore(){
        log.info("开始填充隐患质量分");
        for (int i = 1; i < 10000; i++) {
            LambdaQueryWrapper<HiddenDanger> wrapper = new LambdaQueryWrapper<>();
//            wrapper.in(HiddenDanger::getAuditStatus, Arrays.asList(AuditStatus.Submitted.name(),AuditStatus.Audited.name(),AuditStatus.Auditing.name(), AuditStatus.Released.name()));
//            wrapper.eq(HiddenDanger::getHdQualityScore, 0);
            wrapper.orderByDesc(HiddenDanger::getCreateTime);
            Page<HiddenDanger> objectPage = new Page<>();
            objectPage.setSize(500);
            objectPage.setCurrent(i);
            IPage<HiddenDanger> page = hiddenDangerService.page(objectPage, wrapper);
            List<HiddenDanger> list = page.getRecords();
            if(list == null || list.isEmpty()){
                break;
            }
            for (HiddenDanger hiddenDanger : list) {
                Integer calculateScore = HdQualityScoreUtils.calculateScore(hiddenDanger);
                hiddenDanger.setHdQualityScore(calculateScore);
            }
            hiddenDangerService.updateBatchById(list);
        }
        log.info("填充隐患质量分结束");
        return CommonResult.successResult(null);
    }

    @PostMapping("/fillEquipmentAndJobActivity")
    @ApiOperation(value = "填充fillEquipmentAndJobActivity")
    public CommonResult fillEquipmentAndJobActivity(){
        log.info("开始填充设备设施和作业活动");
        JSONArray allRiskPoint = securityTankApiService.getAllRiskPoint(null);
        Map<String, String> riskPointCache = allRiskPoint.stream().collect(Collectors.toMap(item -> JSONObject.parseObject(item.toString()).getString("riskPointName"), item -> JSONObject.parseObject(item.toString()).getString("typeName")));
        for (int i = 1; i < 10000; i++) {
            LambdaQueryWrapper<HiddenDanger> wrapper = new LambdaQueryWrapper<>();
            wrapper.isNotNull(HiddenDanger::getRiskPointName);
            wrapper.isNull(HiddenDanger::getJobActivity);
            wrapper.isNull(HiddenDanger::getEquipment);
            wrapper.orderByDesc(HiddenDanger::getCreateTime);
            Page<HiddenDanger> objectPage = new Page<>();
            objectPage.setSize(500);
            objectPage.setCurrent(i);
            IPage<HiddenDanger> page = hiddenDangerService.page(objectPage, wrapper);
            List<HiddenDanger> list = page.getRecords();
            if(list == null || list.isEmpty()){
                break;
            }
            for (HiddenDanger hiddenDanger : list) {
                String typeName = riskPointCache.get(hiddenDanger.getRiskPointName());
                if(typeName != null){
                    if(typeName.equals("设备设施")){
                        hiddenDanger.setEquipment(typeName);
                    }else if(typeName.equals("作业活动")){
                        hiddenDanger.setJobActivity(typeName);
                    }
                }else{
                    if(hiddenDanger.getRiskPointName().contains("工艺") || hiddenDanger.getRiskPointName().contains("作业")){
                        hiddenDanger.setJobActivity(hiddenDanger.getRiskPointName());
                    }else{
                        hiddenDanger.setEquipment(hiddenDanger.getRiskPointName());
                    }
                }
            }
            hiddenDangerService.updateBatchById(list);
        }
        log.info("填充设备设施和作业活动结束");
        return CommonResult.successResult(null);
    }

}
