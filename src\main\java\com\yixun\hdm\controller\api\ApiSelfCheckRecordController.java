package com.yixun.hdm.controller.api;

import com.yixun.hd.api.SelfCheckRecordApi;
import com.yixun.hd.bean.in.SelfCheckRecordIn;
import com.yixun.hd.bean.out.SelfCheckRecordOut;
import com.yixun.hdm.entity.SelfCheckRecord;
import com.yixun.hdm.service.SelfCheckRecordService;
import com.yixun.hdm.utils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;


@RestController
public class ApiSelfCheckRecordController implements SelfCheckRecordApi {

    @Autowired
    private SelfCheckRecordService selfCheckRecordService;

    @Override
    public List<SelfCheckRecordOut> getList(SelfCheckRecordIn in) {
        List<SelfCheckRecord> list = selfCheckRecordService.lambdaQuery()
                .eq(SelfCheckRecord::getProjectId, in.getProjectId())
                .eq(in.getCorpId() != null, SelfCheckRecord::getCorpId, in.getCorpId())
                .list();
        return BeanUtils.copyToOutList(list, SelfCheckRecordOut.class);
    }
}
