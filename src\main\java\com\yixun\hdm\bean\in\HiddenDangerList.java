package com.yixun.hdm.bean.in;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.entity.HiddenDangerSuggestion;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class HiddenDangerList {

    private Date createTime;
    private Long projectId;
    private String projectName;
    private Long corporationId;
    private String corpName;
    private Integer turn;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long taskId;
    private Integer taskType;
    private Date taskWorkDate;
    private String gifPictures;
    private List pictures;
    private String specialCategory;
    private List specialCategoryDetail;
    private String description;
    private String type;
    private String level;
    private String tierOneBusiness;
    private String remark;
    private List files;
    private List standard;
    private List possibleConsequence;
    private String riskFactorType;
    private List riskFactorTypeDetail;
    private Long riskPointId;
    private String riskPointName;
    private String manageSource;
    private String manageSourceContent;
    private String rectificationSuggestion;
    private Integer rectifyDeadline;
    private String arrangedAuditor;
    private Long arrangedAuditorId;

    /**
     * 隐患区域
     */
    @ApiModelProperty("隐患区域")
    private String hdRegion;

    /**
     * 设备设施/物料
     */
    @ApiModelProperty("设备设施/物料")
    private String equipment;

    /**
     * 作业活动
     */
    @ApiModelProperty("作业活动")
    private String jobActivity;

    /**
     * 岗位/工种
     */
    @ApiModelProperty("岗位/工种")
    private String workCategory;

    @ApiModelProperty("建议分项录入列表")
    private List<HiddenDangerSuggestion> suggestionList = new ArrayList<>();

}
