package com.yixun.hdm.controller.gov;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.common.ErrorMessage;
import com.yixun.hdm.bean.in.*;
import com.yixun.hdm.bean.out.HdAuditOut;
import com.yixun.hdm.bean.out.HiddenDangerOut;
import com.yixun.hdm.bean.out.StaffEvaluationOut;
import com.yixun.hdm.entity.HdAudit;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.StaffEvaluation;
import com.yixun.hdm.exception.ParameterErrorException;
import com.yixun.hdm.service.*;
import com.yixun.hdm.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "admin隐患审查")
@RestController
@RequestMapping(value = "/gov/hdAudit")
public class GovHdAuditController {

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource
    private HdAuditService hdAuditService;

    @Resource
    private StaffEvaluationService staffEvaluationService;

    @Autowired
    SecurityTankApiService securityTankApiService;

//    @RequiredPermission("get:getAuditList:hiddenDanger")
    @ApiOperation(value = "获取隐患审查列表")
    @GetMapping(value = "/getAuditList")
    public CommonResult<List<HdAuditOut>> getAuditList(HdAuditListGetIn hdAuditListGetIn, CommonPage page){

        Page<HdAudit> hdAuditPage = hdAuditService.getAuditPage(hdAuditListGetIn, page);
        Page<HdAuditOut> outPage = BeanUtils.copyToOutListPage(hdAuditPage, HdAuditOut.class);

        return CommonResult.successData(outPage);
    }

    @ApiOperation(value = "获取隐患提交人")
    @GetMapping(value = "/getAuditSubmitter")
    public CommonResult<Long> getAuditSubmitter(@RequestParam Long corporationId){

        Long submitterId = hdAuditService.getAuditSubmitter(corporationId);

        return CommonResult.successData(submitterId);
    }

//    @RequiredPermission("get:getCorpAuditList:hiddenDanger")
    @ApiOperation(value = "获取公司待审查列表")
    @GetMapping(value = "/getCorpAuditList")
    public CommonResult<List<HiddenDangerOut>> getCorpAuditList(HdCorpAuditListGetIn hdCorpAuditListGetIn, CommonPage page){

        if (hdCorpAuditListGetIn.getHdAuditId()==null){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        HdAudit hdAudit = hdAuditService.getById(hdCorpAuditListGetIn.getHdAuditId());
        List<Long> submitList = JSON.parseArray(hdAudit.getSubmitList(), Long.class);
        Page<HiddenDanger> hiddenDangerList = hiddenDangerService.getCorpAuditList(submitList,
                hdCorpAuditListGetIn.getAuditStatus(), page);
        Page<HiddenDangerOut> outPage = BeanUtils.copyToOutListPage(hiddenDangerList, HiddenDangerOut.class);

        return CommonResult.successData(outPage);
    }

//    @RequiredPermission("get:getStaffEvaluationList:hiddenDanger")
    @ApiOperation(value = "获取隐患管理考核列表")
    @GetMapping(value = "/getStaffEvaluationList")
    public CommonResult<List<StaffEvaluationOut>> getStaffEvaluationList(StaffEvaluationListGetIn staffEvaluationListGetIn, CommonPage page){

        Page<StaffEvaluation> staffEvaluationList = staffEvaluationService.getList(staffEvaluationListGetIn, page);
        Page<StaffEvaluationOut> outPage = BeanUtils.copyToOutListPage(staffEvaluationList, StaffEvaluationOut.class);

        return CommonResult.successData(outPage);
    }

}
