package com.yixun.hdm.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.out.HdCorpInfoOut;
import com.yixun.hdm.bean.out.ProjectCorpHdStatisticOut;
import com.yixun.hdm.bean.out.statistic.HdCorporationOut;
import com.yixun.hdm.bean.out.statistic.HdCountOut;
import com.yixun.hdm.bean.out.statistic.HdStatusOut;
import com.yixun.hdm.bean.out.statistic.HdTypeOut;
import com.yixun.hdm.entity.dto.YqycInfoDto;

import java.util.Date;
import java.util.List;

public interface StatisticService {

    List<HdCountOut> getMonthlyTotalCount(Long projectId, Date firstMonth);

    List<HdCountOut> getMonthlyDoneCount(Long projectId, Date firstMonth);

    List<HdTypeOut> getHdTypeByProject(Long projectId, Date startDate, Date endDate);

    List<HdStatusOut> getHdStatusByProject(Long projectId, Date startDate, Date endDate);

    int getHdWrongByProject(Long projectId, Date startDate, Date endDate);

    int getCorpCountByProject(Long projectId);

    int getHdCountByProject(Long projectId, Date startDate, Date endDate);

    List<HdTypeOut> getHdTierOneTypeByProject(Long projectId, Date startDate, Date endDate);

    List<HdTypeOut> getHdRiskFactorByProject(Long projectId);

    int getHdLevelCountByProject(Long projectId, String level);

    List<HdCorporationOut> getHdCorporationByProject(Long projectId);

    Page<HdCorpInfoOut> getHdCorpInfoPage(Long projectId, List<Long> corpIds, CommonPage page);

    List<HdCorpInfoOut> getHdCorpInfoList(Long projectId, List<Long> corpIds);

    List<YqycInfoDto> getYqycInfoList(List<HdCorpInfoOut> records);

    Page<ProjectCorpHdStatisticOut> getProjectCorpHdStatistic(List<Long> corpIds, Long projectId, CommonPage page);

    List<ProjectCorpHdStatisticOut> getProjectCorpHdList(List<Long> corpIds, Long projectId);
}
