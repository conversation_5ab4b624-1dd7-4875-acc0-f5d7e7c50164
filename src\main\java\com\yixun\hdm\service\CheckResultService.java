package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.PreciseCheckListIn;
import com.yixun.hdm.bean.out.CheckResultStatisticsOut;
import com.yixun.hdm.entity.CheckResult;

import java.util.List;

/**
 *
 */
public interface CheckResultService extends IService<CheckResult> {

    Page<CheckResultStatisticsOut> getStatisticsPage(Long projectId, Long corporationId, Boolean hasHd, CommonPage commonPage);

    List<CheckResultStatisticsOut> getStatisticsList(Long projectId, Long corporationId, Boolean hasHd);

    Page<CheckResult> getCheckedList(Long taskId, Integer result, CommonPage commonPage);

    void syncPreciseCheckList(PreciseCheckListIn in);
}
