package com.yixun.hdm.bean.out;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class HdStatisticOut {

    private Integer totalCount;
    private Integer unDoneCount;
    private Long callbackCount;
    private Map statusMap;
    private Map auditStatusMap;
    private Float doneRate;
    private Map levelMap;
    private Map typeMap;
    private Map specialCategoryMap;
    private List<RiskFactorType> riskFactorTypeList;
    private Map possibleConsequenceMap;

    @Data
    public static class RiskFactorType {
        private String type;
        private Integer count;
        private Map riskFactorTypeMap;
    }
}
