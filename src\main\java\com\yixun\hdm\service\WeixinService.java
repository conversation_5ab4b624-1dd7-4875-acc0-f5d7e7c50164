package com.yixun.hdm.service;

import com.yixun.hdm.bean.weixin.AccessToken;
import com.yixun.hdm.bean.weixin.JsApiTicket;
import com.yixun.hdm.bean.weixin.MiniSession;
import com.yixun.hdm.bean.weixin.WeixinUserInfo;

public interface WeixinService {

    WeixinUserInfo getWeixinUserInfo(String weixinCode, String loginType, String name);

    AccessToken getJSAccessToken();

    JsApiTicket getJSApiTicket();

    AccessToken getAccessToken(String weixinCode, String loginType, String deviceType);

    MiniSession getMiniSessionByCode(String weixinCode);

    MiniSession getMiniSessionByOpenId(String openId);

    void pushWxMiniMessage(String openId, String templateId, String data);

}
