package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.CleanDataWhiteListGetIn;
import com.yixun.hdm.entity.CleanDataWhiteList;

/**
 *
 */
public interface CleanDataWhiteListService extends IService<CleanDataWhiteList> {

    Page<CleanDataWhiteList> getPageList(CleanDataWhiteListGetIn getIn, CommonPage commonPage);
}
