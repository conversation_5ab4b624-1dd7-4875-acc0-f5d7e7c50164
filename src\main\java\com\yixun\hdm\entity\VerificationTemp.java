package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 验证信息表
 * @TableName hdm_verification
 */
@TableName(value ="hdm_verification_temp")
@Data
public class VerificationTemp implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     *
     */
    private Date createTime;

    /**
     * 隐患id
     */
    private Long hiddenDangerId;

    /**
     * 验证负责人
     */
    private String inChargeName;

    /**
     * 验证照片（可多个）
     */
    private String pictures;

    /**
     * 是否正式提交
     */
    private Boolean isPass;

    /**
     * 不合格原因
     */
    private String reason;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}