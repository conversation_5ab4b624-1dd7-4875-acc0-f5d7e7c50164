package com.yixun.hdm.controller.admin;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.in.PreciseCheckListIn;
import com.yixun.hdm.bean.out.CheckResultOut;
import com.yixun.hdm.bean.out.CheckResultStatisticsOut;
import com.yixun.hdm.entity.CheckResult;
import com.yixun.hdm.service.CheckResultService;
import com.yixun.hdm.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "admin持表检查")
@RestController
@RequestMapping(value = "/admin/checkList")
public class AdminCheckListController {

    @Resource
    private CheckResultService checkResultService;

    @RequiredPermission("get:getCheckListStatistics:checkList")
    @ApiOperation(value = "获取持表检查台账")
    @GetMapping(value = "/getCheckListStatistics")
    public CommonResult<List<CheckResultStatisticsOut>> getHiddenDangerList(Long projectId, Long corporationId,
                                                                            Boolean hasHd, CommonPage commonPage){

        Page<CheckResultStatisticsOut> outList = checkResultService.getStatisticsPage(projectId, corporationId, hasHd, commonPage);

        return CommonResult.successData(outList);
    }

    @RequiredPermission("get:exportCheckListStatistics:checkList")
    @ApiOperation(value = "导出持表检查台账")
    @GetMapping(value = "/exportCheckListStatistics")
    public CommonResult<List<CheckResultStatisticsOut>> exportCheckListStatistics(Long projectId, Long corporationId,
                                                                            Boolean hasHd){

        List<CheckResultStatisticsOut> outList = checkResultService.getStatisticsList(projectId, corporationId, hasHd);

        return CommonResult.successData(outList);
    }

    @RequiredPermission("get:getCheckListDetail:checkList")
    @ApiOperation(value = "获取持表检查详情")
    @GetMapping(value = "/getCheckListDetail")
    public CommonResult<List<CheckResult>> getCheckListDetail(@RequestParam Long taskId, Integer result,
                                                                 CommonPage commonPage){

        Page<CheckResult> checkResultPage = checkResultService.getCheckedList(taskId, result, commonPage);
//        Page<CheckResultOut> outList = BeanUtils.copyToOutListPage(checkResultPage, CheckResultOut.class);

        return CommonResult.successData(checkResultPage);
    }

    @ApiOperation(value = "同步精准检查列表")
    @PostMapping(value = "/syncPreciseCheckList")
    public CommonResult<String> syncPreciseCheckList(@RequestBody PreciseCheckListIn in){
        checkResultService.syncPreciseCheckList(in);
        return CommonResult.successData("");
    }
}
