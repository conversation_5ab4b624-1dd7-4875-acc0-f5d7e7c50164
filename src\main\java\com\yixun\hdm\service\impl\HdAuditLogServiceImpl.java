package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.entity.HdAuditLog;
import com.yixun.hdm.service.HdAuditLogService;
import com.yixun.hdm.dao.HdAuditLogMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Service
public class HdAuditLogServiceImpl extends ServiceImpl<HdAuditLogMapper, HdAuditLog>
    implements HdAuditLogService{

    @Override
    public List<HdAuditLog> getByHdId(Long hiddenDangerId) {
        return lambdaQuery().eq(HdAuditLog::getHiddenDangerId,hiddenDangerId)
                .orderByDesc(HdAuditLog::getCreateTime)
                .list();
    }
}




