package com.yixun.hdm.controller.admin;



import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.SelfHiddenDanger;
import com.yixun.hdm.service.SelfHiddenDangerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.Serializable;
import java.util.List;

/**
 * 自查隐患详情表(SelfHiddenDanger)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-27 16:33:59
 */
@Api(tags = "自查隐患详情表")
@RestController
@RequestMapping("/admin/self/hiddenDanger")
public class AdminSelfHiddenDangerController {
    /**
     * 服务对象
     */
    @Autowired
    private SelfHiddenDangerService selfHiddenDangerService;

    /**
     * 分页查询所有数据
     *
     * @param commonPage 分页对象
     * @param selfHiddenDanger 查询实体
     * @return 所有数据
     */
    @ApiOperation("分页列表查询")
    @GetMapping("pageList")
    public CommonResult<List<SelfHiddenDanger>> pageList(CommonPage commonPage, SelfHiddenDanger selfHiddenDanger) {
        return CommonResult.successData(selfHiddenDangerService.pageList(commonPage, selfHiddenDanger));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("详情")
    @GetMapping("detail/{id}")
    public CommonResult<SelfHiddenDanger> detail(@PathVariable Serializable id) {
        return CommonResult.successData(selfHiddenDangerService.getById(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping("export")
    public void export(SelfHiddenDanger selfHiddenDanger) {
        selfHiddenDangerService.export(selfHiddenDanger);
    }

}

