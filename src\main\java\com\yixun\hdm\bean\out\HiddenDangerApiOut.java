package com.yixun.hdm.bean.out;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.entity.HiddenDangerSuggestion;
import com.yixun.hdm.utils.DateJsonSerializer;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class HiddenDangerApiOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long operatorId;
    private String operator;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    private String projectName;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;
    private String corpName;
    private Integer turn;
    private String status;
    /**
     * 是否需老师修改
     */
    private Boolean isNeedEdit;
    private List pictures;
    private String gifFile;
    private String description;
    private String specialCategory;
    private List specialCategoryDetail;
    private Long riskPointId;
    private String riskPointName;
    private String type;
    private String level;
    private String tierOneBusiness;
    private String remark;
    private List files;
    private List standard;
    private List possibleConsequence;
    private String riskFactorType;
    private List riskFactorTypeDetail;
    private String riskDescription;
    private String manageSource;
    private String manageSourceContent;
    private String rectificationSuggestion;
    private Integer rectifyDeadline;
    private Boolean isFindByCallback;
    /**
     * 任务id
     */
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long taskId;

    /**
     * 任务类型 1-调研 2-辅助调查培训 3-回访 4-联合调研
     */
    private Integer taskType;

    /**
     * 任务时间
     */
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date taskWorkDate;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date rectificationTime;
    private List rectificationPictures;
    private String rectificationDescription;
    private String rectificationOperator;

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date verifyTime;
    private List verifyPictures;
    private String verifyDescription;
    private String verifyOperator;

    /**
     * 设备设施/物料
     */
    @ApiModelProperty("设备设施/物料")
    private String equipment;

    /**
     * 作业活动
     */
    @ApiModelProperty("作业活动")
    private String jobActivity;

    /**
     * 岗位/工种
     */
    @ApiModelProperty("岗位/工种")
    private String workCategory;

    /**
     * 隐患质量分
     */
    @ApiModelProperty("隐患质量分")
    private Integer hdQualityScore;

    @ApiModelProperty("建议分项录入列表")
    private List<HiddenDangerSuggestion> suggestionList = new ArrayList<>();

    public void setPictures(String pictures) {
        this.pictures = JSON.parseObject(pictures, List.class);
    }

    public void setRectificationPictures(String rectificationPictures) {
        this.rectificationPictures = JSON.parseObject(rectificationPictures, List.class);
    }

    public void setVerifyPictures(String verifyPictures) {
        this.verifyPictures = JSON.parseObject(verifyPictures, List.class);
    }

    public void setFiles(String files) {
        this.files = JSON.parseObject(files, List.class);
    }

    public void setStandard(String standard) {
        this.standard = JSON.parseObject(standard, List.class);
    }

    public void setPossibleConsequence(String possibleConsequence) {
        this.possibleConsequence = JSON.parseObject(possibleConsequence, List.class);
    }

    public void setRiskFactorTypeDetail(String riskFactorTypeDetail) {
        this.riskFactorTypeDetail = JSON.parseObject(riskFactorTypeDetail, List.class);
    }

    public void setSpecialCategoryDetail(String specialCategoryDetail) {
        this.specialCategoryDetail = JSON.parseObject(specialCategoryDetail, List.class);
    }
}
