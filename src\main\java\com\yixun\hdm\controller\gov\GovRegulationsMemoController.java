package com.yixun.hdm.controller.gov;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.out.RegulationsMemoOut;
import com.yixun.hdm.entity.RegulationsMemo;
import com.yixun.hdm.service.RegulationsMemoService;
import com.yixun.hdm.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "admin法规备忘录")
@RestController
@RequestMapping("/gov/regulationsMemo")
public class GovRegulationsMemoController {

    @Resource
    private RegulationsMemoService regulationsMemoService;

//    @RequiredPermission("get:getList:regulationsMemo")
    @GetMapping("/getList")
    @ApiOperation(value = "获取法规备忘录列表")
    public CommonResult<List<RegulationsMemoOut>> getList(CommonPage page){

        Page<RegulationsMemo> regulationsMemoPage = regulationsMemoService.getList(page);
        Page<RegulationsMemoOut> outList = BeanUtils.copyToOutListPage(regulationsMemoPage, RegulationsMemoOut.class);

        return CommonResult.successData(outList);
    }

}
