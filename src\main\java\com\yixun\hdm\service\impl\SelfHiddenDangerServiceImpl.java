package com.yixun.hdm.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hd.bean.in.SelfHiddenDangerIn;
import com.yixun.hd.bean.out.SelfHiddenDangerOut;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.dao.SelfHiddenDangerMapper;
import com.yixun.hdm.entity.SelfHiddenDanger;
import com.yixun.hdm.service.SelfHiddenDangerService;
import com.yixun.hdm.utils.ExcelOutUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 自查隐患详情表(SelfHiddenDanger)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-27 16:34:00
 */
@Service("selfHiddenDangerService")
public class SelfHiddenDangerServiceImpl extends ServiceImpl<SelfHiddenDangerMapper, SelfHiddenDanger> implements SelfHiddenDangerService {

    @Autowired
    private HttpServletResponse response;
    @Override
    public void export(SelfHiddenDanger selfHiddenDanger) {
        CommonPage commonPage = new CommonPage();
        commonPage.setPageNum(1);
        commonPage.setPageSize(1000);
        Page<SelfHiddenDanger> selfHiddenDangerPage = pageList(commonPage, selfHiddenDanger);
        List<SelfHiddenDanger> list = selfHiddenDangerPage.getRecords();
        ExcelOutUtil.exportByTemp(response, "/file/自查隐患台账导出模板.xlsx", "自查隐患台账" + DatePattern.PURE_DATETIME_FORMAT.format(new Date()) + ".xlsx", null, list);
    }

    @Override
    public Page<SelfHiddenDanger> pageList(CommonPage commonPage, SelfHiddenDanger selfHiddenDanger) {
        Page<SelfHiddenDanger> page = new Page<>(commonPage.getPageNum(),commonPage.getPageSize());
        Date createTimeMin = selfHiddenDanger.getCreateTimeMin();
        if(createTimeMin != null){
            selfHiddenDanger.setCreateTimeMin(null);
        }
        Date createTimeMax = selfHiddenDanger.getCreateTimeMax();
        if(createTimeMax != null){
            selfHiddenDanger.setCreateTimeMax(null);
        }
        QueryWrapper<SelfHiddenDanger> selfHiddenDangerQueryWrapper = new QueryWrapper<>(selfHiddenDanger);
        selfHiddenDangerQueryWrapper.between(createTimeMin != null && createTimeMax != null,"create_time", createTimeMin, createTimeMax);
        selfHiddenDangerQueryWrapper.orderByDesc("create_time");
        return page(page, selfHiddenDangerQueryWrapper);
    }

    @Override
    public List<SelfHiddenDanger> getList(SelfHiddenDangerIn in) {
        List<SelfHiddenDanger> list = lambdaQuery().eq(SelfHiddenDanger::getProjectId, in.getProjectId())
                .eq(in.getCorpId() != null, SelfHiddenDanger::getCorpId, in.getCorpId())
                .eq(in.getStatus() != null, SelfHiddenDanger::getStatus, in.getStatus())
                .list();
        return list;
    }
}

