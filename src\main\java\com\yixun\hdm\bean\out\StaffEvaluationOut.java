package com.yixun.hdm.bean.out;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateJsonSerializer;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;

@Data
public class StaffEvaluationOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    private String projectName;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;
    private String corpName;
    private String surveyManager;
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date surveyDate;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date submitTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date submitExpiredTime;
    private Integer firstSubmitCount;
    private String auditor;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date auditExpiredTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date auditFinishTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date releaseTime;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date releaseExpiredTime;
}
