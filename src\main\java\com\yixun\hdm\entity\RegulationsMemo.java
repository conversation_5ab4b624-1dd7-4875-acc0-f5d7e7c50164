package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName hdm_regulations_memo
 */
@TableName(value ="hdm_regulations_memo")
@Data
public class RegulationsMemo implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Date createTime;

    private Boolean isUsed;

    private String creator;

    private Long sourceId;

    private String innerStandardId;

    /**
     * 
     */
    private String type;

    /**
     * 
     */
    private String categoryL1;

    /**
     * 
     */
    private String categoryL2;

    /**
     * 
     */
    private String categoryL3;

    /**
     * 
     */
    private String categoryL4;

    /**
     * 
     */
    private String standardName;

    /**
     * 
     */
    private String standardSn;

    /**
     * 
     */
    private String firstReleaseDate;

    /**
     * 
     */
    private String firstEffectDate;

    /**
     * 
     */
    private String latestReleaseSn;

    /**
     * 
     */
    private String latestReleaseDate;

    /**
     * 
     */
    private String latestEffectDate;

    /**
     * 
     */
    private String chapterNameL1;

    /**
     * 
     */
    private String chapterNameL2;

    /**
     * 
     */
    private String chapterNameL3;

    /**
     * 
     */
    private String chapterNameL4;

    /**
     * 
     */
    private String termsContent;

    /**
     * 
     */
    private String picture1;

    /**
     * 
     */
    private String picture2;

    /**
     * 
     */
    private String picture3;

    /**
     * 
     */
    private String picture4;

    /**
     * 
     */
    private String explanation;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}