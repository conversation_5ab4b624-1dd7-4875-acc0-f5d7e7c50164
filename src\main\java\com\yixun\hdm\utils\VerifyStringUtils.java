package com.yixun.hdm.utils;

/**
 * 判断各种格式工具
 */
public class VerifyStringUtils {

    public static final String isMoblie = "^[1][3,5,7,8]+\\d{9}"; // 判断手机格式
    public static final String isNumber = "^[0-9]*$"; // 判断只能是数字
    public static final String isMoney = "^[0-9]+\\.{0,1}[0-9]{0,2}$"; // 判断只能金钱模式整数或小数
    public static final String isEmail = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$"; // 判断邮箱
    public static final String isPwd = "^(?=.*?[a-zA-Z])(?=.*?[0-9])[a-zA-Z0-9]{6,}$"; // 密码必须是字母和数字的组合
    public static final String isPhone = "[\\d\\-]*"; // 判断电话格式  必须是纯数字或者数字+“-”；
    public static final String isDate = "^(\\d{4})-(\\d{1}||\\d{2})-(\\d{1}||\\d{2}) (\\d{1}||\\d{2}):(\\d{1}||\\d{2}):(\\d{1}||\\d{2})$"; // 判断时间
    public static final String isDates = "^(\\d{4})-(\\d{1}||\\d{2})-(\\d{1}||\\d{2})$"; // 判断时间

    /**
     * 判断输入字符串是否合法
     *
     * @return
     * @throws Exception
     */
    public static boolean verify(Object values, int min, int max, String type, Boolean isMust) {
        String value = values + "";
        if (isMust) { //如果是必填
            if (null == value || "".equals(value.trim()) || "null".equals(value.trim())
					|| value.length() < min || value.length() > max) {
                return false;
            } else {
                if (!"".equals(type.trim()) && !value.trim().matches(type)) {
                    return false;
                }
            }
        } else {  //如果是非必填
            if (null != value && !"".equals(value.trim()) && !"null".equals(value.trim())) {
                if ((value.length() < min || value.length() > max)) {
                    return false;
                } else {
                    if (!"".equals(type.trim()) && !value.trim().matches(type)) {
                        return false;
                    }
                }
            }

        }
        return true;
    }
}