package com.yixun.hdm.bean.in;

import com.yixun.hdm.bean.common.CommonPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Collection;

@Data
public class CorpSafeListIn extends CommonPage {

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty(value="项目id")
    private Long projectId;

    @ApiModelProperty("公司IDs")
    private Collection<Long> corpIds;

    @ApiModelProperty("公司名称")
    private String corpName;

    @ApiModelProperty("工伤同比增长率符号；1：>=；2：<=")
    private Integer workInjuryGrowthRateSymbol;

    @ApiModelProperty("工伤同比增长率数字")
    private String workInjuryGrowthRateNumber;

    @ApiModelProperty("隐患逾期情况；1：全部逾期；2：逾期5天；3：逾期10天")
    private Integer hdOverdue;

}
