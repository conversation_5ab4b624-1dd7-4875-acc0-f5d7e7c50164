package com.yixun.hdm.controller.admin;

import com.alibaba.fastjson.JSON;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.in.VerificationIn;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.OperationLogs;
import com.yixun.hdm.entity.Verification;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.entity.em.AuditStatus;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import com.yixun.hdm.entity.em.OperationType;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.service.UserCenterService;
import com.yixun.hdm.service.VerificationService;
import com.yixun.hdm.utils.AdminUserHelper;
import com.yixun.hdm.utils.BeanUtils;
import com.yixun.hdm.utils.SnGeneratorUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Api(tags = "admin隐患验证")
@RestController
@RequestMapping(value = "/admin/verification")
public class AdminVerificationController {

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource
    private VerificationService verificationService;

    @Resource
    private UserCenterService userCenterService;

    @PostMapping("/submit")
    @ApiOperation("隐患验证提交")
    public CommonResult<Void> submit(@RequestBody VerificationIn hiddenDangerIn) {

        HiddenDanger hiddenDanger = hiddenDangerService.getById(hiddenDangerIn.getHiddenDangerId());
        if (hiddenDanger==null){
            throw new DataErrorException("未找到该隐患");
        }
        if(hiddenDanger.getTaskType() == 1 || hiddenDanger.getTaskType() == 2){
            throw new DataErrorException("任务类型错误");
        }
        if(AuditStatus.UnReleased.name().equals(hiddenDanger.getAuditStatus())){
            throw new DataErrorException("隐患未发布");
        }
        Verification verification = verificationService.getVerification(hiddenDangerIn.getHiddenDangerId());
        OperationLogs operationLogs = null;
        if(verification == null){
            verification = new Verification();
            Long verificationId = SnGeneratorUtil.getId();
            verification.setId(verificationId);
            verification.setCreateTime(new Date());
            operationLogs = new OperationLogs();
            operationLogs.setId(SnGeneratorUtil.getId());
            operationLogs.setCreateTime(new Date());
            operationLogs.setCorporationId(hiddenDanger.getCorporationId());
            operationLogs.setHiddenDangerId(hiddenDangerIn.getHiddenDangerId());
            operationLogs.setOperationType(OperationType.verification.name());
            operationLogs.setInChargeName(hiddenDangerIn.getInChargeName());
            StaffUserDto adminUser = userCenterService.getAdminUser(AdminUserHelper.getCurrentUserId());
            operationLogs.setOperatorId(adminUser.getId());
            operationLogs.setOperator(adminUser.getRealName());
        }
        BeanUtils.copyProperties(hiddenDangerIn, verification);
        verification.setPictures(JSON.toJSONString(hiddenDangerIn.getPictures()));

        if (hiddenDangerIn.getIsPass()){
            hiddenDanger.setStatus(HiddenDangerStatus.已改善.name());
        }else {
            hiddenDanger.setStatus(HiddenDangerStatus.待改善.name());
        }
        hiddenDanger.setUpdateTime(new Date());
        verificationService.submitVerification(hiddenDanger, verification, operationLogs);


        return CommonResult.successResult("提交成功");
    }

    @PostMapping("/update")
    @ApiOperation("修改验证信息")
    public CommonResult<Void> update(@RequestBody VerificationIn hiddenDangerIn) {
        verificationService.updateVerification(hiddenDangerIn);
        return CommonResult.successResult("提交成功");
    }

    @GetMapping("/getVerification")
    @ApiOperation("获取验证信息")
    public CommonResult<Verification> getVerification(Long hiddenDangerId){
        Verification verification = verificationService.getVerification(hiddenDangerId);
        return CommonResult.successData(verification);
    }


}
