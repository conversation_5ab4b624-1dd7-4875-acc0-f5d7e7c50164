<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yixun.hdm.dao.CheckResultMapper">
    <select id="getStatisticsPage" resultType="com.yixun.hdm.bean.out.CheckResultStatisticsOut">
        select task_id, project_id, project_name, corporation_id, corp_name, executor, accompany,
            min(create_time) as startTime, max(create_time) as endTime, count(*) as checkCount,
            sum(case when result = 0 THEN 1 ELSE 0 END) as passCount,
            sum(case when result = 1 THEN 1 ELSE 0 END) as hdCount,
            sum(case when result = 2 THEN 1 ELSE 0 END) as uninvolvedCount,
            sum(case when result = 3 THEN 1 ELSE 0 END) as repeatCount,
            sum(case when result is null THEN 1 ELSE 0 END) as unCheckCount
        from hdm_check_result
        <where>
            <if test="projectId!=null">
                and project_id = #{projectId}
            </if>
            <if test="corporationId!=null">
                and corporation_id = #{corporationId}
            </if>
        </where>
        group by task_id
        order by create_time desc
    </select>
    <select id="getStatisticsList" resultType="com.yixun.hdm.bean.out.CheckResultStatisticsOut">
        select task_id, project_id, project_name, corporation_id, corp_name, executor, accompany,
        min(create_time) as startTime, max(create_time) as endTime, count(*) as checkCount,
        sum(case when result = 0 THEN 1 ELSE 0 END) as passCount,
        sum(case when result = 1 THEN 1 ELSE 0 END) as hdCount,
        sum(case when result = 2 THEN 1 ELSE 0 END) as uninvolvedCount
        from hdm_check_result
        <where>
            <if test="projectId!=null">
                and project_id = #{projectId}
            </if>
            <if test="corporationId!=null">
                and corporation_id = #{corporationId}
            </if>
        </where>
        group by task_id
        <if test="hasHd != null and hasHd==true">
            having hdCount > 0
        </if>
        <if test="hasHd != null and hasHd==false">
            having hdCount = 0
        </if>
        order by create_time desc
    </select>
</mapper>