package com.yixun.hdm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.PostLogsIn;
import com.yixun.hdm.dao.PostLogsMapper;
import com.yixun.hdm.entity.HdmPostLogs;
import com.yixun.hdm.service.PostLogsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;

@Service
@DS("opt_logs")
public class PostLogsServiceImpl implements PostLogsService {

    @Resource
    private PostLogsMapper postLogsMapper;

    @Override
    public void insert(HdmPostLogs postLogs) {
        postLogsMapper.insert(postLogs);
    }

    @Override
    public Page<HdmPostLogs> getList(PostLogsIn postLogsIn, CommonPage commonPage) {
        QueryWrapper<HdmPostLogs> queryWrapper = new QueryWrapper<>();
        if (postLogsIn.getOptTimeStart()!=null && postLogsIn.getOptTimeEnd()!=null){
            Calendar c  = Calendar.getInstance();
            c.setTime(postLogsIn.getOptTimeEnd());
            c.add(Calendar.SECOND,86399);
            queryWrapper.between("opt_time", postLogsIn.getOptTimeStart(), c.getTime());
        }
        if (postLogsIn.getOperator()!=null){
            queryWrapper.like("operator", postLogsIn.getOperator());
        }
        if (postLogsIn.getGroupName()!=null){
            queryWrapper.like("group_name", postLogsIn.getGroupName());
        }
        if (postLogsIn.getPath()!=null){
            queryWrapper.eq("path", postLogsIn.getPath());
        }
        if (postLogsIn.getArgs()!=null){
            queryWrapper.like("args", postLogsIn.getArgs());
        }
        if (postLogsIn.getFuncName()!=null){
            queryWrapper.like("func_name", postLogsIn.getFuncName());
        }
        queryWrapper.orderByDesc("opt_time");

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<HdmPostLogs>) postLogsMapper.selectPage(page, queryWrapper);
    }

}
