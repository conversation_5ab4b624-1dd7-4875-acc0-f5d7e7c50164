package com.yixun.hdm.controller.admin;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.vo.*;
import com.yixun.hdm.service.SelfCheckStatisticService;
import com.yixun.hdm.utils.ExcelOutUtil;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 智查自纠看板统计相关接口
 */
@Validated
@RestController
@AllArgsConstructor
@RequestMapping("/admin/self/statistic")
public class AdminSelfCheckStatisticController {

	private final SelfCheckStatisticService selfCheckStatisticService;


	/**
	 * 自查企业统计
	 *
	 * @param projectId 项目ID
	 * @return 自查企业统计结果
	 */
	@GetMapping("/corp")
	public CommonResult<SelfCheckCorpStatisticVO> selfCheckCorpStatistic(@RequestParam String projectId) {
		return CommonResult.successData(selfCheckStatisticService.selfCheckCorpStatistic(projectId));
	}

	/**
	 * 检查情况
	 *
	 * @param projectId 项目ID
	 * @return 检查情况统计结果
	 */
	@GetMapping("/situation")
	public CommonResult<SelfCheckSituationStatisticVO> selfCheckSituationStatistic(@RequestParam String projectId) {
		return CommonResult.successData(selfCheckStatisticService.selfCheckSituationStatistic(projectId));
	}

	/**
	 * 隐患整改情况
	 *
	 * @param projectId 项目ID
	 * @return 隐患整改情况统计结果
	 */
	@GetMapping("/hdanger")
	public CommonResult<HiddenDangerRectificationVO> hiddenDangerRectification(@RequestParam String projectId) {
		return CommonResult.successData(selfCheckStatisticService.hiddenDangerRectification(projectId));
	}

	/**
	 * 企业统计列表
	 *
	 * @param selfCheckQueryParam 查询参数
	 * @return 企业统计列表
	 */
	@GetMapping("/corp/list")
	public CommonResult<List<SelfCheckResult>> corpSelfCheckStatistic(@Validated SelfCheckQueryParam selfCheckQueryParam) {
		Integer selfCheckProgressSymbol = selfCheckQueryParam.getSelfCheckProgressSymbol();
		Double selfCheckProgress = selfCheckQueryParam.getSelfCheckProgress();
		if (ObjUtil.isNotNull(selfCheckProgressSymbol) && ObjUtil.isNotNull(selfCheckProgress)) {
			if (selfCheckProgressSymbol == 1) {
				selfCheckQueryParam.setMinSelfCheckProgress(selfCheckProgress);
			}
			if (selfCheckProgressSymbol == 2) {
				selfCheckQueryParam.setMaxSelfCheckProgress(selfCheckProgress);
			}
		}
		Integer hiddenDangerDetectionRateSymbol = selfCheckQueryParam.getHiddenDangerDetectionRateSymbol();
		Double hiddenDangerDetectionRate = selfCheckQueryParam.getHiddenDangerDetectionRate();
		if (ObjUtil.isNotNull(hiddenDangerDetectionRateSymbol) && ObjUtil.isNotNull(hiddenDangerDetectionRate)) {
			if (hiddenDangerDetectionRateSymbol == 1) {
				selfCheckQueryParam.setMinHiddenDangerDetectionRate(hiddenDangerDetectionRate);
			}
			if (hiddenDangerDetectionRateSymbol == 2) {
				selfCheckQueryParam.setMaxHiddenDangerDetectionRate(hiddenDangerDetectionRate);
			}
		}
		Integer hiddenDangersRectifiedRateSymbol = selfCheckQueryParam.getHiddenDangersRectifiedRateSymbol();
		Double hiddenDangersRectifiedRate = selfCheckQueryParam.getHiddenDangersRectifiedRate();
		if (ObjUtil.isNotNull(hiddenDangersRectifiedRateSymbol) && ObjUtil.isNotNull(hiddenDangersRectifiedRate)) {
			if (hiddenDangersRectifiedRateSymbol == 1) {
				selfCheckQueryParam.setMinHiddenDangersRectifiedRate(hiddenDangersRectifiedRate);
			}
			if (hiddenDangersRectifiedRateSymbol == 2) {
				selfCheckQueryParam.setMaxHiddenDangersRectifiedRate(hiddenDangersRectifiedRate);
			}
		}
		return CommonResult.successPageData(selfCheckStatisticService.corpSelfCheckStatistic(selfCheckQueryParam));
	}

	/**
	 * 企业统计列表导出
	 *
	 * @param selfCheckQueryParam 查询参数
	 */
	@GetMapping("/corp/list/export")
	public void corpSelfCheckStatisticExport(@Validated SelfCheckQueryParam selfCheckQueryParam, HttpServletResponse response) {
		String projectName = selfCheckQueryParam.getProjectName();
		if (StrUtil.isBlank(projectName)) {
			throw new RuntimeException("项目名称不能为空");
		}
		Integer selfCheckProgressSymbol = selfCheckQueryParam.getSelfCheckProgressSymbol();
		Double selfCheckProgress = selfCheckQueryParam.getSelfCheckProgress();
		if (ObjUtil.isNotNull(selfCheckProgressSymbol) && ObjUtil.isNotNull(selfCheckProgress)) {
			if (selfCheckProgressSymbol == 1) {
				selfCheckQueryParam.setMinSelfCheckProgress(selfCheckProgress);
			}
			if (selfCheckProgressSymbol == 2) {
				selfCheckQueryParam.setMaxSelfCheckProgress(selfCheckProgress);
			}
		}
		Integer hiddenDangerDetectionRateSymbol = selfCheckQueryParam.getHiddenDangerDetectionRateSymbol();
		Double hiddenDangerDetectionRate = selfCheckQueryParam.getHiddenDangerDetectionRate();
		if (ObjUtil.isNotNull(hiddenDangerDetectionRateSymbol) && ObjUtil.isNotNull(hiddenDangerDetectionRate)) {
			if (hiddenDangerDetectionRateSymbol == 1) {
				selfCheckQueryParam.setMinHiddenDangerDetectionRate(hiddenDangerDetectionRate);
			}
			if (hiddenDangerDetectionRateSymbol == 2) {
				selfCheckQueryParam.setMaxHiddenDangerDetectionRate(hiddenDangerDetectionRate);
			}
		}
		Integer hiddenDangersRectifiedRateSymbol = selfCheckQueryParam.getHiddenDangersRectifiedRateSymbol();
		Double hiddenDangersRectifiedRate = selfCheckQueryParam.getHiddenDangersRectifiedRate();
		if (ObjUtil.isNotNull(hiddenDangersRectifiedRateSymbol) && ObjUtil.isNotNull(hiddenDangersRectifiedRate)) {
			if (hiddenDangersRectifiedRateSymbol == 1) {
				selfCheckQueryParam.setMinHiddenDangersRectifiedRate(hiddenDangersRectifiedRate);
			}
			if (hiddenDangersRectifiedRateSymbol == 2) {
				selfCheckQueryParam.setMaxHiddenDangersRectifiedRate(hiddenDangersRectifiedRate);
			}
		}
		PageInfo<SelfCheckResult> selfCheckResultPageInfo = selfCheckStatisticService.corpSelfCheckStatistic(selfCheckQueryParam);
		Map<String, String> map = new HashMap<>();
		map.put(SelfCheckQueryParam.Fields.projectName, projectName);
		ExcelOutUtil.exportByTemp(response, "/file/智查自纠企业统计表.xlsx", "智查自纠企业统计表-" + DatePattern.PURE_DATETIME_FORMAT.format(new Date()) + ".xlsx", map, selfCheckResultPageInfo.getList());
	}


}
