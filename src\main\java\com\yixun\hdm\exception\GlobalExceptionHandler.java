package com.yixun.hdm.exception;

import com.yixun.hdm.bean.common.CommonErrorInfo;
import com.yixun.hdm.bean.common.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(DataErrorException.class)
    public CommonResult data(DataErrorException e){
        log.error("DataErrorException",e);
        return CommonResult.failResult(CommonErrorInfo.code_6001, e.getMessage());
    }

    @ExceptionHandler(UnLoginException.class)
    public CommonResult unLogin(UnLoginException e){
        log.error("UnLoginException: ",e);
        return CommonResult.failResult(CommonErrorInfo.code_4001, e.getMessage());
    }

    @ExceptionHandler(PermissionErrorException.class)
    public CommonResult unLogin(PermissionErrorException e){
        log.error("PermissionErrorException: ",e);
        return CommonResult.failResult(CommonErrorInfo.code_2001, e.getMessage());
    }

    @ExceptionHandler(ParameterErrorException.class)
    public CommonResult parameter(ParameterErrorException e){
        log.error("ParameterErrorException: ",e);
        return CommonResult.failResult(CommonErrorInfo.code_1001, e.getMessage());
    }

    @ExceptionHandler(BindException.class)
    public CommonResult handleBindException(BindException e){
        log.error("handleBindException: ",e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return CommonResult.failResult(CommonErrorInfo.code_1001, message);
    }

    @ExceptionHandler(RuntimeException.class)
    public CommonResult runtime(RuntimeException e){
        log.error("RuntimeException: ",e);
        return CommonResult.failResult(CommonErrorInfo.code_3001, e.getMessage());
    }

    @ExceptionHandler(ClientAbortException.class)
    public String runtime(ClientAbortException e){
        log.error("ClientAbortException: ",e);
        return "ClientAbortException:" + e.getMessage();
    }
}
