package com.yixun.hdm.controller.admin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.yixun.hdm.aop.RequiredPermission;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.common.ErrorMessage;
import com.yixun.hdm.bean.in.*;
import com.yixun.hdm.bean.out.*;
import com.yixun.hdm.entity.*;
import com.yixun.hdm.entity.dto.*;
import com.yixun.hdm.entity.em.AuditStatus;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import com.yixun.hdm.entity.em.OperationType;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.exception.ParameterErrorException;
import com.yixun.hdm.service.*;
import com.yixun.hdm.utils.*;
import com.yixun.teacher.api.TaskApi;
import com.yixun.teacher.bean.enums.TaskTypeEnum;
import com.yixun.teacher.bean.in.ApiTaskGetIn;
import com.yixun.teacher.bean.out.TaskOut;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "admin隐患基本情况")
@RestController
@RequestMapping(value = "/admin/hiddenDanger")
public class AdminHiddenDangerController {

    @Autowired
    private TaskApi taskApi;

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Resource
    private RectificationService rectificationService;

    @Resource
    private VerificationService verificationService;

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private HdAuditLogService hdAuditLogService;

    @Autowired
    private HiddenDangerSuggestionService hiddenDangerSuggestionService;

    @ApiOperation(value = "获取企业隐患质量")
    @GetMapping(value = "/getCorpHdQuality")
    public CommonResult<CorpHdQualityOut> getCorpHdQuality(Long corpId){
        LambdaQueryWrapper<HiddenDanger> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HiddenDanger::getCorporationId, corpId);
        List<HiddenDanger> list = hiddenDangerService.list(wrapper);
        CorpHdQualityOut out = new CorpHdQualityOut();
        Integer corpScore = HdQualityScoreUtils.calcEvaluateHdScore(list);
        Integer effectiveHdNum = HdQualityScoreUtils.calcEffectiveHdNum(list);
        out.setCorpHdQualityScore(corpScore);
        out.setEffectiveHdNum(effectiveHdNum);
        return CommonResult.successData(out);
    }

    @ApiOperation(value = "获取隐患任务列表")
    @GetMapping(value = "/getTaskList")
    public com.yixun.bean.CommonResult<List<TaskOut>> getTaskList(TaskGetIn taskGetIn, CommonPage commonPage){

        ApiTaskGetIn apiTaskGetIn = new ApiTaskGetIn();
        if (taskGetIn.getProjectId()!=null){
            apiTaskGetIn.setProjectId(taskGetIn.getProjectId());
        }
        if (taskGetIn.getCorporationId()!=null){
            apiTaskGetIn.setCorporationId(taskGetIn.getCorporationId());
        }
        if (taskGetIn.getTurn()!=null){
            apiTaskGetIn.setTurn(taskGetIn.getTurn());
        }
        apiTaskGetIn.setTypes(Arrays.asList(TaskTypeEnum.Survey, TaskTypeEnum.Callback));
        apiTaskGetIn.setPageNum(commonPage.getPageNum());
        apiTaskGetIn.setPageSize(commonPage.getPageSize());
        return taskApi.getTaskPageList(apiTaskGetIn);
    }

    @RequiredPermission("get:getHiddenDangerList:hiddenDanger")
    @ApiOperation(value = "获取隐患列表")
    @PostMapping(value = "/getHiddenDangerList")
    public CommonResult<List<HiddenDangerOut>> getHiddenDangerList(@RequestBody HiddenDangerListGetIn hiddenDangerListGetIn){

        PageInfo<HiddenDanger> hiddenDangerList = hiddenDangerService.getHiddenDangerPage(hiddenDangerListGetIn);
        PageInfo<HiddenDangerOut> outPage = BeanUtils.copyToOutListPage(hiddenDangerList, HiddenDangerOut.class);
        return CommonResult.successPageData(outPage);
    }

    @RequiredPermission("get:getHiddenDangerAllList:hiddenDanger")
    @ApiOperation(value = "获取所有隐患列表")
    @PostMapping(value = "/getHiddenDangerAllList")
    public CommonResult<List<HiddenDanger>> getHiddenDangerAllList(@RequestBody HiddenDangerListGetIn hiddenDangerListGetIn){

        List<HiddenDanger> hiddenDangerList = hiddenDangerService.getHiddenDangerAllList(hiddenDangerListGetIn);

        return CommonResult.successData(hiddenDangerList);
    }

    @RequiredPermission("get:getHiddenDangerDetail:hiddenDanger")
    @ApiOperation(value = "获取隐患详情")
    @GetMapping(value = "/getHiddenDangerDetail/{hiddenDangerId}")
    public CommonResult<HiddenDangerDetailOut> getHiddenDangerDetail(@PathVariable("hiddenDangerId") Long hiddenDangerId){

        HiddenDanger hiddenDanger = hiddenDangerService.getById(hiddenDangerId);
        if (hiddenDanger==null){
            throw new DataErrorException("未找到该隐患");
        }

        HiddenDangerDetailOut out = new HiddenDangerDetailOut();
        out.setHiddenDanger(hiddenDanger);

        //获取审查记录（最近2次）
        List<HdAuditLog> hdAuditLogs = hdAuditLogService.getByHdId(hiddenDangerId);
        if(hdAuditLogs != null && !hdAuditLogs.isEmpty()){
            out.setHdAuditLog(hdAuditLogs.get(0));
        }

        //整改和验证记录
        List<Rectification> rectificationList = rectificationService.getByHiddenDanger(hiddenDangerId);
        List<RectificationOut> rectificationOutList = BeanUtils.copyToOutList(rectificationList, RectificationOut.class);
        rectificationOutList.forEach(r->r.setType("rectification"));
        List<Verification> verificationList = verificationService.getByHiddenDanger(hiddenDangerId);
        List<VerificationOut> verificationOutsList = BeanUtils.copyToOutList(verificationList, VerificationOut.class);
        verificationOutsList.forEach(r->r.setType("verification"));

        List<HdManageItemsOut> hdManageItems = new ArrayList<>();
        HdSubmitOut hdSubmitOut = new HdSubmitOut();
        BeanUtils.copyProperties(hiddenDanger, hdSubmitOut);
        hdSubmitOut.setType("submit");
        hdSubmitOut.setPictures(hiddenDanger.getPictures());
        hdManageItems.add(hdSubmitOut);
        hdManageItems.addAll(rectificationOutList);
        hdManageItems.addAll(verificationOutsList);
        hdManageItems = hdManageItems.stream()
                .sorted(Comparator.comparing(HdManageItemsOut::getCreateTime)).collect(Collectors.toList());
        out.setHdManageItems(hdManageItems);
        //建议措施
        LambdaQueryWrapper<HiddenDangerSuggestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HiddenDangerSuggestion::getHdId, hiddenDangerId);
        List<HiddenDangerSuggestion> suggestionList = hiddenDangerSuggestionService.list(queryWrapper);
        out.getHiddenDanger().setSuggestionList(suggestionList);

        return CommonResult.successData(out);
    }

    @RequiredPermission("save:doSave:hiddenDanger")
    @ApiOperation(value = "保存隐患详情")
    @PostMapping(value = "/doSave")
    public CommonResult doSave(@RequestBody HiddenDangerIn hiddenDangerIn){

        //检查参数
//        if (!BeanFieldCheckingUtils.forAllFieldNotNull(hiddenDangerIn,
//                Arrays.asList("gifPictures","checkOptionId","riskPointId","riskPointName"))){
//            throw new ParameterErrorException(ErrorMessage.parameter_error);
//        }

        if (hiddenDangerIn.getRiskFactorTypeDetail().size()<=1){
            throw new DataErrorException("危害因素分类必须填到二级以上");
        }

        if("重大隐患".equals(hiddenDangerIn.getLevel()) && hiddenDangerIn.getSuggestionList().isEmpty()){
            throw new DataErrorException("重大隐患建议改善措施必须分项录入");
        }

        Long userId = AdminUserHelper.getCurrentUserId();
        StaffUserDto adminUser = userCenterService.getAdminUser(userId);
        CorporationDto corporation = userCenterService.getCorporation(hiddenDangerIn.getCorporationId());
        ProjectDto project = userCenterService.getProject(hiddenDangerIn.getProjectId());

        HiddenDanger hiddenDanger = new HiddenDanger();
        BeanUtils.copyProperties(hiddenDangerIn, hiddenDanger);
        Long hiddenDangerId = SnGeneratorUtil.getId();
        hiddenDanger.setId(hiddenDangerId);
        hiddenDanger.setCreateTime(new Date());
        hiddenDanger.setOperator(adminUser.getRealName());
        hiddenDanger.setOperatorId(adminUser.getId());
        hiddenDanger.setCorpName(corporation.getCorpName());
        hiddenDanger.setProjectName(project.getProjectName());
        hiddenDanger.setPictures(hiddenDangerIn.getPictures());

        if (hiddenDangerIn.getGifPictures()!=null){
            hiddenDanger.setGifPictures(hiddenDangerIn.getGifPictures());
        }
        hiddenDanger.setFiles(hiddenDangerIn.getFiles());
        hiddenDanger.setStandard(JSON.toJSONString(hiddenDangerIn.getStandard()));
        hiddenDanger.setSpecialCategoryDetail(JSON.toJSONString(hiddenDangerIn.getSpecialCategoryDetail()));
        hiddenDanger.setRiskFactorTypeDetail(JSON.toJSONString(hiddenDangerIn.getRiskFactorTypeDetail()));
        hiddenDanger.setPossibleConsequence(hiddenDangerIn.getPossibleConsequence());
        hiddenDanger.setStatus(HiddenDangerStatus.待改善.name());
        hiddenDanger.setIsWrongSubmit(false);
        hiddenDanger.setArrangedAuditor(project.getTechAssistant());
        hiddenDanger.setArrangedAuditorId(project.getTechAssistantId());
        hiddenDanger.setAuditStatus(AuditStatus.UnReleased.name());
        hiddenDanger.setSuggestionList(hiddenDangerIn.getSuggestionList());

        OperationLogs operationLogs = new OperationLogs();
        operationLogs.setId(SnGeneratorUtil.getId());
        operationLogs.setCreateTime(new Date());
        operationLogs.setCorporationId(hiddenDangerIn.getCorporationId());
        operationLogs.setHiddenDangerId(hiddenDangerId);
        operationLogs.setOperationType(OperationType.submit.name());
        operationLogs.setInChargeName(adminUser.getRealName());
        operationLogs.setOperatorId(adminUser.getId());
        operationLogs.setOperator(adminUser.getRealName());

        hiddenDangerService.submitHiddenDanger(hiddenDanger, operationLogs);
        return CommonResult.successData(hiddenDangerId);
    }

    @RequiredPermission("update:doEdit:hiddenDanger")
    @ApiOperation(value = "修改隐患详情")
    @PostMapping(value = "/doEdit")
    public CommonResult doEdit(@RequestBody HiddenDangerEditIn in){

        hiddenDangerService.doEdit(in);
        return CommonResult.successResult("修改成功");
    }

    @RequiredPermission("update:setWrongSubmit:hiddenDanger")
    @ApiOperation(value = "设置是否错误上报")
    @PostMapping(value = "/setWrongSubmit")
    public CommonResult setWrongSubmit(@RequestBody WrongSubmitIn wrongSubmitIn){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(wrongSubmitIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        HiddenDanger hiddenDanger = hiddenDangerService.getById(wrongSubmitIn.getHiddenDangerId());
        if (hiddenDanger==null){
            throw new DataErrorException("未找到该隐患");
        }

        hiddenDanger.setIsWrongSubmit(wrongSubmitIn.getIsWrongSubmit());

        hiddenDangerService.updateHiddenDanger(hiddenDanger);

        return CommonResult.successResult("设置成功");
    }

    @RequiredPermission("update:setFindByCallback:hiddenDanger")
    @ApiOperation(value = "设置是否回访时发现")
    @PostMapping(value = "/setFindByCallback")
    public CommonResult setFindByCallback(@RequestBody FindByCallbackIn findByCallbackIn){

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(findByCallbackIn)){
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        HiddenDanger hiddenDanger = hiddenDangerService.getById(findByCallbackIn.getHiddenDangerId());
        if (hiddenDanger==null){
            throw new DataErrorException("未找到该隐患");
        }

        hiddenDanger.setIsFindByCallback(findByCallbackIn.getIsFindByCallback());

        hiddenDangerService.updateHiddenDanger(hiddenDanger);

        return CommonResult.successResult("设置成功");
    }

    @RequiredPermission("update:doEditVerification:hiddenDanger")
    @ApiOperation(value = "修改隐患验证信息")
    @PostMapping(value = "/doEditVerification/{verificationId}")
    public CommonResult doEditVerification(@PathVariable("verificationId") Long verificationId,
                               @RequestBody VerificationEditIn verificationEditIn){

        Verification verification = verificationService.getById(verificationId);
        if (verification==null){
            throw new DataErrorException("未找到该隐患验证信息");
        }
        if (verificationEditIn.getPictures()!=null){
            verification.setPictures(JSON.toJSONString(verificationEditIn.getPictures()));
        }
        if (verificationEditIn.getReason()!=null){
            verification.setReason(verificationEditIn.getReason());
        }

        verificationService.updateById(verification);

        return CommonResult.successResult("修改成功");
    }

    /**
     * 单次导入项目和公司是固定的
     */
    @ApiOperation(value = "批量导入隐患")
    @PostMapping(value = "/doSaveList")
    public CommonResult doSaveList(@RequestBody HiddenDangerListIn hiddenDangerListIn){
        hiddenDangerService.doSaveList(hiddenDangerListIn);
        return CommonResult.successResult("保存成功");
    }
    @ApiOperation(value = "智能推荐检查依据")
    @GetMapping(value = "/smartRecommendStandby")
    public CommonResult<List<Object>> smartRecommendStandby(String description){
        return CommonResult.successData(hiddenDangerService.smartRecommendStandby(description));
    }

    @ApiOperation(value = "通过描述推荐隐患")
    @GetMapping(value = "/recommendHdByDesc")
    public CommonResult<JSONObject> recommendHdByDesc(String description){
        return CommonResult.successData(hiddenDangerService.recommendHdByDesc(description));
    }

    @ApiOperation(value = "删除隐患")
    @GetMapping(value = "/delete/{id}")
    public CommonResult<Boolean> delete(@PathVariable("id") Long id){
        return CommonResult.successData(hiddenDangerService.delete(id));
    }

    @ApiOperation(value = "企业安全监测列表")
    @GetMapping(value = "/corpSafeList")
    public com.yixun.bean.CommonResult<List<CorpSafeListOut>> corpSafeList(@Valid CorpSafeListIn in){
        return hiddenDangerService.corpSafeList(in);
    }

    @ApiOperation(value = "导出企业安全监测列表")
    @GetMapping(value = "/exportCorpSafeList")
    public void exportCorpSafeList(@Valid CorpSafeListIn in){
        hiddenDangerService.exportCorpSafeList(in);
    }

}
