package com.yixun.hdm.interceptor;

import com.alibaba.fastjson.JSON;
import com.yixun.hdm.bean.common.CommonResult;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ApiInterceptor - Api调用权限
 */
@Component
public class ApiInterceptor extends HandlerInterceptorAdapter {

	/** "token"属性名称 */
	private static final String TOKEN_NAME = "ApiToken";

	@Override
	public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {

		String tokenValue = request.getHeader(TOKEN_NAME);
		if (tokenValue== null || tokenValue.isEmpty()){
			return false;
		}

		if (tokenValue.equals("C9F1B71774E4899B9ECFC735A39A8659")){
			return true;
		}
//        try {
//			String userName = Jwts.parser().setSigningKey("QXBpVG9rZW5TZWNyZXQ=").parseClaimsJws(tokenValue).getBody().getSubject();
//		}catch (Exception e){
//            response.getWriter().write(e.getMessage());
//			return false;
//		}

		response.setContentType("text/html;charset=utf-8");
		response.getWriter()
				.write(JSON.toJSONString(CommonResult.failResult(401, "token不正确")));
        return false;
    }

	@Override
	public void postHandle(HttpServletRequest request,
                           HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {
	}

}