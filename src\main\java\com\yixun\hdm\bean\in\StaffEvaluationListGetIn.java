package com.yixun.hdm.bean.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class StaffEvaluationListGetIn {

    private String surveyManager;
    @ApiModelProperty(value="格式 yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date surveyMonth;
    private String auditor;
    @ApiModelProperty(value="格式 yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date reportMonth;
    private Boolean isSubmitExpired;
    private Boolean isSubmitCountOK;
    private Boolean isAuditExpired;
    private Boolean isReleaseExpired;

}
