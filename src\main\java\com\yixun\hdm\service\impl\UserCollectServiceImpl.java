package com.yixun.hdm.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.dao.HiddenDangerMapper;
import com.yixun.hdm.dao.UserCollectMapper;
import com.yixun.hdm.entity.UserCollect;
import com.yixun.hdm.entity.em.CollectContentType;
import com.yixun.hdm.service.UserCollectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户收藏表(UserCollect)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-19 16:10:45
 */
@Service("userCollectService")
public class UserCollectServiceImpl extends ServiceImpl<UserCollectMapper, UserCollect> implements UserCollectService {


    @Autowired
    private HiddenDangerMapper hiddenDangerMapper;

    @Override
    public Set<Long> getUserCollect(Long userId, CollectContentType type) {
        LambdaQueryWrapper<UserCollect> wrapper = new LambdaQueryWrapper<UserCollect>();
        wrapper.eq(UserCollect::getUserId, userId);
        wrapper.eq(UserCollect::getContentType,type.name());
        List<UserCollect> list = list(wrapper);
        return list.stream().map(UserCollect::getContentId).collect(Collectors.toSet());
    }

    @Override
    public Set<Long> idSet(Long userId, Integer contentType, Integer range) {
        Set<Long> idSet = new HashSet<>();
        if(range == 1 || range == 0){
            //我的收藏
            LambdaQueryWrapper<UserCollect> wrapper = new LambdaQueryWrapper<UserCollect>();
            wrapper.eq(UserCollect::getUserId, userId);
            wrapper.eq(UserCollect::getContentType,contentType);
            List<UserCollect> list = list(wrapper);
            Set<Long> collect = list.stream().map(UserCollect::getContentId).collect(Collectors.toSet());
            idSet.addAll(collect);
        }
        if(range == 2 || range == 0){
            //经常使用
            if(contentType == 1){
                idSet.addAll(frequentlyUseCheckContent(userId));
            } else if (contentType == 2) {
                idSet.addAll(frequentlyUseRegulation(userId));
            }
        }
        return idSet;
    }

    /**
     * 获取最常使用的检查内容 30条
     * @param userId
     * @return
     */
    private Set<Long> frequentlyUseCheckContent(Long userId){
        Set<String> checkContent = hiddenDangerMapper.frequentlyUseStandard(userId, "checkContent");
        return convertStringSetToLongSet(checkContent);
    }

    /**
     * 获取最常使用的法律法规 30条
     * @param userId
     * @return
     */
    private Set<Long> frequentlyUseRegulation(Long userId){
        Set<String> regulation = hiddenDangerMapper.frequentlyUseStandard(userId, "regulation");
        return convertStringSetToLongSet(regulation);
    }

    private Set<Long> convertStringSetToLongSet(Set<String> stringSet) {
        if (stringSet == null) {
            return new HashSet<>();
        }

        Set<Long> longSet = new HashSet<>(stringSet.size());
        for (String str : stringSet) {
            try {
                longSet.add(Long.parseLong(str));
            } catch (NumberFormatException e) {
                log.error("该id数据不能转换成long类型：" + str);
            }
        }
        return longSet;
    }

}

