package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.HdAuditListGetIn;
import com.yixun.hdm.bean.out.HdAuditOut;
import com.yixun.hdm.entity.HdAudit;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.dto.StaffUserDto;

import java.util.Date;
import java.util.List;

/**
 *
 */
public interface HdAuditService extends IService<HdAudit> {

    void insert(List<HiddenDanger> hiddenDangerList, StaffUserDto adminUser);

    void checkAuditSubmitter(Long corporationId, Long userId);

    Page<HdAudit> getAuditPage(HdAuditListGetIn hdAuditListGetIn, CommonPage page);

    void setAudit(HdAudit hdAudit, Long hiddenDangerId, StaffUser<PERSON><PERSON> adminUser, StaffUserDto submitter);

    Long getAuditSubmitter(Long corporationId);

    List<HdAudit> getExpiredAudit(Date date);

    void submitAudit(List<Long> hdIdList);

    void checkHdCompletion(List<HiddenDanger> hiddenDangerList);
}
