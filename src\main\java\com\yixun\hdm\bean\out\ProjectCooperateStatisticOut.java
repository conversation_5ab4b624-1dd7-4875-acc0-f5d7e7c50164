package com.yixun.hdm.bean.out;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ProjectCooperateStatisticOut {

    @ApiModelProperty("企业数量")
    private Long corpCount;
    @ApiModelProperty("隐患数量")
    private Integer hdCount;
    @ApiModelProperty("回访隐患数量")
    private Long callbackCount;
    @ApiModelProperty("隐患分级")
    private Map levelMap;
    @ApiModelProperty("项目隐患改善情况")
    private Map statusMap;
    @ApiModelProperty("隐患类别统计")
    private Map typeMap;
    @ApiModelProperty("危害因素分类统计")
    private List<RiskFactorType> riskFactorTypeList;

    @Data
    public static class RiskFactorType {
        private String type;
        private Integer count;
        private Map riskFactorTypeMap;
    }
}
