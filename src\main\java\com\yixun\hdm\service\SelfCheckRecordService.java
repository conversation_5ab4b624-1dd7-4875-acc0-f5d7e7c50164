package com.yixun.hdm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yixun.hdm.entity.SelfCheckRecord;

import java.util.Date;

/**
 * 自查记录(SelfCheckRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-26 17:09:51
 */
public interface SelfCheckRecordService extends IService<SelfCheckRecord> {

    SelfCheckRecord getRecord(Long projectId, Long corpId, Date date);

    boolean createRecord(Long projectId,String projectName,Long corpId,String corpName,Date date,boolean isForce);

    Page<SelfCheckRecord> pageList(Page<SelfCheckRecord> page, SelfCheckRecord selfCheckRecord);

    Boolean delete(Long id);

    void export(SelfCheckRecord selfCheckRecord);
}

