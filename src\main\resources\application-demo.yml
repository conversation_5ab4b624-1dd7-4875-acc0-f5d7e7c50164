server:
  port: 9075
  servlet:
    context-path: /hdanger-admin-api
spring:
  application:
    name: hdm-admin-service
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *********************************************************************************************************************************
          username: yqycowner
          password: yQyad1n*0802v.D
        opt_logs:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ****************************************************************************************************************************
          username: yqycowner
          password: yQyad1n*0802v.D
  redis:
    database: 0
    host: ***************
    password: yiXn#9alm(rd2S
    port: 6379
    timeout: 5000
jwt:
  secret:
    account: ****************************************
  expiration: 604800    #单位秒，值为7天‬
aliyun:
  endpoint: sts.cn-hangzhou.aliyuncs.com
  bucket: yxcloud-app-pro
weixin:
  appId: wxb92d994208d236cd
  appSecret: 5612c7a7b5f4c8449c8087854b04f5a4
api:
  token: C9F1B71774E4899B9ECFC735A39A8659
  yqycInternal: https://app-api-demo.hyaitech.com:8845/yqyc-internal-api
  yqycApi: https://app-api-demo.hyaitech.com:8845/yqyc-api
  yqyc-api:
    url: https://app-api-demo.hyaitech.com:8845/yqyc-api
  hdangerApi: https://app-api-demo.hyaitech.com:8845/hdanger-api
  messageCenterApi: https://app-api-demo.hyaitech.com:8845/message-center-api
  examManageApi: https://app-api-demo.hyaitech.com:8845/exam-manage-api
  integralApi: https://app-api-demo.hyaitech.com:8845/integral-api
  securityTankApi: https://app-api-demo.hyaitech.com:8845/security-tank-api
  regulationSearchApi: https://app-api-demo.hyaitech.com:8845/regulation-search-api
  userCenterApi: https://app-api-demo.hyaitech.com:8845/usercenter-api
  aiBaseUrl: http://aiapp.yxgsyf.com:19080
  aiPlatformApi: http://aiapp.yxgsyf.com:19080/dynamic-precision/hd_api/
  recommendStandardUrl: http://aiapp.yxgsyf.com:19080/check_option_matcher
  aiYqyc: http://aiapp.yxgsyf.com:19080/yiqiyice/
  teacher-resource: https://app-api-demo.hyaitech.com:8845/teacher-admin-api
  user-center-api:
    name: user-center-api
    url: https://app-api-demo.hyaitech.com:8845/usercenter-api
xxl:
  job:
    admin:
      addresses: http://***********:10099/xxl-job-admin
    accessToken: AwJ&87sRi!iIRr1M
    executor:
      appname: hdm-admin-service
      address:
      ip: ***********
      port: 1001
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30     #执行器Log文件定期清理功能，指定日志保存天数，日志文件过期自动删除。限制至少保持3天，否则功能不生效；