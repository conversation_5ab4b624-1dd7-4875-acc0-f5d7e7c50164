package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName hdm_staff_evaluation
 */
@TableName(value ="hdm_staff_evaluation")
@Data
public class StaffEvaluation implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 
     */
    private Date createTime;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 公司id
     */
    private Long corporationId;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 调研老师
     */
    private String surveyManager;

    /**
     * 调研日期
     */
    private Date surveyDate;

    /**
     * 提交审查时间
     */
    private Date submitTime;

    /**
     * 提交截止期限
     */
    private Date submitExpiredTime;

    /**
     * 第一次提交隐患数
     */
    private Integer firstSubmitCount;

    /**
     * 审查员
     */
    private String auditor;

    /**
     * 审查期限
     */
    private Date auditExpiredTime;

    /**
     * 审查完成时间
     */
    private Date auditFinishTime;

    /**
     * 报告发布时间
     */
    private Date releaseTime;

    /**
     * 发布截止期限
     */
    private Date releaseExpiredTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}