package com.yixun.hdm.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.convert.ListTypeHandler;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 隐患详情表
 * @TableName hdm_hidden_danger
 */
@SuppressWarnings("serial")
@Data
@ApiModel("隐患详情表")
@TableName(value = "hdm_hidden_danger",autoResultMap = true)
public class HiddenDanger  {

    @NotNull(message="[主键ID]不能为空")
    @JsonSerialize(using = LongJsonSerializer.class)
    @TableId
    private Long id;

    @ApiModelProperty("创建时间")
    @NotNull(message="[创建时间]不能为空")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date updateTime;

    @ApiModelProperty("操作人员id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long operatorId;

    @ApiModelProperty("操作人员")
    private String operator;

    @ApiModelProperty("项目id")
    @NotNull(message="[项目id]不能为空")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("公司id")
    @NotNull(message="[公司id]不能为空")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;

    @ApiModelProperty("公司名称")
    private String corpName;

    @ApiModelProperty("调研轮次")
    private Integer turn;

    @ApiModelProperty("任务id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long taskId;

    @ApiModelProperty("任务类型 1-调研 2-辅助调查培训 3-回访 4-联合调研")
    private Integer taskType;

    @ApiModelProperty("任务时间")
    private Date taskWorkDate;

    @ApiModelProperty("隐患状态")
    @NotNull(message="[隐患状态]不能为空")
    private String status;

    @ApiModelProperty("隐患照片（可多图）")
    @NotNull(message="[隐患照片（可多图）]不能为空")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> pictures;

    @ApiModelProperty("动图（可多图）")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> gifPictures;

    @ApiModelProperty("隐患质量分")
    private Integer hdQualityScore;

    @ApiModelProperty("岗位/工种")
    private String workCategory;

    @ApiModelProperty("作业活动")
    private String jobActivity;

    @ApiModelProperty("设备设施/物料")
    private String equipment;

    @ApiModelProperty("隐患区域")
    private String hdRegion;

    @ApiModelProperty("隐患描述")
    @NotNull(message="[隐患描述]不能为空")
    private String description;

    @ApiModelProperty("危害因素分类")
    @NotNull(message="[危害因素分类]不能为空")
    private String riskFactorType;

    @ApiModelProperty("危害因素分类明细")
    private String riskFactorTypeDetail;

    @ApiModelProperty("危害因素分类详情-人机料法环")
    private String riskFactorTypeDetailAlias;

    @ApiModelProperty("专项类别")
    private String specialCategory;

    @ApiModelProperty("专项类别明细")
    private String specialCategoryDetail;

    @ApiModelProperty("所属风险点id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long riskPointId;

    @ApiModelProperty("所属风险点")
    private String riskPointName;

    @ApiModelProperty("隐患门类")
    @NotNull(message="[隐患门类]不能为空")
    private String type;

    @ApiModelProperty("归属类别；1：制度与操作规程；2：教育培训；3：设备管理；4：物料管理；5：作业环境管理；6：事故四不放过管理；7：应急管理；8：职业健康管理")
    private Integer belongCategory;

    @ApiModelProperty("隐患级别")
    @NotNull(message="[隐患级别]不能为空")
    private String level;

    @ApiModelProperty("临时应急处置")
    private String tempEmergency;

    @ApiModelProperty("一级业务")
    private String tierOneBusiness;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("文件（可多个）")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> files;

    @ApiModelProperty("依据标准")
    @NotNull(message="[依据标准]不能为空")
    private String standard;

    @ApiModelProperty("可能导致后果")
    @NotNull(message="[可能导致后果]不能为空")
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> possibleConsequence;

    @ApiModelProperty("风险描述")
    private String riskDescription;

    @ApiModelProperty("管理追溯")
    private String manageSource;

    @ApiModelProperty("管理追溯内容")
    private String manageSourceContent;

    @ApiModelProperty("建议整改措施")
    private String rectificationSuggestion;

    @ApiModelProperty("整改期限（天）")
    private Integer rectifyDeadline;

    @ApiModelProperty("持表检查项目id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long checkOptionId;

    @ApiModelProperty("是否回访时发现")
    private Boolean isFindByCallback;

    @ApiModelProperty("是否为错误上报")
    private Boolean isWrongSubmit;

    @ApiModelProperty("计划安排的审查员")
    private String arrangedAuditor;

    @ApiModelProperty("计划安排的审查员id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long arrangedAuditorId;

    @ApiModelProperty("审查员")
    private String auditor;

    @ApiModelProperty("审查员id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long auditorId;

    @ApiModelProperty("审核状态")
    private String auditStatus;

    @ApiModelProperty("是否需老师修改")
    private Boolean isNeedEdit;

    @ApiModelProperty("审查意见")
    private String auditComment;

    @ApiModelProperty("审查时间")
    private Date auditTime;

    @TableField(exist = false)
    @ApiModelProperty("建议分项录入列表")
    private List<HiddenDangerSuggestion> suggestionList = new ArrayList<>();

    @TableField(exist = false)
    @ApiModelProperty("是否已工作移交")
    private Boolean isWorkHandover;

}