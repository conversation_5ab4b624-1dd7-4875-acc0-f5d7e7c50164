package com.yixun.hdm.utils;

import com.yixun.hdm.entity.HiddenDanger;
import jodd.util.StringUtil;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

public class HdQualityScoreUtils {

    public static Integer calculateScore(HiddenDanger hiddenDanger) {
        if (hiddenDanger == null) {
            return 0;
        }
        int score = 0;
        String nophoto = "https://yxcloud-app.yxgsyf.com/static/img/hdanger/nophoto.jpg";
        if (hiddenDanger.getPictures().contains(nophoto)) {
            score -= 1;
        }
        if (hiddenDanger.getGifPictures() != null && !hiddenDanger.getGifPictures().isEmpty()) {
            score += 1;
        }
        String riskFactorType = hiddenDanger.getRiskFactorType();
        String riskFactorTypeDetail = hiddenDanger.getRiskFactorTypeDetail();
        if ("环境因素".equals(riskFactorType)
                || "管理因素".equals(riskFactorType)
                || riskFactorTypeDetail.contains("标志标识缺陷")
                || riskFactorTypeDetail.contains("信息系统缺陷")
        ) {
            score -= 1;
        }
        List<String> possibleConsequence = hiddenDanger.getPossibleConsequence();
        if (possibleConsequence.contains("合规风险")
                || possibleConsequence.contains("火灾")
                || possibleConsequence.contains("其他")
        ) {
            score -= 1;
        }
        if ("重大隐患".equals(hiddenDanger.getLevel())) {
            String standard = hiddenDanger.getStandard();
            if (standard.contains("\"from\": \"regulation\"")
                    || standard.contains("\"from\": \"custom\"")) {
                score -= 1;
            }
            //<3项，-1分；
            //=5项，+1分；
            int size = hiddenDanger.getSuggestionList().size();
            if(size == 5){
                score += 1;
            } else if (size < 3) {
                score -= 1;
            }
        }
        //设备设施、作业活动字段都有值，+1分
        if(StringUtil.isNotEmpty(hiddenDanger.getEquipment()) && StringUtil.isNotEmpty(hiddenDanger.getJobActivity())){
            score += 1;
        }
        return score;
    }

    public static Double calcCorpScore(List<HiddenDanger> list) {
        if (list.isEmpty()) {
            return 0D;
        }
        double score = list.stream().mapToInt(HiddenDanger::getHdQualityScore).average().orElse(0D);
        long count = list.stream().filter(hiddenDanger -> hiddenDanger.getStandard().contains("\"from\": \"checkContent\"")).count();
        if ((double) count / list.size() < 0.7) {
            score -= 1;
        }
        long num = list.stream().filter(hiddenDanger -> hiddenDanger.getDescription().matches("(消防|灭火器|消火栓|防火|应急照明|安全出口|疏散)")).count();
        if (num > 15) {
            score -= 3;
        } else if (num > 10) {
            score -= 2;
        } else if (num > 4) {
            score -= 1;
        }
        //TODO 必查项查报率
        //TODO 隐患工伤关联率
        //调研隐患数量
        int size = list.size();
        if(size >=50){
            score += 3;
        } else if (size >= 40) {
            score += 2;
        } else if (size >= 30) {
            score += 1;
        } else if (size < 10) {
            score -= 3;
        } else if (size < 15 ) {
            score -= 2;
        } else if (size < 25) {
            score -= 1;
        }
        //回访发现隐患数量
        long callbackCount = list.stream().filter(hiddenDanger -> hiddenDanger.getTaskType() == 3).count();
        if(callbackCount > 10){
            score += 1;
        } else if (callbackCount < 10) {
            score -= 1;
        }
        return score;
    }

    public static Integer calcEffectiveHdNum(List<HiddenDanger> list){
        // 按 createDate 倒序排序
        list.sort(Comparator.comparing(HiddenDanger::getCreateTime).reversed());

        // 初始化结果列表
        List<HiddenDanger> result = new ArrayList<>();

        // 遍历每一条记录，移除重复的隐患
        for (int i = 0; i < list.size(); i++) {
            HiddenDanger current = list.get(i);
            boolean isDuplicate = false;
            for (int j = 0; j < result.size(); j++) {
                HiddenDanger compared = result.get(j);
                if (Objects.equals(current.getEquipment(), compared.getEquipment())
                        || Objects.equals(current.getJobActivity(), compared.getJobActivity())) {
                    isDuplicate = true;
                    break;
                }
            }
            if (!isDuplicate) {
                result.add(current);
            }
        }
        return result.size();
    }

    /**
     * 计算隐患列表评价得分
     * @param list
     * @return
     */
    public static Integer calcEvaluateHdScore(List<HiddenDanger> list){
        // 一般隐患 扣 1 分 , 重大隐患扣10分
        int score = 0;
        for (HiddenDanger hiddenDanger : list) {
            if ("一般隐患".equals(hiddenDanger.getLevel())) {
                score -= 1;
            }else if ("重大隐患".equals(hiddenDanger.getLevel())){
                score -= 10;
            }
        }
        return score;
    }

}
