package com.yixun.hdm.service.impl;

import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.yixun.hdm.service.ShortMsgService;
import com.yixun.hdm.utils.AliyunSms;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@SuppressWarnings("all")
@Slf4j
@Service
public class ShortMsgServiceImpl implements ShortMsgService {

	@Value("${spring.profiles.active}")
	private String env;

    //阿里发短信
	@Override
	public void sendSMS(Map<String, String> map) {
		try {
			log.info("开始发短信:" + map);
			if (!env.equals("product"))
				return;
			SendSmsResponse response = AliyunSms.sendSms(map);
			log.info("短信回执:" + map.get("phone") + ", "+ response.getCode() + ", " + response.getMessage());
			if (!response.getCode().equals("OK")){
                throw new RuntimeException("短信发送失败，请稍后再试");
            }
		} catch (Exception e) {
			log.error("短信发送失败" + e.getMessage());
			throw new RuntimeException("短信发送失败");
		}
	}

}
