package com.yixun.hdm.bean.out;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CheckResultOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    @ApiModelProperty("检查时间")
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;

    @ApiModelProperty("任务id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long taskId;

    @ApiModelProperty("项目id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("公司id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;

    @ApiModelProperty("公司名称")
    private String corpName;

    @ApiModelProperty("检查内容")
    private String checkContent;

    @ApiModelProperty(value = "检查项目名称")
    private String checkProjectName;

    @ApiModelProperty("检查结果 0合格 1隐患 2不涉及")
    private Integer result;

    @ApiModelProperty("隐患id")
    private Long hiddenDangerId;

    @ApiModelProperty("合格时的图片")
    private List picture;

    @ApiModelProperty("执行负责人")
    private String executor;

    @ApiModelProperty("陪同人员")
    private String accompany;

    /**
     * 推荐检查的隐患Id
     */
    private String recommendHdId;

    /**
     * 推荐检查的隐患描述
     */
    private String recommendHdDesc;

    @ApiModelProperty("是否提交")
    private Boolean isSubmit;

    public void setPicture(String picture) {
        this.picture = JSON.parseObject(picture, List.class);
    }
}
