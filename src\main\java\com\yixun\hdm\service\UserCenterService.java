package com.yixun.hdm.service;

import com.yixun.hdm.entity.dto.*;
import com.yixun.hdm.entity.em.DictType;

import java.util.List;

public interface UserCenterService {

    StaffUserDto getAdminUser(Long adminUserId);

    CorporationDto getCorporation(Long corporationId);

    ProjectDto getProject(Long projectId);

    Boolean checkPermissions(String required);

    List<CorporationProjectDto> getCorporationList(Long projectId);

    Integer getCorporationCount(Long projectId);

    OccupInjuryStatisticsDto getOccupInjuryStatistics(Long projectId);

    CorporationProjectDto getCorporationProjectById(Long corporationProjectId);

    List<YqycInfoDto> getYqycInfoList(List<CorporationProjectDto> corpProjList);

    CorporationProjectDto getCorporationProject(Long corporationId, Long projectId);

    List<DataDict> getDataDictionary(DictType type);
}
