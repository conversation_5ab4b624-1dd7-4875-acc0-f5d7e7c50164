package com.yixun.hdm.bean.out;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class HdmPostLogsOut {

    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date optTime;
    private String operator;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long operatorId;
    private Map args;

    public void setArgs(String args) {
        List list = JSON.parseObject(args, List.class);
        if (list.size()==2){
            this.args = (Map) list.get(1);
        }else {
            this.args = new HashMap();
        }
    }
}
