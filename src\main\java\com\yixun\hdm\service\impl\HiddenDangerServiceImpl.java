package com.yixun.hdm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yixun.api.CorporationProjectApi;
import com.yixun.api.WorkInjuryApi;
import com.yixun.bean.CommonResult;
import com.yixun.bean.entity.Corporation;
import com.yixun.bean.in.InjuryStatisticsIn;
import com.yixun.bean.out.InjuryStatisticsOut;
import com.yixun.hd.bean.in.HdReleaseIn;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.ErrorMessage;
import com.yixun.hdm.bean.in.*;
import com.yixun.hdm.bean.out.CorpSafeListOut;
import com.yixun.hdm.bean.out.HdStatisticOut;
import com.yixun.hdm.bean.out.ProjectCooperateStatisticOut;
import com.yixun.hdm.dao.HiddenDangerMapper;
import com.yixun.hdm.entity.*;
import com.yixun.hdm.entity.dto.DataDict;
import com.yixun.hdm.entity.dto.ProjectDto;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.entity.em.AuditStatus;
import com.yixun.hdm.entity.em.DictType;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import com.yixun.hdm.entity.em.OperationType;
import com.yixun.hdm.entity.vo.SelfCheckQueryParam;
import com.yixun.hdm.entity.vo.SelfCheckResult;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.exception.ParameterErrorException;
import com.yixun.hdm.service.*;
import com.yixun.hdm.service.biz.AiService;
import com.yixun.hdm.utils.*;
import com.yixun.teacher.api.TaskApi;
import com.yixun.teacher.bean.enums.TaskTypeEnum;
import com.yixun.teacher.bean.in.ApiTaskGetIn;
import com.yixun.teacher.bean.in.TaskCorpListIn;
import com.yixun.teacher.bean.out.TaskCorpListOut;
import com.yixun.teacher.bean.out.TaskOut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 隐患主服务
 */
@Slf4j
@Service
public class HiddenDangerServiceImpl extends ServiceImpl<HiddenDangerMapper, HiddenDanger>
        implements HiddenDangerService {

    @Value("${api.yqycApi}")
    private String yqycApi;

    @Value("${api.token}")
    private String token;

    @Autowired
    private TaskApi taskApi;

    @Resource
    private OperationLogsService operationLogsService;

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private MessageService messageService;

    @Resource
    private RegulationsMemoService regulationsMemoService;

    @Autowired
    private HiddenDangerSuggestionService hiddenDangerSuggestionService;

    @Autowired
    private SecurityTankApiService securityTankApiService;

    private Set<String> possibleConsequenceDictCache;

    @Autowired
    private AiService aiService;

    @Autowired
    private CheckResultService checkResultService;

    @Autowired
    private HdAuditLogService hdAuditLogService;

    @Autowired
    private CorporationProjectApi corporationProjectApi;

    @Autowired
    private WorkInjuryApi workInjuryApi;

    @Override
    public PageInfo<HiddenDanger> getHiddenDangerPage(HiddenDangerListGetIn hiddenDangerListGetIn) {
        if (hiddenDangerListGetIn.getStartDate() != null && hiddenDangerListGetIn.getEndDate() != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(hiddenDangerListGetIn.getEndDate());
            c.add(Calendar.SECOND, 86399); //结束时间加到当天的23:59:59
            hiddenDangerListGetIn.setEndDate(c.getTime());
        }
        if (hiddenDangerListGetIn.getRiskFactorTypeDetail() != null) {
            String jsonString = JSON.toJSONString(hiddenDangerListGetIn.getRiskFactorTypeDetail());
            JSONArray array = JSONArray.parseArray(jsonString);
            String string = array.getJSONObject(array.size() - 1).getString("value");
            hiddenDangerListGetIn.setRiskFactorTypeDetail(string);
        }
        if (hiddenDangerListGetIn.getRiskFactorTypeDetailAlias() != null) {
            String jsonString = JSON.toJSONString(hiddenDangerListGetIn.getRiskFactorTypeDetailAlias());
            JSONArray array = JSONArray.parseArray(jsonString);
            String string = array.getJSONObject(array.size() - 1).getString("value");
            hiddenDangerListGetIn.setRiskFactorTypeDetailAlias(string);
        }
        com.github.pagehelper.Page<HiddenDanger> objects = PageHelper.startPage((int) hiddenDangerListGetIn.getPageNum(), (int) hiddenDangerListGetIn.getPageSize());
        getBaseMapper().getHiddenDangerPage(hiddenDangerListGetIn);
        fillWorkHandover(objects.getResult());
        return objects.toPageInfo();
    }

    private void fillWorkHandover(List<HiddenDanger> result) {
        if(result == null || result.isEmpty()){
            return;
        }
        List<Long> taskIdList = result.stream().map(HiddenDanger::getTaskId).collect(Collectors.toList());
        ApiTaskGetIn taskGetIn = new ApiTaskGetIn();
        taskGetIn.setIds(taskIdList);
        List<TaskOut> taskList = taskApi.getTaskList(taskGetIn);
        Map<Long, Long> map = taskList.stream().filter(taskOut -> taskOut.getWorkReceiverId() != null).collect(Collectors.toMap(TaskOut::getId, TaskOut::getWorkReceiverId));
        for (HiddenDanger hiddenDanger : result) {
            hiddenDanger.setIsWorkHandover(map.get(hiddenDanger.getTaskId()) != null);
        }
    }

    @Override
    public List<HiddenDanger> getHiddenDangerList(HiddenDangerListApiGetIn hiddenDangerListApiIn) {

        List<HiddenDanger> list = lambdaQuery().eq(HiddenDanger::getIsWrongSubmit, false)
                .eq(hiddenDangerListApiIn.getCorporationId() != null, HiddenDanger::getCorporationId, hiddenDangerListApiIn.getCorporationId())
                .eq(hiddenDangerListApiIn.getProjectId() != null, HiddenDanger::getProjectId, hiddenDangerListApiIn.getProjectId())
                .eq(hiddenDangerListApiIn.getTurn() != null, HiddenDanger::getTurn, hiddenDangerListApiIn.getTurn())
                .eq(hiddenDangerListApiIn.getAuditStatus() != null, HiddenDanger::getAuditStatus, hiddenDangerListApiIn.getAuditStatus())
                .eq(hiddenDangerListApiIn.getHiddenDangerStatus() != null, HiddenDanger::getStatus, hiddenDangerListApiIn.getHiddenDangerStatus())
                .eq(hiddenDangerListApiIn.getType() != null, HiddenDanger::getType, hiddenDangerListApiIn.getType())
                .in(hiddenDangerListApiIn.getIdList() != null && !hiddenDangerListApiIn.getIdList().isEmpty(), HiddenDanger::getId, hiddenDangerListApiIn.getIdList())
                .in(hiddenDangerListApiIn.getTaskIds() != null && !hiddenDangerListApiIn.getTaskIds().isEmpty(), HiddenDanger::getTaskId, hiddenDangerListApiIn.getTaskIds())
                .list();
        if (hiddenDangerListApiIn.getEliminateRecommendHd() && !list.isEmpty()) {
            List<Long> collect = list.stream().filter(item -> item.getTaskType() == 3).map(HiddenDanger::getId).collect(Collectors.toList());
            if (collect.isEmpty()) {
                return list;
            }
            List<Long> checkHdIds = checkResultService.lambdaQuery().in(CheckResult::getHiddenDangerId, collect).list().stream().map(CheckResult::getHiddenDangerId).collect(Collectors.toList());
            list.removeIf(item -> checkHdIds.contains(item.getId()));
        }
        return list;
    }

    @Override
    public void submitHiddenDanger(HiddenDanger hiddenDanger, OperationLogs operationLogs) {
        checkHiddenDangerContent(hiddenDanger);
        JSONObject object = convertRiskFactorTypeDetail(Collections.singletonList(hiddenDanger));
        hiddenDanger.setRiskFactorTypeDetailAlias(object.getString(hiddenDanger.getId() + ""));
        handleRegulationsMemo(hiddenDanger);
        save(hiddenDanger);
        hiddenDanger.getSuggestionList().forEach(suggestion -> {
            suggestion.setHdId(hiddenDanger.getId());
            suggestion.setCreateTime(new Date());
            hiddenDangerSuggestionService.save(suggestion);
        });
        operationLogsService.save(operationLogs);

        updateHdLedgerStatus(hiddenDanger.getProjectId(), hiddenDanger.getCorporationId(), hiddenDanger.getTurn(),
                hiddenDanger.getOperator());
    }

    @Override
    public void handleRegulationsMemo(HiddenDanger hiddenDanger) {
        List<Regulations> regulationsList = JSON.parseArray(hiddenDanger.getStandard(), Regulations.class);
        List<Regulations> customList = regulationsList.stream()
                .filter(r -> r.getFrom() != null && r.getFrom().equals("custom")).collect(Collectors.toList());
        for (Regulations regulations : customList) {
            RegulationsMemo regulationsMemo = new RegulationsMemo();
            BeanUtils.copyProperties(regulations, regulationsMemo);
            regulationsMemo.setInnerStandardId(regulations.getId());
            regulationsMemo.setCreateTime(new Date());
            regulationsMemo.setIsUsed(false);
            regulationsMemo.setSourceId(hiddenDanger.getId());
            regulationsMemo.setId(SnGeneratorUtil.getId());
            regulationsMemo.setCreator(hiddenDanger.getOperator());

            regulationsMemoService.save(regulationsMemo);
        }
        //意义不明，注释掉
//        for (Regulations regulations : regulationsList){
//            if (regulations.getFrom()!=null && regulations.getFrom().equals("custom")){
//                regulations.setFrom("regulation");
//            }
//        }
        hiddenDanger.setStandard(JSON.toJSONString(regulationsList));
    }

    @Override
    public List<HiddenDanger> getHiddenDangerAllList(HiddenDangerListGetIn hiddenDangerListGetIn) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        if (hiddenDangerListGetIn.getIds() != null) {
            queryWrapper.in("id", hiddenDangerListGetIn.getIds());
        }
        if (hiddenDangerListGetIn.getProjectId() != null) {
            queryWrapper.eq("project_id", hiddenDangerListGetIn.getProjectId());
        }
        if (hiddenDangerListGetIn.getCorporationId() != null) {
            queryWrapper.eq("corporation_id", hiddenDangerListGetIn.getCorporationId());
        }
        if (hiddenDangerListGetIn.getTurn() != null) {
            queryWrapper.eq("turn", hiddenDangerListGetIn.getTurn());
        }
        if (hiddenDangerListGetIn.getStartDate() != null && hiddenDangerListGetIn.getEndDate() != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(hiddenDangerListGetIn.getEndDate());
            c.add(Calendar.SECOND, 86399); //结束时间加到当天的23:59:59
            queryWrapper.between("create_time", hiddenDangerListGetIn.getStartDate(), c.getTime());
        }
        if (hiddenDangerListGetIn.getOperatorId() != null) {
            queryWrapper.eq("operator_id", hiddenDangerListGetIn.getOperatorId());
        }
        if (hiddenDangerListGetIn.getLevel() != null) {
            queryWrapper.eq("level", hiddenDangerListGetIn.getLevel());
        }
        if (hiddenDangerListGetIn.getType() != null) {
            queryWrapper.eq("type", hiddenDangerListGetIn.getType());
        }
        if (hiddenDangerListGetIn.getTaskType() != null) {
            queryWrapper.eq("task_type", hiddenDangerListGetIn.getTaskType());
        }
        if (hiddenDangerListGetIn.getTaskId() != null) {
            queryWrapper.eq("task_id", hiddenDangerListGetIn.getTaskId());
        }
//        if (hiddenDangerListGetIn.getTaskWorkDate()!=null){
//            queryWrapper.eq("task_work_date", hiddenDangerListGetIn.getTaskWorkDate());
//        }
        if (hiddenDangerListGetIn.getSpecialCategory() != null) {
            queryWrapper.eq("special_category", hiddenDangerListGetIn.getSpecialCategory());
        }
        if (hiddenDangerListGetIn.getRiskFactorTypeDetail() != null) {
            queryWrapper.apply("risk_factor_type_detail = CAST({0} as json)",
                    JSON.toJSONString(hiddenDangerListGetIn.getRiskFactorTypeDetail()));
        }
        if (hiddenDangerListGetIn.getHiddenDangerStatus() != null) {
            queryWrapper.eq("status", hiddenDangerListGetIn.getHiddenDangerStatus().name());
        }
        if (hiddenDangerListGetIn.getAuditStatus() != null) {
            queryWrapper.eq("audit_status", hiddenDangerListGetIn.getAuditStatus());
        }
        if (hiddenDangerListGetIn.getIsRelease() != null) {
            queryWrapper.eq("is_release", hiddenDangerListGetIn.getIsRelease());
        }
        if (hiddenDangerListGetIn.getIsFindByCallback() != null) {
            queryWrapper.eq("is_find_by_callback", hiddenDangerListGetIn.getIsFindByCallback());
        }
        if (hiddenDangerListGetIn.getIsNeedEdit() != null) {
            queryWrapper.eq("is_need_edit", hiddenDangerListGetIn.getIsNeedEdit());
        }
        if (hiddenDangerListGetIn.getIsWrongSubmit() != null && hiddenDangerListGetIn.getIsWrongSubmit()) {
            queryWrapper.eq("is_wrong_submit", true);
        } else {
            queryWrapper.eq("is_wrong_submit", false);
        }
        queryWrapper.orderByDesc("create_time");

        return list(queryWrapper);
    }

    @Override
    public List<HiddenDanger> getHiddenDangerGroupedList(HiddenDangerListApiGetIn hiddenDangerListGetIn) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        if (hiddenDangerListGetIn.getCorporationId() != null) {
            queryWrapper.eq("corporation_id", hiddenDangerListGetIn.getCorporationId());
        }
        if (hiddenDangerListGetIn.getProjectId() != null) {
            queryWrapper.eq("project_id", hiddenDangerListGetIn.getProjectId());
        }
        queryWrapper.eq("is_wrong_submit", false);
        queryWrapper.select("id", "project_id", "corporation_id", "operator");
        queryWrapper.groupBy("project_id", "corporation_id");

        return list(queryWrapper);
    }

    @Override
    public List<HiddenDanger> getUnSubmittedList(List<Long> hdIdList) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", hdIdList);
//        queryWrapper.eq("audit_status", AuditStatus.UnSubmitted.name());
        queryWrapper.eq("is_wrong_submit", false);
        List<HiddenDanger> list = list(queryWrapper);
        list.forEach(hiddenDanger -> {
            List<HiddenDangerSuggestion> hiddenDangerSuggestions = hiddenDangerSuggestionService.list(hiddenDanger.getId());
            hiddenDanger.setSuggestionList(hiddenDangerSuggestions);
        });

        return list;
    }

    @Override
    public List<HiddenDanger> getAuditedList(List<Long> hdIdList) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", hdIdList);
        queryWrapper.eq("audit_status", AuditStatus.UnReleased.name());
        queryWrapper.eq("is_wrong_submit", false);

        return list(queryWrapper);
    }

    @Override
    public void sendDingMessage(List<HiddenDanger> hiddenDangerList) {
        Long userId = AdminUserHelper.getCurrentUserId();
        StaffUserDto adminUser = userCenterService.getAdminUser(userId);
        String name = hiddenDangerList.get(0).getArrangedAuditor();
        String project = hiddenDangerList.get(0).getProjectName();
        String corp = hiddenDangerList.get(0).getCorpName();
        int size = hiddenDangerList.size();
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String message = name + "，" + project + "-" + corp + "的" + size + "条隐患需审查，现提交到你处，请在平台中及时处理，谢谢。提交人："
                + adminUser.getRealName() + "  " + date;
        StaffUserDto auditor = userCenterService.getAdminUser(hiddenDangerList.get(0).getArrangedAuditorId());
        messageService.sendDing(message, auditor.getPhone());
    }

    @Override
    public void updateArrangedAuditorByProject(Long projectId, HiddenDanger hiddenDanger) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);

        update(hiddenDanger, queryWrapper);
    }

    @Override
    public Page<HiddenDanger> getCorpAuditList(List<Long> submitList, AuditStatus auditStatus, CommonPage commonPage) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", submitList);
        queryWrapper.eq("is_wrong_submit", false);
        if (auditStatus != null) {
            queryWrapper.eq("audit_status", auditStatus.name());
        }
        queryWrapper.orderByDesc("create_time");

        Page page = new Page(commonPage.getPageNum(), commonPage.getPageSize());
        return (Page<HiddenDanger>) page(page, queryWrapper);
    }

    @Override
    public void updateCorpName(Long corporationId, HiddenDanger hiddenDanger) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("corporation_id", corporationId);

        update(hiddenDanger, queryWrapper);
    }

    @Override
    public Boolean checkRiskFactorTypeUsed(LabelValue labelValue) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply("JSON_CONTAINS(risk_factor_type_detail, {0}, '$')",
                JSON.toJSONString(labelValue));
        long count = count(queryWrapper);
        return count > 0;
    }

    @Override
    public void updateHdLedgerStatus(Long projectId, Long corporationId, Integer turn, String realName) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("corporationId", corporationId);
        params.put("turn", turn);
        params.put("name", realName);

        try {
            JSONObject connPost = OkHttpKit.apiPostWithToken(yqycApi + "/api/investigation/updateHdLedgerStatus",
                    JSON.toJSONString(params), token);
            log.info("updateHdLedgerStatus: " + JSON.toJSONString(connPost));
        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }

    @Override
    public List<HiddenDanger> getProjectCooperateStatistic(Long projectId, List<Long> corpIds, Boolean isFindByCallback) {
        QueryWrapper<HiddenDanger> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_wrong_submit", false);
        queryWrapper.eq("project_id", projectId);
        queryWrapper.in("corporation_id", corpIds);

        if (isFindByCallback != null) {
            queryWrapper.eq("is_find_by_callback", isFindByCallback);
        }

        return list(queryWrapper);
    }

    @Override
    public HdStatisticOut getNewHdStatistic(HiddenDangerListGetIn hiddenDangerListGetIn) {
        HdStatisticOut statisticOut = new HdStatisticOut();

        //隐患概况
        List<HiddenDanger> hiddenDangerList = getHiddenDangerAllList(hiddenDangerListGetIn);
        int totalCount = hiddenDangerList.size();
        long callbackCount = hiddenDangerList.stream().filter(item -> item.getTaskType() != null && item.getTaskType() == 3).count();

        //隐患状态计数
        Map<String, Long> statusMap = hiddenDangerList.stream().collect(Collectors.groupingBy(HiddenDanger::getStatus, Collectors.counting()));
        float doneRate = totalCount == 0 || statusMap.get("已改善") == null ? 0 : statusMap.get("已改善") * 100F / totalCount;
        statisticOut.setTotalCount(totalCount);
        statisticOut.setCallbackCount(callbackCount);
        statisticOut.setStatusMap(statusMap);
        statisticOut.setDoneRate(Math.round(doneRate * 100) / 100f);

        //逾期未改善
        int unDoneCount = 0;
        for (HiddenDanger hiddenDanger : hiddenDangerList) {
            if (hiddenDanger.getStatus().equals("待改善")) {
                Calendar c = Calendar.getInstance();
                c.add(Calendar.DAY_OF_MONTH, hiddenDanger.getRectifyDeadline() * -1);
                if (hiddenDanger.getCreateTime().compareTo(c.getTime()) < 0) {
                    unDoneCount++;
                }
            }
        }
        statisticOut.setUnDoneCount(unDoneCount);

        //隐患审查计数
        Map<String, Long> auditStatusMap = hiddenDangerList.stream().collect(Collectors.groupingBy(HiddenDanger::getAuditStatus, Collectors.counting()));
        statisticOut.setAuditStatusMap(auditStatusMap);

        //隐患分级
        Map<String, Long> levelMap = hiddenDangerList.stream().filter(h -> !h.getLevel().isEmpty())
                .collect(Collectors.groupingBy(HiddenDanger::getLevel, Collectors.counting()));
        statisticOut.setLevelMap(levelMap);

        //隐患门类
        Map<String, Long> typeMap = hiddenDangerList.stream().filter(h -> !h.getType().isEmpty())
                .collect(Collectors.groupingBy(HiddenDanger::getType, Collectors.counting()));
        statisticOut.setTypeMap(typeMap);

        //隐患专项类别
        Map<String, Long> specialCategoryMap = hiddenDangerList.stream().filter(h -> StringUtils.hasLength(h.getSpecialCategory()))
                .collect(Collectors.groupingBy(HiddenDanger::getSpecialCategory, Collectors.counting()));
        statisticOut.setSpecialCategoryMap(specialCategoryMap);


        //危害因素分类
        if (hiddenDangerListGetIn.getProjectId() != null) {
            ProjectDto project = userCenterService.getProject(hiddenDangerListGetIn.getProjectId());
            List<HdStatisticOut.RiskFactorType> riskFactorTypeList = new ArrayList<>();
            if ("人机料法环".equals(project.getRiskCategory())) {
                Map<String, List<HiddenDanger>> riskFactorTypeListMap = hiddenDangerList.stream().filter(h -> StringUtils.hasLength(h.getRiskFactorTypeDetailAlias())).collect(Collectors.groupingBy(
                        h -> JSON.parseArray(h.getRiskFactorTypeDetailAlias(), LabelValue.class).get(0).getLabel()
                ));
                for (Map.Entry<String, List<HiddenDanger>> entry : riskFactorTypeListMap.entrySet()) {
                    HdStatisticOut.RiskFactorType riskFactorType = new HdStatisticOut.RiskFactorType();
                    riskFactorType.setType(entry.getKey());
                    List<HiddenDanger> hiddenDangers = entry.getValue();
                    riskFactorType.setCount(hiddenDangers.size());
                    Map<String, Long> riskFactorTypeMap = hiddenDangers.stream()
                            .filter(h -> h.getRiskFactorTypeDetailAlias() != null && JSON.parseArray(h.getRiskFactorTypeDetailAlias(), LabelValue.class).size() > 1)
                            .map(h -> JSON.parseArray(h.getRiskFactorTypeDetailAlias(), LabelValue.class).get(1))
                            .collect(Collectors.groupingBy(LabelValue::getLabel, Collectors.counting()));
                    riskFactorType.setRiskFactorTypeMap(riskFactorTypeMap);
                    riskFactorTypeList.add(riskFactorType);
                }
            } else {
                Map<String, List<HiddenDanger>> riskFactorTypeListMap = hiddenDangerList.stream()
                        .filter(h -> StringUtils.hasLength(h.getRiskFactorType()))
                        .collect(Collectors.groupingBy(HiddenDanger::getRiskFactorType));
                for (Map.Entry<String, List<HiddenDanger>> entry : riskFactorTypeListMap.entrySet()) {
                    HdStatisticOut.RiskFactorType riskFactorType = new HdStatisticOut.RiskFactorType();
                    riskFactorType.setType(entry.getKey());
                    List<HiddenDanger> hiddenDangers = entry.getValue();
                    riskFactorType.setCount(hiddenDangers.size());
                    Map<String, Long> riskFactorTypeMap = hiddenDangers.stream()
                            .filter(h -> h.getRiskFactorTypeDetail() != null && JSON.parseArray(h.getRiskFactorTypeDetail(), LabelValue.class).size() > 1)
                            .map(h -> JSON.parseArray(h.getRiskFactorTypeDetail(), LabelValue.class).get(1))
                            .collect(Collectors.groupingBy(LabelValue::getLabel, Collectors.counting()));
                    riskFactorType.setRiskFactorTypeMap(riskFactorTypeMap);
                    riskFactorTypeList.add(riskFactorType);
                }
            }
            statisticOut.setRiskFactorTypeList(riskFactorTypeList);
        }

        //隐患可能的后果
        Map<String, Long> possibleConsequenceMap = hiddenDangerList.stream()
                .filter(h -> h.getPossibleConsequence() != null && !h.getPossibleConsequence().isEmpty())
                .map(HiddenDanger::getPossibleConsequence)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        statisticOut.setPossibleConsequenceMap(possibleConsequenceMap);

        return statisticOut;
    }

    @Override
    public JSONObject convertRiskFactorTypeDetail(List<HiddenDanger> list) {
        if (list == null || list.isEmpty()) {
            return new JSONObject();
        }
        Map<String, String> collect = list.stream().collect(Collectors.toMap(key -> key.getId() + "", HiddenDanger::getRiskFactorTypeDetail));
        return securityTankApiService.transferMatch(collect);
    }

    @Override
    @Transactional
    public void doSaveList(HiddenDangerListIn hiddenDangerListIn) {

        //检查参数
        if (!BeanFieldCheckingUtils.forAllFieldNotNull(hiddenDangerListIn)) {
            throw new ParameterErrorException(ErrorMessage.parameter_error);
        }

        Long userId = AdminUserHelper.getCurrentUserId();
        StaffUserDto adminUser = userCenterService.getAdminUser(userId);
        ProjectDto project = userCenterService.getProject(hiddenDangerListIn.getHiddenDangerList().get(0).getProjectId());

        hiddenDangerListIn.getHiddenDangerList().forEach(hiddenDangerList -> {
            HiddenDanger hiddenDanger = new HiddenDanger();
            Long hiddenDangerId = SnGeneratorUtil.getId();
            hiddenDanger.setId(hiddenDangerId);
            hiddenDanger.setCreateTime(hiddenDangerList.getCreateTime());
            hiddenDanger.setOperator(adminUser.getRealName());
            hiddenDanger.setOperatorId(adminUser.getId());
            BeanUtils.copyProperties(hiddenDangerList, hiddenDanger);
            hiddenDanger.setPictures(hiddenDangerList.getPictures());
            if (hiddenDangerList.getGifPictures() != null) {
                hiddenDanger.setGifPictures(JSONArray.parseArray(hiddenDangerList.getGifPictures(), String.class));
            }
            hiddenDanger.setFiles(hiddenDangerList.getFiles());
            hiddenDanger.setStandard(JSON.toJSONString(hiddenDangerList.getStandard()));
            hiddenDanger.setSpecialCategoryDetail(JSON.toJSONString(hiddenDangerList.getSpecialCategoryDetail()));
            hiddenDanger.setRiskFactorTypeDetail(JSON.toJSONString(hiddenDangerList.getRiskFactorTypeDetail()));
            hiddenDanger.setPossibleConsequence(hiddenDangerList.getPossibleConsequence());
            hiddenDanger.setStatus(HiddenDangerStatus.待改善.name());
            hiddenDanger.setIsWrongSubmit(false);
            hiddenDanger.setIsFindByCallback(hiddenDanger.getTaskType() == 3);
            hiddenDanger.setArrangedAuditor(project.getTechAssistant());
            hiddenDanger.setArrangedAuditorId(project.getTechAssistantId());
            hiddenDanger.setAuditStatus(AuditStatus.UnReleased.name());

            OperationLogs operationLogs = new OperationLogs();
            operationLogs.setId(SnGeneratorUtil.getId());
            operationLogs.setCreateTime(hiddenDangerList.getCreateTime());
            operationLogs.setCorporationId(hiddenDangerList.getCorporationId());
            operationLogs.setHiddenDangerId(hiddenDangerId);
            operationLogs.setOperationType(OperationType.submit.name());
            operationLogs.setInChargeName(adminUser.getRealName());
            operationLogs.setOperatorId(adminUser.getId());
            operationLogs.setOperator(adminUser.getRealName());

            SpringUtil.getBean(HiddenDangerService.class).submitHiddenDanger(hiddenDanger, operationLogs);
        });

        updateHdLedgerStatus(hiddenDangerListIn.getHiddenDangerList().get(0).getProjectId(),
                hiddenDangerListIn.getHiddenDangerList().get(0).getCorporationId(),
                hiddenDangerListIn.getHiddenDangerList().get(0).getTurn(), adminUser.getRealName());

    }

    @Override
    public void checkHiddenDangerContent(HiddenDanger hiddenDanger) {
        List<String> possibleConsequence = hiddenDanger.getPossibleConsequence();
        if (possibleConsequenceDictCache == null) {
            List<DataDict> dataDictionary = userCenterService.getDataDictionary(DictType.yhoutcome);
            possibleConsequenceDictCache = dataDictionary.stream().map(DataDict::getLabel).collect(Collectors.toSet());
        }
        if (possibleConsequence != null) {
            for (Object o : possibleConsequence) {
                if (!possibleConsequenceDictCache.contains(o.toString())) {
                    throw new DataErrorException("数据异常，可能照成的后果不在字典中：" + o);
                }
            }
        }
    }

    @Override
    public List<Object> smartRecommendStandby(String description) {
//        HiddenDanger limit1 = lambdaQuery().ne(HiddenDanger::getStandard,"").ne(HiddenDanger::getStandard, "[]").isNotNull(HiddenDanger::getStandard).orderByDesc(HiddenDanger::getStandard).last("limit 1").one();
//        try {
//            Thread.sleep(10000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//        return JSONArray.parseArray(limit1.getStandard());
        if (StrUtil.isBlank(description)) {
            throw new DataErrorException("隐患描述不能为空");
        }
        return aiService.getRecommendStandard(description);
    }

    @Override
    public void updateHiddenDanger(HiddenDanger hiddenDanger) {
        Long userId = AdminUserHelper.getCurrentUserId();
        StaffUserDto adminUser = userCenterService.getAdminUser(userId);
        updateById(hiddenDanger);
        updateHdLedgerStatus(hiddenDanger.getProjectId(), hiddenDanger.getCorporationId(), hiddenDanger.getTurn(), adminUser.getRealName());
    }

    @Override
    public JSONObject recommendHdByDesc(String description) {
        if (StrUtil.isBlank(description)) {
            throw new DataErrorException("隐患描述不能为空");
        }
        return aiService.recommendHdByDesc(description);
    }

    @Override
    @Transactional
    public void doEdit(HiddenDangerEditIn in) {
        HiddenDangerIn hiddenDangerIn = in.getHiddenDangerIn();
        Long hiddenDangerId = hiddenDangerIn.getId();

        HiddenDanger hiddenDanger = getById(hiddenDangerId);
        if (hiddenDanger == null) {
            throw new DataErrorException("未找到该隐患");
        }
//        if(hiddenDanger.getAuditStatus().equals(AuditStatus.Released.name())){
//            throw new DataErrorException("已发布隐患不可编辑");
//        }
        if (hiddenDangerIn.getRiskFactorTypeDetail().size() <= 1) {
            throw new DataErrorException("危害因素分类必须填到二级以上");
        }
        if ("重大隐患".equals(hiddenDangerIn.getLevel()) && hiddenDangerIn.getSuggestionList().isEmpty()) {
            throw new DataErrorException("重大隐患建议改善措施必须分项录入");
        }
        if (taskApi.isWorkHandover(hiddenDanger.getTaskId())) {
            // 任务已移交
            HdAuditLogIn hdAuditLogIn = in.getHdAuditLog();
            HdAuditLog hdAuditLog = new HdAuditLog();
            BeanUtil.copyProperties(hdAuditLogIn, hdAuditLog);
            if(hdAuditLogIn.getId() == null){
                BeanUtil.copyProperties(hiddenDanger, hdAuditLog);
                hdAuditLog.setId(SnGeneratorUtil.getId());
                hdAuditLog.setCreateTime(new Date());
                hdAuditLog.setHiddenDangerId(hiddenDanger.getId());
            }
            hdAuditLogService.saveOrUpdate(hdAuditLog);
        }
        if (hiddenDanger.getIsNeedEdit() != null && hiddenDanger.getIsNeedEdit()) {
            hiddenDanger.setIsNeedEdit(false);
        }
        BeanUtils.copyProperties(hiddenDangerIn, hiddenDanger, true);
        if (hiddenDangerIn.getPictures() != null) {
            hiddenDanger.setPictures(hiddenDangerIn.getPictures());
        }
        if (hiddenDangerIn.getGifPictures() != null) {
            hiddenDanger.setGifPictures(hiddenDangerIn.getGifPictures());
        }
        if (hiddenDangerIn.getFiles() != null) {
            hiddenDanger.setFiles(hiddenDangerIn.getFiles());
        }
        if (hiddenDangerIn.getStandard() != null) {
            hiddenDanger.setStandard(JSON.toJSONString(hiddenDangerIn.getStandard()));
            SpringUtil.getBean(HiddenDangerService.class).handleRegulationsMemo(hiddenDanger);
        }
        if (hiddenDangerIn.getPossibleConsequence() != null) {
            hiddenDanger.setPossibleConsequence(hiddenDangerIn.getPossibleConsequence());
        }
        if (hiddenDangerIn.getSpecialCategoryDetail() != null) {
            hiddenDanger.setSpecialCategoryDetail(JSON.toJSONString(hiddenDangerIn.getSpecialCategoryDetail()));
        }
        if (hiddenDangerIn.getRiskFactorTypeDetail() != null) {
            hiddenDanger.setRiskFactorTypeDetail(JSON.toJSONString(hiddenDangerIn.getRiskFactorTypeDetail()));
        }
        if (hiddenDanger.getIsNeedEdit() != null && hiddenDanger.getIsNeedEdit()) {
            hiddenDanger.setIsNeedEdit(false);
        }
        JSONObject object = SpringUtil.getBean(HiddenDangerService.class).convertRiskFactorTypeDetail(Collections.singletonList(hiddenDanger));
        hiddenDanger.setRiskFactorTypeDetailAlias(object.getString(hiddenDanger.getId() + ""));
        SpringUtil.getBean(HiddenDangerService.class).checkHiddenDangerContent(hiddenDanger);
        SpringUtil.getBean(HiddenDangerService.class).updateHiddenDanger(hiddenDanger);

        if (!hiddenDangerIn.getSuggestionList().isEmpty()) {
            LambdaQueryWrapper<HiddenDangerSuggestion> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HiddenDangerSuggestion::getHdId, hiddenDangerId);
            hiddenDangerSuggestionService.remove(queryWrapper);
            hiddenDangerIn.getSuggestionList().forEach(suggestion -> {
                suggestion.setHdId(hiddenDangerId);
                hiddenDangerSuggestionService.save(suggestion);
            });
        }
    }

    @Override
    @Transactional
    public Boolean delete(Long id) {
        removeById(id);
        checkResultService.lambdaUpdate().eq(CheckResult::getHiddenDangerId, id)
                .set(CheckResult::getResult, 0)
                .set(CheckResult::getHiddenDangerId, null)
                .update();
        return true;
    }

    @Override
    @Transactional
    public Boolean release(HdReleaseIn in) {
        List<HiddenDanger> list = lambdaQuery().eq(HiddenDanger::getProjectId, in.getProjectId())
                .eq(HiddenDanger::getCorporationId, in.getCorporationId())
                .eq(HiddenDanger::getTurn, in.getTurn())
                .eq(in.getTaskType() != null, HiddenDanger::getTaskType, in.getTaskType())
                .eq(HiddenDanger::getAuditStatus, AuditStatus.UnReleased.name())
                .eq(HiddenDanger::getIsWrongSubmit, false)
                .list();
        if (in.getEliminateRecommendHd() && !list.isEmpty()) {
            List<Long> collect = list.stream().map(HiddenDanger::getId).collect(Collectors.toList());
            if (!collect.isEmpty()) {
                List<Long> checkHdIds = checkResultService.lambdaQuery().in(CheckResult::getHiddenDangerId, collect).list().stream().map(CheckResult::getHiddenDangerId).collect(Collectors.toList());
                list.removeIf(item -> checkHdIds.contains(item.getId()));
            }
        }
        if(list.isEmpty()){
            return true;
        }

        checkHdCompletion(list);

        List<Long> hdIds = list.stream().map(HiddenDanger::getCorporationId).distinct().collect(Collectors.toList());
        if (hdIds.size()>1){
            throw new DataErrorException("请一次发布只针对一家公司");
        }

        for (HiddenDanger hiddenDanger : list){
            hiddenDanger.setIsNeedEdit(false);
            hiddenDanger.setAuditStatus(AuditStatus.Released.name());
            updateById(hiddenDanger);
        }
        return true;
    }

    @Override
    public CommonResult<List<CorpSafeListOut>> corpSafeList(CorpSafeListIn in) {
        // 企业安全监测
        TaskCorpListIn taskCorpListIn = new TaskCorpListIn();
        taskCorpListIn.setProjectId(in.getProjectId());
        taskCorpListIn.setCorpName(in.getCorpName());
        taskCorpListIn.setTaskTypes(Arrays.asList(TaskTypeEnum.Survey, TaskTypeEnum.AssistiveSurveyTraining));
        // 填充筛选出的企业
        Map<Long, CorpSafeListOut> hdMap = null;
        Map<Long, CorpSafeListOut> injuryMap = null;
        if(StrUtil.isEmpty(in.getCorpName())){
            Set<Long> corpIds = null;
            if(in.getHdOverdue() != null){
                // 获取隐患逾期企业列表
                List<CorpSafeListOut> cropSafeStatistic = getBaseMapper().getCropSafeStatistic(in);
                hdMap = cropSafeStatistic.stream().collect(Collectors.toMap(CorpSafeListOut::getCorpId, corpSafeListOut -> corpSafeListOut, (k1, k2) -> k1));
                if(hdMap.isEmpty()){
                    return CommonResult.successData(new ArrayList<>());
                }
                corpIds = new HashSet<>(hdMap.keySet());
            }
            // 工伤增长率筛选
            if(in.getWorkInjuryGrowthRateSymbol() != null && in.getWorkInjuryGrowthRateNumber() != null){
                injuryMap = getInjuryMap(in,null);
                if(injuryMap == null || injuryMap.isEmpty()){
                    return CommonResult.successData(new ArrayList<>());
                }
                if(corpIds == null || corpIds.isEmpty()){
                    corpIds = injuryMap.keySet();
                }else{
                    corpIds.retainAll(injuryMap.keySet());
                }
            }
            if(corpIds != null){
                if(corpIds.isEmpty()){
                    return CommonResult.successData(new ArrayList<>());
                }else{
                    taskCorpListIn.setCorpIds(corpIds);
                }
            }
        }
        taskCorpListIn.setPageNum((int) in.getPageNum());
        taskCorpListIn.setPageSize((int) in.getPageSize());
        CommonResult<List<TaskCorpListOut>> taskResult = taskApi.getCorpList(taskCorpListIn);
        List<TaskCorpListOut> data = taskResult.getData();
        if(data == null || data.isEmpty()){
            return CommonResult.successData(new ArrayList<>());
        }
        if(hdMap == null){
            Set<Long> corpIds = data.stream().map(TaskCorpListOut::getCorpId).collect(Collectors.toSet());
            in.setCorpIds(corpIds);
            List<CorpSafeListOut> cropSafeStatistic = getBaseMapper().getCropSafeStatistic(in);
            hdMap = cropSafeStatistic.stream().collect(Collectors.toMap(CorpSafeListOut::getCorpId, corpSafeListOut -> corpSafeListOut, (k1, k2) -> k1));
        }
        if(injuryMap == null){
            Map<String, Long> collect = data.stream().collect(Collectors.toMap(TaskCorpListOut::getCorpName, TaskCorpListOut::getCorpId, (k1, k2) -> k1));
            injuryMap = getInjuryMap(in,collect);
        }
        List<CorpSafeListOut> resultData = new ArrayList<>();
        for (TaskCorpListOut taskCorpListOut : data) {
            CorpSafeListOut corpSafeListOut = BeanUtils.copyNew(taskCorpListOut, CorpSafeListOut.class);
            //填充工伤数据
            CorpSafeListOut injuryData = injuryMap.get(taskCorpListOut.getCorpId());
            BeanUtils.copyProperties(injuryData, corpSafeListOut, true);
            //填充隐患数据
            CorpSafeListOut hdData = hdMap.get(taskCorpListOut.getCorpId());
            BeanUtils.copyProperties(hdData, corpSafeListOut, true);
            String workInjuryGrowthRate = corpSafeListOut.getWorkInjuryGrowthRate();
            if (workInjuryGrowthRate != null) {
                corpSafeListOut.setWorkInjuryGrowthRateStr(String.format("%.2f", Float.parseFloat(workInjuryGrowthRate)) + "%");
            }
            if (corpSafeListOut.getImproveRate() != null) {
                corpSafeListOut.setImproveRateStr(String.format("%.2f", Float.parseFloat(corpSafeListOut.getImproveRate()) * 100) + "%");
            }

            resultData.add(corpSafeListOut);
        }

        CommonResult<List<CorpSafeListOut>> result = new CommonResult<>();
        result.setCode(taskResult.getCode());
        result.setMsg(taskResult.getMsg());
        result.setData(resultData);
        result.setPage(taskResult.getPage());
        return result;
    }

    @Autowired
    private HttpServletResponse response;

    @Override
    public void exportCorpSafeList(CorpSafeListIn in) {
        ProjectDto project = userCenterService.getProject(in.getProjectId());
        if(project ==  null){
            throw new DataErrorException("项目不存在");
        }
        List<CorpSafeListOut> data = corpSafeList(in).getData();
        Map<String, String> map = new HashMap<>();
        map.put("projectName", project.getProjectName());
        ExcelOutUtil.exportByTemp(response, "/file/企业安全监测导出模板.xlsx", "企业安全监测导出数据-" + DatePattern.PURE_DATETIME_FORMAT.format(new Date()) + ".xlsx", map, data);
    }

    private Map<Long, CorpSafeListOut> getInjuryMap(CorpSafeListIn in,Map<String,Long> corpNameMap){
        Map<Long, CorpSafeListOut> injuryMap = null;
        ProjectDto project = userCenterService.getProject(in.getProjectId());
        if(project ==  null){
            throw new DataErrorException("项目不存在");
        }
        Date contractDate = project.getContractDate();
        if(project.getContractDate() == null){
            throw new DataErrorException("项目签约时间不存在");
        }
        if(corpNameMap == null){
            List<Corporation> projectCorporations = corporationProjectApi.getCorpByProjectId(in.getProjectId(), null).getData();
            corpNameMap = projectCorporations.stream().collect(Collectors.toMap(Corporation::getCorpName, Corporation::getId, (k1, k2) -> k1));
        }
        if(corpNameMap.isEmpty()){
            return new HashMap<>();
        }
        InjuryStatisticsIn injuryStatisticsIn = new InjuryStatisticsIn();
        injuryStatisticsIn.setCorpNameList(corpNameMap.keySet());
        injuryStatisticsIn.setAccidentDateMin(contractDate);
        injuryStatisticsIn.setAccidentDateMax(new Date());
        injuryStatisticsIn.setWorkInjuryGrowthRateNumber(in.getWorkInjuryGrowthRateNumber());
        injuryStatisticsIn.setWorkInjuryGrowthRateSymbol(in.getWorkInjuryGrowthRateSymbol());
        List<InjuryStatisticsOut> injuryStatistics = workInjuryApi.getInjuryStatistics(injuryStatisticsIn);
        for (InjuryStatisticsOut injuryStatistic : injuryStatistics) {
            Long corpId = corpNameMap.get(injuryStatistic.getCorpName());
            if(corpId == null){
                continue;
            }
            CorpSafeListOut corpSafeListOut = new CorpSafeListOut();
            corpSafeListOut.setCorpName(injuryStatistic.getCorpName());
            corpSafeListOut.setCorpId(corpId);
            corpSafeListOut.setWorkInjuryNum(injuryStatistic.getWorkInjuryNum());
            corpSafeListOut.setWorkInjuryGrowthRate(injuryStatistic.getWorkInjuryGrowthRate());
            if(injuryMap == null){
                injuryMap = new HashMap<>();
            }else{
                injuryMap.put(corpId, corpSafeListOut);
            }
        }
        return injuryMap;
    }

    private void checkHdCompletion(List<HiddenDanger> hiddenDangerList) {
        for (HiddenDanger hiddenDanger : hiddenDangerList){
            String description = hiddenDanger.getDescription();
            if (hiddenDanger.getPictures() ==  null || hiddenDanger.getPictures().isEmpty()){
                throw new DataErrorException("请上传隐患图片--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getDescription())){
                throw new DataErrorException("请填写隐患描述--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getType())){
                throw new DataErrorException("请填写隐患类别--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getLevel())){
                throw new DataErrorException("请填写隐患级别--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getRiskFactorType())){
                throw new DataErrorException("请填写危害因素分类--"+description);
            }
            if(StringUtils.hasLength(hiddenDanger.getRiskFactorTypeDetail())){
                JSONArray array = JSON.parseArray(hiddenDanger.getRiskFactorTypeDetail());
                if (array.size()<=1){
                    throw new DataErrorException("危害因素分类必须填到二级以上"+description);
                }
            }
            if (hiddenDanger.getPossibleConsequence() == null || hiddenDanger.getPossibleConsequence().isEmpty()){
                throw new DataErrorException("请填写可能导致后果--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getRectificationSuggestion()) && hiddenDanger.getSuggestionList().isEmpty()){
                throw new DataErrorException("请填写建议改善措施--"+description);
            }
            if (!StringUtils.hasLength(hiddenDanger.getStandard()) || hiddenDanger.getStandard().length()<3){
                throw new DataErrorException("请填写依据标准--"+description);
            }
        }
    }


}




