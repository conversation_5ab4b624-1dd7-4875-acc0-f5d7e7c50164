package com.yixun.hdm.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.convert.ListTypeHandler;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 持表检查结果表(CheckResult)表实体类
 *
 * <AUTHOR>
 * @since 2025-04-30 16:30:11
 */
@SuppressWarnings("serial")
@Data
@ApiModel("持表检查结果表")
@TableName(value = "hdm_check_result",autoResultMap = true)
public class CheckResult extends Model<CheckResult> {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;

    private Date createTime;

    @ApiModelProperty("任务id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long taskId;

    @ApiModelProperty("项目id")
    @NotNull(message="[项目id]不能为空")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("公司id")
    @NotNull(message="[公司id]不能为空")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;

    @ApiModelProperty("公司名称")
    private String corpName;

    @ApiModelProperty("检查项id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long checkContentId;

    @ApiModelProperty("检查项目名称")
    private String checkProjectName;

    @ApiModelProperty("检查内容")
    private String checkContent;

    @ApiModelProperty("检查结果 0合格 1隐患 2不涉及 3重复项")
    private Integer result;

    @ApiModelProperty("隐患id")
    @JsonSerialize(using = LongJsonSerializer.class)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long hiddenDangerId;

    @ApiModelProperty("图片")
    @TableField(updateStrategy = FieldStrategy.IGNORED,typeHandler = ListTypeHandler.class)
    private List<String> picture;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("是否提交")
    private Boolean isSubmit;

    @ApiModelProperty("执行负责人")
    private String executor;

    @ApiModelProperty("陪同人员")
    private String accompany;

    @ApiModelProperty("推荐原因")
    private String recommendReason;

    @ApiModelProperty("推荐检查的隐患Id")
    private String recommendHdId;

    @ApiModelProperty("推荐检查的隐患描述")
    private String recommendHdDesc;

    @ApiModelProperty("推荐隐患区域")
    private String recommendHdArea;

    @ApiModelProperty("推荐隐患区域序列")
    private Integer recommendHdAreaSeq;

    @ApiModelProperty("推荐隐患详细地址")
    private String recommendHdAddress;

    @ApiModelProperty("推荐隐患伤害类型")
    private String recommendHdHurtType;

    @ApiModelProperty("推荐隐患作业活动")
    private String recommendHdJobActivity;

    @ApiModelProperty("推荐隐患岗位工种")
    private String recommendHdWorkCategory;

    @ApiModelProperty("推荐隐患的工伤数据摘要")
    private String recommendHdAccidentHistoryAbstract;

    @ApiModelProperty("推荐隐患的工伤数据")
    private String recommendHdAccidentHistory;

    @ApiModelProperty("推荐隐患的工伤sid")
    private String recommendHdAccidentSid;

    @ApiModelProperty("推荐隐患的场所")
    private String recommendHdTierCategory;

    @ApiModelProperty("推荐隐患的一级场所")
    private String recommendHdFirstTierCategory;

    @ApiModelProperty("推荐隐患的二级场所")
    private String recommendHdSecondTierCategory;

    @ApiModelProperty("推荐隐患的三级场所")
    private String recommendHdThirdTierCategory;

    @ApiModelProperty("隐患的推荐原因AI")
    private String recommendAiReason;

    @ApiModelProperty("推荐隐患一级分类")
    private String recommendFirstLevelClassification;

    @ApiModelProperty("推荐隐患二级分类")
    private String recommendSecondLevelClassification;

    @ApiModelProperty("推荐隐患类型;精准隐患、工伤隐患")
    private String recommendHdType;

    @ApiModelProperty("推荐隐患或风险")
    private String recommendHdOrRisk;

    @ApiModelProperty("推荐维度")
    private String recommendDimension;

    @ApiModelProperty("推荐维度排序")
    private Integer recommendDimensionOrder;



	@TableField(exist = false)
	private String batchFlag;

}

