server:
  port: 9075
  servlet:
    context-path: /hdanger-admin-api
spring:
  application:
    name: hdm-admin-service
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ****************************************************************************************************************************
          username: root
          password: tg?Lxf%61ew#yiXu82
        opt_logs:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************
          username: root
          password: tg?Lxf%61ew#yiXu82
  redis:
    database: 0
    host: ***************
    password: yiXn#9alm(rd2S
    port: 6379
    timeout: 5000
jwt:
  secret:
    account: ****************************************
  expiration: 604800    #单位秒，值为7天‬
aliyun:
  endpoint: sts.cn-chengdu.aliyuncs.com
  bucket: yxcloud-app-dev
weixin:
  appId: wxfd2778094f23e14f
  appSecret: 306f7174c97c3ae84075846348d9ad78
api:
  token: C9F1B71774E4899B9ECFC735A39A8659
  yqycApi: https://yqyc-api-dev.yxgsyf.com/yqyc-api
  yqyc-api:
    url: https://yqyc-api-dev.yxgsyf.com/yqyc-api
  hdangerApi: http://***************:9070/hdanger-api
  messageCenterApi: http://***************:10098/message-center-api
  examManageApi: http://***************:9009/exam-manage-api
  integralApi: http://***************:10020/integral-api
  securityTankApi: http://***************:10040/security-tank-api
  regulationSearchApi: http://***************:9050/regulation-search-api
  userCenterApi: http://***************:11000/usercenter-api
  aiBaseUrl: http://aiapp.yxgsyf.com:19080
  aiPlatformApi: http://**************:9213/hd_api
  recommendStandardUrl: http://**************:9217
  aiYqyc: http://aiapp.yxgsyf.com:19080/yiqiyice/
  teacher-resource: https://yqyc-api-dev.yxgsyf.com/teacher-admin-api
  user-center-api:
    name: user-center-api
    url: https://yqyc-api-dev.yxgsyf.com/usercenter-api
xxl:
  job:
    admin:
      addresses: http://***************:10099/xxl-job-admin
    accessToken: AwJ&87sRi!iIRr1M
    executor:
      appname: hdm-admin-service   #此处需改为自己应用的名称
      address:
      ip: ***************   #此处需改为自己应用部署的服务器的ip(xxljob所在服务能访问到，内网外网ip都可以)
      port: 1000     #此处需改为自己应用提供给xxljob访问的端口，目前1000、1001已占用
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30