
package com.yixun.hdm.controller.admin;



import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.in.SelfCheckCorpDeleteIn;
import com.yixun.hdm.bean.in.SelfCheckCorpPageQueryIn;
import com.yixun.hdm.bean.in.SelfCheckCorpPushIn;
import com.yixun.hdm.bean.out.SelfCheckCorpImportProgressOut;
import com.yixun.hdm.entity.SelfCheckCorp;
import com.yixun.hdm.service.SelfCheckCorpImportService;
import com.yixun.hdm.service.SelfCheckCorpService;
import com.yixun.hdm.service.UserCenterService;
import com.yixun.hdm.utils.AdminUserHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 自查企业(SelfCheckCorp)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-26 15:54:38
 */
@Api(tags = "自查企业")
@RestController
@RequestMapping("/admin/self/checkCorp")
public class AdminSelfCheckCorpController {
    /**
     * 服务对象
     */
    @Autowired
    private SelfCheckCorpService selfCheckCorpService;

    @Autowired
    private SelfCheckCorpImportService selfCheckCorpImportService;

    @Autowired
    private UserCenterService userCenterService;



    /**
     * 分页查询所有数据
     *
     * @param queryIn 查询参数
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("pageList")
    public CommonResult<List<SelfCheckCorp>> pageList(SelfCheckCorpPageQueryIn queryIn) {
        return CommonResult.successData(selfCheckCorpService.pageList(queryIn));
    }


    /**
     * 新增数据
     *
     * @param selfCheckCorp 实体对象
     * @return 新增结果
     */
    @ApiOperation("添加企业")
    @PostMapping("insert")
    public CommonResult<Boolean> insert(@RequestBody SelfCheckCorp selfCheckCorp) {
        selfCheckCorpService.insert(selfCheckCorp);
        return CommonResult.successData(true);
    }

    /**
     * 删除数据
     *
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("delete")
    public CommonResult<Boolean> delete(@RequestBody @Valid SelfCheckCorpDeleteIn in) {
        return CommonResult.successData(this.selfCheckCorpService.remove(in));
    }

    @ApiOperation("清单推送")
    @PostMapping("push")
    public CommonResult<Boolean> push(@RequestBody SelfCheckCorpPushIn in) {
        return CommonResult.successData(this.selfCheckCorpService.push(in.getId(), in.getForce()));
    }

    @ApiOperation("清单推送全部推送")
    @PostMapping("pushAll")
    public CommonResult<Boolean> pushAll(@RequestParam Long projectId) {
        // 获取当前登录用户信息（在异步执行前获取）
        Long currentUserId = AdminUserHelper.getCurrentUserId();
        String userPhone = userCenterService.getAdminUser(currentUserId).getPhone();
        String projectName = userCenterService.getProject(projectId).getProjectName();

        // 异步执行推送
        this.selfCheckCorpService.pushAllAsync(projectId, userPhone, projectName);

        return CommonResult.successData(true);
    }

    /**
     * 企业批量导入
     *
     * @param file Excel文件
     * @param projectId 项目ID
     * @return 任务ID
     */
    @PostMapping("import")
    public CommonResult<String> importCorps(@RequestParam("file") MultipartFile file,
                                           @RequestParam("projectId") Long projectId) {
        String taskId = selfCheckCorpImportService.startImport(file, projectId);
        return CommonResult.successData(taskId);
    }

    /**
     * 查询导入进度
     *
     * @param taskId 任务ID
     * @return 导入进度
     */
    @GetMapping("import/progress/{taskId}")
    public CommonResult<SelfCheckCorpImportProgressOut> getImportProgress(@PathVariable String taskId) {
        SelfCheckCorpImportProgressOut progress = selfCheckCorpImportService.getImportProgress(taskId);
        return CommonResult.successData(progress);
    }


}

