package com.yixun.hdm.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.bean.in.VerificationIn;
import com.yixun.hdm.dao.VerificationMapper;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.OperationLogs;
import com.yixun.hdm.entity.Verification;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import com.yixun.hdm.entity.em.OperationType;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.service.OperationLogsService;
import com.yixun.hdm.service.VerificationService;
import com.yixun.hdm.utils.AdminUserHelper;
import com.yixun.hdm.utils.BeanUtils;
import com.yixun.hdm.utils.SnGeneratorUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Service
public class VerificationServiceImpl extends ServiceImpl<VerificationMapper, Verification>
    implements VerificationService{

    @Resource
    private OperationLogsService operationLogsService;

    @Resource
    private HiddenDangerService hiddenDangerService;

    @Override
    @Transactional
    public void submitVerification(HiddenDanger hiddenDanger, Verification verification, OperationLogs operationLogs) {
        saveOrUpdate(verification);
        if(operationLogs!=null){
            operationLogsService.save(operationLogs);
        }
        if (hiddenDanger!=null){
            hiddenDangerService.updateById(hiddenDanger);
        }
    }

    @Override
    public List<Verification> getByHiddenDanger(Long hiddenDangerId) {
        QueryWrapper<Verification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hidden_danger_id", hiddenDangerId);

        return list(queryWrapper);
    }

    @Override
    public Verification getLastVerification(Long hiddenDangerId) {
        QueryWrapper<Verification> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hidden_danger_id", hiddenDangerId);
        queryWrapper.orderByDesc("create_time");

        List<Verification> verificationList = list(queryWrapper);
        if (verificationList.isEmpty()){
            return null;
        }

        return verificationList.get(0);
    }

    @Override
    public List<Verification> getByHiddenDangerIds(String ids) {
        QueryWrapper<Verification> queryWrapper = new QueryWrapper<>();
        queryWrapper.inSql("hidden_danger_id", ids);

        return list(queryWrapper);
    }

    @Override
    public Verification getVerification(Long hiddenDangerId) {
        List<Verification> list = lambdaQuery().eq(Verification::getHiddenDangerId, hiddenDangerId).list();
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public void updateVerification(VerificationIn hiddenDangerIn) {
        HiddenDanger hiddenDanger = hiddenDangerService.getById(hiddenDangerIn.getHiddenDangerId());
        if (hiddenDanger==null){
            throw new DataErrorException("未找到该隐患");
        }
        if (!hiddenDanger.getStatus().equals(HiddenDangerStatus.已改善.name())){
            throw new DataErrorException("隐患状态错误");
        }
        Verification verification = getById(hiddenDangerIn.getVerificationId());
        if (verification==null){
            throw new RuntimeException("未找到该隐患验证信息");
        }
        BeanUtils.copyProperties(hiddenDangerIn, verification);
        verification.setPictures(JSON.toJSONString(hiddenDangerIn.getPictures()));

        if (hiddenDangerIn.getIsPass()){
            hiddenDanger.setStatus(HiddenDangerStatus.已改善.name());
        }else {
            hiddenDanger.setStatus(HiddenDangerStatus.待改善.name());
        }
        hiddenDanger.setUpdateTime(new Date());
        updateById(verification);
        hiddenDangerService.updateById(hiddenDanger);
    }
}




