package com.yixun.hdm.bean.in;

import com.yixun.hd.bean.in.HiddenDangerStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.List;

@Data
public class HiddenDangerListApiGetIn {

    @ApiModelProperty("id列表")
    private List<Long> idList;
    @ApiModelProperty(value="公司id")
    private Long corporationId;
    @ApiModelProperty(value="项目id")
    private Long projectId;
    @ApiModelProperty(value="调研轮次")
    private Integer turn;
    @ApiModelProperty(value="隐患状态")
    private HiddenDangerStatus hiddenDangerStatus;
    @ApiModelProperty(value="隐患门类；现场类；管理类")
    private String type;
    @ApiModelProperty(value="审核状态")
    private String auditStatus;
    @ApiModelProperty("排除推荐检查隐患")
    private Boolean eliminateRecommendHd = false;
    @ApiModelProperty(value="任务IDs")
    private Collection<Long> taskIds;

}
