package com.yixun.hdm.bean.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class HiddenDangerListGetIn {

    private List<Long> ids;
    @ApiModelProperty(value="项目id")
    private Long projectId;
    @ApiModelProperty(value="公司id")
    private Long corporationId;
    @ApiModelProperty(value="调研轮次")
    private Integer turn;
    @ApiModelProperty(value="任务ID")
    private Long taskId;
    @ApiModelProperty(value="任务类型 1-调研 2-辅助调查培训 3-回访 4-联合调研")
    private Integer taskType;
    @ApiModelProperty(value="任务时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskWorkDate;
    @ApiModelProperty(value="开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;
    @ApiModelProperty(value="结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;
    @ApiModelProperty(value="查报人")
    private Long operatorId;
    @ApiModelProperty(value="审查状态")
    private String auditStatus;
    @ApiModelProperty(value="隐患级别")
    private String level;
    @ApiModelProperty(value="隐患门类")
    private String type;
    @ApiModelProperty(value="专项类别")
    private String specialCategory;
    @ApiModelProperty(value="危害因素分类")
    private Object riskFactorTypeDetail;
    @ApiModelProperty(value="危害因素分类详情-人机料法环")
    private Object riskFactorTypeDetailAlias;
    @ApiModelProperty(value="可能导致后果")
    private String possibleConsequence;
    @ApiModelProperty(value="是否发布")
    private Boolean isRelease;
    @ApiModelProperty(value="是否回访发现")
    private Boolean isFindByCallback;
    @ApiModelProperty(value="隐患状态")
    private HiddenDangerStatus hiddenDangerStatus;
    @ApiModelProperty(value="是否需老师修改")
    private Boolean isNeedEdit;
    @ApiModelProperty(value="是否错误提交")
    private Boolean isWrongSubmit;

    @ApiModelProperty(value="填报质量")
    private String quality;

    @ApiModelProperty(value="隐患质量分; 0:等于0；1：大于0；2：小于0")
    private Integer qualityScore;

    private long pageNum = 1;
    private long pageSize = 20;
}
