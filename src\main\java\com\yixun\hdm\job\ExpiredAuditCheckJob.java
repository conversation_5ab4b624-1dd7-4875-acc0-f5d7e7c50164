package com.yixun.hdm.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.HdAudit;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.service.HdAuditService;
import com.yixun.hdm.service.MessageService;
import com.yixun.hdm.service.UserCenterService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
public class ExpiredAuditCheckJob {

    @Resource
    private HdAuditService hdAuditService;

    @Resource
    private MessageService messageService;

    @Resource
    private UserCenterService userCenterService;

    @XxlJob("expiredAuditCheckHandler")
    public CommonResult<Void> expiredAuditCheck() {

        List<HdAudit> hdAuditList = hdAuditService.getExpiredAudit(new Date());
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        for (HdAudit hdAudit : hdAuditList){
            StaffUserDto submitter = userCenterService.getAdminUser(hdAudit.getAuditSubmitterId());
            String message = hdAudit.getAuditSubmitter()+"老师，" + hdAudit.getProjectName() + "-" + hdAudit.getCorpName()
                    + " 已超过审核期限，审查人：" + hdAudit.getAuditor() + "  " + date;
            messageService.sendDing(message, submitter.getPhone());
        }

        return CommonResult.successResult(null);
    }

}
