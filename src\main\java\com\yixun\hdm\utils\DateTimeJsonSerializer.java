package com.yixun.hdm.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateTimeJsonSerializer extends JsonSerializer<Date> {

    @Override
    public void serialize(Date value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

        String text = (value == null ? null : new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(value));
        if (text != null) {
            jsonGenerator.writeString(text);
        }
    }
}