package com.yixun.hdm.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.bean.common.CommonPage;
import com.yixun.hdm.bean.in.SelfCheckCorpDeleteIn;
import com.yixun.hdm.bean.in.SelfCheckCorpPageQueryIn;
import com.yixun.hdm.dao.SelfCheckCorpMapper;
import com.yixun.hdm.entity.SelfCheckCorp;
import com.yixun.hdm.entity.SelfCheckDetail;
import com.yixun.hdm.entity.SelfCheckRecord;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.MessageService;
import com.yixun.hdm.service.SelfCheckCorpService;
import com.yixun.hdm.service.SelfCheckDetailService;
import com.yixun.hdm.service.SelfCheckRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 自查企业(SelfCheckCorp)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-26 15:54:42
 */
@Slf4j
@Service("selfCheckCorpService")
public class SelfCheckCorpServiceImpl extends ServiceImpl<SelfCheckCorpMapper, SelfCheckCorp> implements SelfCheckCorpService {

    @Autowired
    private SelfCheckRecordService selfCheckRecordService;

    @Autowired
    private SelfCheckDetailService selfCheckDetailService;

    @Autowired
    private MessageService messageService;

    @Resource(name = "customRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    private static final String PUSH_ALL_LOCK_KEY_PREFIX = "push_all_lock:";
    private static final int LOCK_EXPIRE_SECONDS = 300 * 6; // 5分钟锁过期时间

    @Override
    @Transactional
    public Boolean push(Long id, boolean isForce) {
        SelfCheckCorp byId = getById(id);
        if (byId == null) {
            throw new RuntimeException("自查企业不存在");
        }
        selfCheckRecordService.createRecord(byId.getProjectId(), byId.getProjectName(), byId.getCorpId(), byId.getCorpName(), new Date(), isForce);
        byId.setPushStatus(1);
        byId.setLastPushTime(new Date());
        updateById(byId);
        return true;
    }

    @Override
    @Transactional
    public Boolean remove(SelfCheckCorpDeleteIn in) {
        SelfCheckCorp byId = getById(in.getId());
        if (byId == null) {
            throw new RuntimeException("自查企业不存在");
        }
        removeById(in.getId());
        if (in.getMode() == 2) {
            List<SelfCheckRecord> list = selfCheckRecordService.lambdaQuery().eq(SelfCheckRecord::getProjectId, byId.getProjectId())
                    .eq(SelfCheckRecord::getCorpId, byId.getCorpId())
                    .list();
            List<Long> collect = list.stream().map(SelfCheckRecord::getId).collect(Collectors.toList());
            selfCheckRecordService.removeByIds(collect);
            selfCheckDetailService.lambdaUpdate().in(SelfCheckDetail::getCheckRecordId, collect).remove();
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean pushAll(Long projectId) {
        List<SelfCheckCorp> list = lambdaQuery().eq(SelfCheckCorp::getProjectId, projectId).list();
        for (SelfCheckCorp selfCheckCorp : list) {
            try {
                push(selfCheckCorp.getId(), true);
            } catch (DataErrorException e) {
                log.warn(selfCheckCorp.getCorpName() + e.getLocalizedMessage());
            }
        }
        return true;
    }

    @Override
    @Async
    public void pushAllAsync(SelfCheckCorpPageQueryIn queryIn, String userPhone, String projectName) {
        String lockKey = PUSH_ALL_LOCK_KEY_PREFIX + queryIn.getProjectId();

        // 尝试获取分布式锁
        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", LOCK_EXPIRE_SECONDS, TimeUnit.SECONDS);

        if (!Boolean.TRUE.equals(lockAcquired)) {
            log.warn("项目{}正在推送中，跳过本次推送请求", queryIn.getProjectId());
            return;
        }

        try {
            log.info("开始异步推送项目{}的筛选企业", queryIn.getProjectId());

            // 获取筛选出的需要推送的企业（不分页）
            List<SelfCheckCorp> list = getFilteredCorpList(queryIn);
            int totalCount = list.size();
            int successCount = 0;
            int failCount = 0;

            log.info("根据筛选条件，共找到{}家企业需要推送", totalCount);

            // 执行推送
            for (SelfCheckCorp selfCheckCorp : list) {
                try {
                    SpringUtil.getBean(SelfCheckCorpService.class).push(selfCheckCorp.getId(), true);
                    successCount++;
                    log.info("企业{}推送成功", selfCheckCorp.getCorpName());
                } catch (Exception e) {
                    failCount++;
                    log.warn("企业{}推送失败: {}", selfCheckCorp.getCorpName(), e.getMessage());
                }
            }

            // 推送完成后发送钉钉通知
            String message = String.format(
                "标题：智查自纠隐患清单推送\n项目：%s\n需推送：%d家\n已推送：%d家\n推送失败：%d家",
                projectName, totalCount, successCount, failCount
            );

            try {
                messageService.sendDing(message, userPhone);
                log.info("钉钉通知发送成功，接收人: {}", userPhone);
            } catch (Exception e) {
                log.error("钉钉通知发送失败: {}", e.getMessage(), e);
            }

            log.info("项目{}推送完成，总计{}家，成功{}家，失败{}家", queryIn.getProjectId(), totalCount, successCount, failCount);

        } finally {
            // 释放分布式锁
            try {
                redisTemplate.delete(lockKey);
                log.info("项目{}推送锁已释放", queryIn.getProjectId());
            } catch (Exception e) {
                log.error("释放项目{}推送锁失败: {}", queryIn.getProjectId(), e.getMessage(), e);
            }
        }
    }

    @Override
    public void insert(SelfCheckCorp selfCheckCorp) {
        Long count = lambdaQuery().eq(SelfCheckCorp::getProjectId, selfCheckCorp.getProjectId())
                .eq(SelfCheckCorp::getCorpId, selfCheckCorp.getCorpId())
                .count();
        if (count != 0) {
            throw new DataErrorException("自查企业已存在");
        }
        save(selfCheckCorp);
    }

    @Override
    public Page<SelfCheckCorp> pageList(SelfCheckCorpPageQueryIn queryIn) {
        Page<SelfCheckCorp> page = new Page<>(queryIn.getPageNum(), queryIn.getPageSize());

        // 判断是否需要通过self_check_record表进行关联查询
        boolean hasTimeRange = queryIn.getStartDate() != null && queryIn.getEndDate() != null;
        boolean hasPushStatus = queryIn.getPushStatus() != null;

        if (hasTimeRange && hasPushStatus) {
            // 需要通过self_check_record表进行关联查询
            return pageListWithRecordFilter(queryIn, page);
        } else {
            // 保持原有逻辑，直接查询self_check_corp表
            return pageListDirect(queryIn, page);
        }
    }

    /**
     * 直接查询self_check_corp表
     */
    private Page<SelfCheckCorp> pageListDirect(SelfCheckCorpPageQueryIn queryIn, Page<SelfCheckCorp> page) {
        lambdaQuery().like(!StrUtil.isBlank(queryIn.getCorpName()), SelfCheckCorp::getCorpName, queryIn.getCorpName())
                .eq(queryIn.getCorpId() != null, SelfCheckCorp::getCorpId, queryIn.getCorpId())
                .eq(queryIn.getPushStatus() != null, SelfCheckCorp::getPushStatus, queryIn.getPushStatus())
                .eq(queryIn.getProjectId() != null, SelfCheckCorp::getProjectId, queryIn.getProjectId())
                .page(page);

        fillAdditionalInfo(page);
        return page;
    }

    /**
     * 通过self_check_record表进行关联查询
     */
    private Page<SelfCheckCorp> pageListWithRecordFilter(SelfCheckCorpPageQueryIn queryIn, Page<SelfCheckCorp> page) {
        // 先查询符合时间范围的推送记录
        List<SelfCheckRecord> records = selfCheckRecordService.lambdaQuery()
                .eq(queryIn.getProjectId() != null, SelfCheckRecord::getProjectId, queryIn.getProjectId())
                .ge(SelfCheckRecord::getCreateTime, queryIn.getStartDate())
                .le(SelfCheckRecord::getCreateTime, queryIn.getEndDate())
                .list();

        // 提取有推送记录的企业ID列表
        List<Long> corpIdsWithRecord = records.stream()
                .map(SelfCheckRecord::getCorpId)
                .distinct()
                .collect(Collectors.toList());

        if (queryIn.getPushStatus().equals(0)) {
            // 未推送：查询在时间范围内没有推送记录的企业
            if (corpIdsWithRecord.isEmpty()) {
                // 没有任何推送记录，查询所有企业
                lambdaQuery().like(!StrUtil.isBlank(queryIn.getCorpName()), SelfCheckCorp::getCorpName, queryIn.getCorpName())
                        .eq(queryIn.getCorpId() != null, SelfCheckCorp::getCorpId, queryIn.getCorpId())
                        .eq(queryIn.getProjectId() != null, SelfCheckCorp::getProjectId, queryIn.getProjectId())
                        .page(page);
            } else {
                // 排除有推送记录的企业
                lambdaQuery().like(!StrUtil.isBlank(queryIn.getCorpName()), SelfCheckCorp::getCorpName, queryIn.getCorpName())
                        .eq(queryIn.getCorpId() != null, SelfCheckCorp::getCorpId, queryIn.getCorpId())
                        .eq(queryIn.getProjectId() != null, SelfCheckCorp::getProjectId, queryIn.getProjectId())
                        .notIn(SelfCheckCorp::getCorpId, corpIdsWithRecord)
                        .page(page);
            }
        } else {
            // 已推送：查询在时间范围内有推送记录的企业
            if (corpIdsWithRecord.isEmpty()) {
                // 没有推送记录，返回空结果
                page.setRecords(new ArrayList<>());
                page.setTotal(0);
                return page;
            } else {
                // 只查询有推送记录的企业
                lambdaQuery().like(!StrUtil.isBlank(queryIn.getCorpName()), SelfCheckCorp::getCorpName, queryIn.getCorpName())
                        .eq(queryIn.getCorpId() != null, SelfCheckCorp::getCorpId, queryIn.getCorpId())
                        .eq(queryIn.getProjectId() != null, SelfCheckCorp::getProjectId, queryIn.getProjectId())
                        .in(SelfCheckCorp::getCorpId, corpIdsWithRecord)
                        .page(page);
            }
        }

        fillAdditionalInfo(page);
        return page;
    }

    /**
     * 获取筛选出的企业列表（不分页）
     */
    private List<SelfCheckCorp> getFilteredCorpList(SelfCheckCorpPageQueryIn queryIn) {
        // 判断是否需要通过self_check_record表进行关联查询
        boolean hasTimeRange = queryIn.getStartDate() != null && queryIn.getEndDate() != null;
        boolean hasPushStatus = queryIn.getPushStatus() != null;

        if (hasTimeRange && hasPushStatus) {
            // 需要通过self_check_record表进行关联查询
            return getFilteredCorpListWithRecordFilter(queryIn);
        } else {
            // 保持原有逻辑，直接查询self_check_corp表
            return getFilteredCorpListDirect(queryIn);
        }
    }

    /**
     * 直接查询self_check_corp表（不分页）
     */
    private List<SelfCheckCorp> getFilteredCorpListDirect(SelfCheckCorpPageQueryIn queryIn) {
        return lambdaQuery().like(!StrUtil.isBlank(queryIn.getCorpName()), SelfCheckCorp::getCorpName, queryIn.getCorpName())
                .eq(queryIn.getCorpId() != null, SelfCheckCorp::getCorpId, queryIn.getCorpId())
                .eq(queryIn.getPushStatus() != null, SelfCheckCorp::getPushStatus, queryIn.getPushStatus())
                .eq(queryIn.getProjectId() != null, SelfCheckCorp::getProjectId, queryIn.getProjectId())
                .list();
    }

    /**
     * 通过self_check_record表进行关联查询（不分页）
     */
    private List<SelfCheckCorp> getFilteredCorpListWithRecordFilter(SelfCheckCorpPageQueryIn queryIn) {
        // 先查询符合时间范围的推送记录
        List<SelfCheckRecord> records = selfCheckRecordService.lambdaQuery()
                .eq(queryIn.getProjectId() != null, SelfCheckRecord::getProjectId, queryIn.getProjectId())
                .ge(SelfCheckRecord::getCreateTime, queryIn.getStartDate())
                .le(SelfCheckRecord::getCreateTime, queryIn.getEndDate())
                .list();

        // 提取有推送记录的企业ID列表
        List<Long> corpIdsWithRecord = records.stream()
                .map(SelfCheckRecord::getCorpId)
                .distinct()
                .collect(Collectors.toList());

        if (queryIn.getPushStatus().equals(0)) {
            // 未推送：查询在时间范围内没有推送记录的企业
            if (corpIdsWithRecord.isEmpty()) {
                // 没有任何推送记录，查询所有企业
                return lambdaQuery().like(!StrUtil.isBlank(queryIn.getCorpName()), SelfCheckCorp::getCorpName, queryIn.getCorpName())
                        .eq(queryIn.getCorpId() != null, SelfCheckCorp::getCorpId, queryIn.getCorpId())
                        .eq(queryIn.getProjectId() != null, SelfCheckCorp::getProjectId, queryIn.getProjectId())
                        .list();
            } else {
                // 排除有推送记录的企业
                return lambdaQuery().like(!StrUtil.isBlank(queryIn.getCorpName()), SelfCheckCorp::getCorpName, queryIn.getCorpName())
                        .eq(queryIn.getCorpId() != null, SelfCheckCorp::getCorpId, queryIn.getCorpId())
                        .eq(queryIn.getProjectId() != null, SelfCheckCorp::getProjectId, queryIn.getProjectId())
                        .notIn(SelfCheckCorp::getCorpId, corpIdsWithRecord)
                        .list();
            }
        } else {
            // 已推送：查询在时间范围内有推送记录的企业
            if (corpIdsWithRecord.isEmpty()) {
                // 没有推送记录，返回空结果
                return new ArrayList<>();
            } else {
                // 只查询有推送记录的企业
                return lambdaQuery().like(!StrUtil.isBlank(queryIn.getCorpName()), SelfCheckCorp::getCorpName, queryIn.getCorpName())
                        .eq(queryIn.getCorpId() != null, SelfCheckCorp::getCorpId, queryIn.getCorpId())
                        .eq(queryIn.getProjectId() != null, SelfCheckCorp::getProjectId, queryIn.getProjectId())
                        .in(SelfCheckCorp::getCorpId, corpIdsWithRecord)
                        .list();
            }
        }
    }

    /**
     * 填充附加信息
     */
    private void fillAdditionalInfo(Page<SelfCheckCorp> page) {
        page.getRecords().forEach(item -> {
            SelfCheckRecord record = selfCheckRecordService.getRecord(item.getProjectId(), item.getCorpId(), new Date());
            if(record != null){
                item.setLastCheckTime(record.getLastCheckTime());
            }
            Long count = selfCheckRecordService.lambdaQuery().eq(SelfCheckRecord::getProjectId, item.getProjectId())
                    .eq(SelfCheckRecord::getCorpId, item.getCorpId())
                    .isNotNull(SelfCheckRecord::getLastCheckTime).count();
            item.setHasChecked(count > 0);
        });
    }

	@Override
	public Long countByProjectId(String projectId) {
		return this.lambdaQuery().eq(SelfCheckCorp::getProjectId, projectId).count();
	}

}

