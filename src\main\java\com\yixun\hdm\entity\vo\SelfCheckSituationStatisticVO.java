package com.yixun.hdm.entity.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 检查情况
 */
@Data
public class SelfCheckSituationStatisticVO {

	/**
	 * 共推荐检查内容
	 */
	private Long totalRecommendSelfCheck;

	/**
	 * 共查出隐患数
	 */
	private Long totalHiddenDanger;

	/**
	 * 隐患查出率
	 */
	private BigDecimal hiddenDangerDetectionRate;

	/**
	 * 平均查出隐患数
	 */
	private BigDecimal averageHiddenDangersNumber;

}
