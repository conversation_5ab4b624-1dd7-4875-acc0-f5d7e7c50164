package com.yixun.hdm.job;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.entity.HdAudit;
import com.yixun.hdm.entity.HiddenDanger;
import com.yixun.hdm.entity.dto.StaffUserDto;
import com.yixun.hdm.entity.em.AuditStatus;
import com.yixun.hdm.entity.em.HiddenDangerStatus;
import com.yixun.hdm.service.HiddenDangerService;
import com.yixun.hdm.service.MessageService;
import com.yixun.hdm.service.UserCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class ExpiredHdImproveJob {

    @Resource
    private UserCenterService userCenterService;

    @Resource
    private MessageService messageService;

    @Resource
    private HiddenDangerService hiddenDangerService;

    @XxlJob("expiredHdImprove")
    public CommonResult<Void> expiredHdImprove() {
        LambdaQueryWrapper<HiddenDanger> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HiddenDanger::getLevel, "重大隐患");
        queryWrapper.eq(HiddenDanger::getAuditStatus, AuditStatus.Released.name());
        queryWrapper.eq(HiddenDanger::getStatus, HiddenDangerStatus.待改善.name());
        List<HiddenDanger> list = hiddenDangerService.list(queryWrapper);
        for (HiddenDanger hiddenDanger : list) {
            long between = DateUtil.between(DateUtil.date(), hiddenDanger.getCreateTime(), DateUnit.DAY, true);
            long overDay = between - hiddenDanger.getRectifyDeadline();
            if (overDay == 1 || overDay == 3 || overDay == 7) {
                //超期
                StaffUserDto adminUser = userCenterService.getAdminUser(hiddenDanger.getOperatorId());
                String message = String.format("%s老师，%s项目-%s企业，重大隐患已超过整改期限%d天，但尚未整改，请及时与企业沟通，询问整改情况，督促整改。"
                        , adminUser.getRealName(), hiddenDanger.getProjectName(), hiddenDanger.getCorpName(), overDay);
                messageService.sendDing(message, adminUser.getPhone());
            }
        }
        return CommonResult.successResult(null);
    }

}
