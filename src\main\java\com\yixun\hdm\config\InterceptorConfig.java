package com.yixun.hdm.config;

import com.yixun.hdm.interceptor.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private ApiInterceptor apiInterceptor;

    @Resource
    private AdminInterceptor adminInterceptor;

    @Resource
    private UserInterceptor userInterceptor;

    //拦截器配置
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        registry.addInterceptor(new ParametersInterceptor()).addPathPatterns("/**");
//        if (!env.equals("dev")){
            registry.addInterceptor(apiInterceptor).addPathPatterns("/api/**");
            registry.addInterceptor(adminInterceptor).addPathPatterns("/admin/**");
            registry.addInterceptor(userInterceptor).addPathPatterns("/gov/**");
//        }
    }
}
