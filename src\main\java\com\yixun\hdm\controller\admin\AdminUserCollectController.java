package com.yixun.hdm.controller.admin;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yixun.hdm.bean.common.CommonResult;
import com.yixun.hdm.bean.in.UserCollectIn;
import com.yixun.hdm.entity.UserCollect;
import com.yixun.hdm.exception.DataErrorException;
import com.yixun.hdm.service.UserCollectService;
import com.yixun.hdm.utils.AdminUserHelper;
import com.yixun.hdm.utils.BeanUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

@Slf4j
@Api(tags = "admin 用户收藏")
@RestController
@RequestMapping("/admin/user/collect")
public class AdminUserCollectController {
    /**
     * 服务对象
     */
    @Autowired
    private UserCollectService userCollectService;


    /**
     * 添加收藏
     *
     * @return 新增结果
     */
    @PostMapping("add")
    public CommonResult<Object> insert(@RequestBody @Validated UserCollectIn in) {
        LambdaQueryWrapper<UserCollect> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserCollect::getUserId, AdminUserHelper.getCurrentUserId());
        wrapper.eq(UserCollect::getContentType, in.getContentType());
        wrapper.eq(UserCollect::getContentId, in.getContentId());
        long count = userCollectService.count(wrapper);
        if(count != 0){
            throw new DataErrorException("用户已收藏");
        }
        UserCollect userCollect = BeanUtils.copyNew(in, UserCollect.class);
        userCollect.setUserId(AdminUserHelper.getCurrentUserId());
        userCollectService.save(userCollect);
        return CommonResult.successData("");
    }

    /**
     * 删除收藏
     *
     * @return 新增结果
     */
    @PostMapping("remove")
    public CommonResult<Object> remove(@RequestBody @Validated UserCollectIn in) {
        LambdaQueryWrapper<UserCollect> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserCollect::getUserId, AdminUserHelper.getCurrentUserId());
        wrapper.eq(UserCollect::getContentType, in.getContentType());
        wrapper.eq(UserCollect::getContentId, in.getContentId());
        userCollectService.remove(wrapper);
        return CommonResult.successData("");
    }

}
