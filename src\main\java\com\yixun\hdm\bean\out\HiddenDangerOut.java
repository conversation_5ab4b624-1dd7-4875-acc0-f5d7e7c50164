package com.yixun.hdm.bean.out;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yixun.hdm.entity.HiddenDangerSuggestion;
import com.yixun.hdm.utils.DateJsonSerializer;
import com.yixun.hdm.utils.DateTimeJsonSerializer;
import com.yixun.hdm.utils.LongJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class HiddenDangerOut {

    @JsonSerialize(using = LongJsonSerializer.class)
    private Long id;
    @JsonSerialize(using = DateTimeJsonSerializer.class)
    private Date createTime;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long operatorId;
    private String operator;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long projectId;
    @ApiModelProperty(value="调研轮次")
    private Integer turn;
    @ApiModelProperty(value="任务id")
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long taskId;
    @ApiModelProperty(value="任务类型 1-调研 2-辅助调查培训 3-回访 4-联合调研")
    private Integer taskType;
    @ApiModelProperty(value="任务时间")
    @JsonSerialize(using = DateJsonSerializer.class)
    private Date taskWorkDate;
    private String projectName;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long corporationId;
    private String corpName;
    private String status;
    private List pictures;
    private List gifPictures;
    private String specialCategory;
    private List specialCategoryDetail;
    private String description;
    private String type;
    private String level;
    private String tierOneBusiness;
    private String remark;
    private List files;
    private List standard;
    private List possibleConsequence;
    private String riskFactorType;
    private List riskFactorTypeDetail;
    @ApiModelProperty("危害因素分类详情-人机料法环")
    private List riskFactorTypeDetailAlias;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long riskPointId;
    private String riskPointName;
    private String manageSource;
    private String manageSourceContent;
    private String rectificationSuggestion;
    private Integer rectifyDeadline;
    private Boolean isFindByCallback;
    private Boolean isWrongSubmit;
    private String arrangedAuditor;
    private String auditor;
    @JsonSerialize(using = LongJsonSerializer.class)
    private Long auditorId;
    private String auditStatus;
    private String auditComment;
    private Date auditTime;
    private Boolean isNeedEdit;

    /**
     * 隐患区域
     */
    @ApiModelProperty("隐患区域")
    private String hdRegion;

    /**
     * 设备设施/物料
     */
    @ApiModelProperty("设备设施/物料")
    private String equipment;

    /**
     * 作业活动
     */
    @ApiModelProperty("作业活动")
    private String jobActivity;

    /**
     * 岗位/工种
     */
    @ApiModelProperty("岗位/工种")
    private String workCategory;

    @ApiModelProperty("是否已工作移交")
    private Boolean isWorkHandover;

    @ApiModelProperty("建议分项录入列表")
    private List<HiddenDangerSuggestion> suggestionList = new ArrayList<>();

    public void setStandard(String standard) {
        this.standard = JSON.parseObject(standard, List.class);
    }

    public void setRiskFactorTypeDetail(String riskFactorTypeDetail) {
        this.riskFactorTypeDetail = JSON.parseObject(riskFactorTypeDetail, List.class);
    }

    public void setRiskFactorTypeDetailAlias(String riskFactorTypeDetailAlias) {
        this.riskFactorTypeDetailAlias = JSON.parseObject(riskFactorTypeDetailAlias, List.class);;
    }

    public void setSpecialCategoryDetail(String specialCategoryDetail) {
        this.specialCategoryDetail = JSON.parseObject(specialCategoryDetail, List.class);
    }

}
