package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.dao.HiddenDangerTempMapper;
import com.yixun.hdm.entity.HiddenDangerTemp;
import com.yixun.hdm.entity.OperationLogsTemp;
import com.yixun.hdm.service.HiddenDangerTempService;
import com.yixun.hdm.service.OperationLogsTempService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *
 */
@Service
public class HiddenDangerTempServiceImpl extends ServiceImpl<HiddenDangerTempMapper, HiddenDangerTemp>
    implements HiddenDangerTempService{

    @Resource
    private OperationLogsTempService operationLogsTempService;

    @Override
    public void submitHiddenDanger(HiddenDangerTemp hiddenDangerTemp, OperationLogsTemp operationLogsTemp) {
        save(hiddenDangerTemp);
        operationLogsTempService.save(operationLogsTemp);
    }
}




