package com.yixun.hdm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yixun.hdm.dao.RectificationTempMapper;
import com.yixun.hdm.entity.OperationLogsTemp;
import com.yixun.hdm.entity.RectificationTemp;
import com.yixun.hdm.service.OperationLogsTempService;
import com.yixun.hdm.service.RectificationTempService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 隐患改善服务
 */
@Service
public class RectificationTempServiceImpl extends ServiceImpl<RectificationTempMapper, RectificationTemp>
    implements RectificationTempService{

    @Resource
    private OperationLogsTempService operationLogsTempService;

    @Override
    public void submitRectification(RectificationTemp rectificationTemp, OperationLogsTemp rectificationLogsTemp) {
        save(rectificationTemp);
        operationLogsTempService.save(rectificationLogsTemp);
    }
}




